<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'

const router = useRouter()
const callAxios = useAxios()
const Swal = useSwal()

const totalRecords = ref(0)
const currentPage = ref(1)
const rowPerPage = ref(20)

const breadcrumbItems = [
  {
    title: 'รายงาน',
    disable: false,
    to: '/apps/report/menu'
  },
  {
    title: 'รายงานสรุปผลการประเมินความพึงพอใจ',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const listFilter = reactive([
  {
    name: 'searchDate',
    type: 'dateRange',
    label: 'วันที่ (เริ่มต้น-สิ้นสุด)',
    required: true,
    default: '',
    title: '',
    value: '',
    items: [],
    placeholder: 'เลือกวันที่เริ่มต้น-สิ้นสุด'
  },
  {
    name: 'searchGoverment',
    type: 'select',
    label: 'ศูนย์ราชการสะดวก',
    default: '',
    title: 'name',
    value: 'govermentId',
    items: [{ name: 'ทั้งหมด', govermentId: '' }],
    placeholder: 'เลือกศูนย์ราชการสะดวก'
  },
  {
    name: 'searchAgenciresMainName',
    type: 'select',
    label: 'ชื่อส่วนราชการ (เจ้าภาพหลัก)',
    default: '',
    title: 'name',
    value: 'id',
    items: [{ name: 'ทั้งหมด', id: '' }]
  },
  {
    name: 'searchProvince',
    type: 'select',
    label: 'จังหวัด',
    default: '',
    title: 'name',
    value: 'id',
    items: [{ name: 'ทั้งหมด', id: '' }]
  },
  {
    name: 'searchAgenciresName',
    type: 'select',
    label: 'หน่วยงาน',
    default: '',
    title: 'name',
    value: 'id',
    items: [{ name: 'ทั้งหมด', id: '' }]
  },
  {
    name: 'searchSubcommittee',
    type: 'select',
    label: 'คณะอนุกรรมการ',
    default: '',
    title: 'name',
    value: 'id',
    items: [{ name: 'ทั้งหมด', id: '' }]
  }
])

const filter = reactive({
  searchDate: '',
  searchGoverment: '',
  searchAgenciresMainName: '',
  searchProvince: '',
  searchAgenciresName: '',
  searchSubcommittee: ''
})

const listFields = ref([
  {
    field: 'no',
    header: 'ลำดับที่',
    sortable: false,
    style: { textAlign: 'center' }
  },
  {
    field: 'govermentName',
    header: 'ศูนย์ราชการสะดวก',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'provinceName',
    header: 'จังหวัด',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'totalAssessmentsAnswer',
    header: 'จำนวนผู้ตอบแบบสอบถามรวม (ครั้ง)',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'assessmentsAnswer',
    header: 'แบบสำรวจความคิดเห็น(ครั้ง)',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'satisfaction',
    header: 'แบบประเมินความพึงพอใจ(ครั้ง)',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'score',
    header: 'ภาพรวมการให้บริการ (คะแนน)',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'options',
    header: 'การจัดการ',
    sortable: false,
    style: { textAlign: 'center' }
  }
])

const ExportExcel_URL = ref('')
const ListItem = ref([])

const adjustYear = dateString => {
  const [year, month, day] = dateString.split('-')

  return `${Number(year) + 543}-${month}-${day}`
}

const downloadExcel = () => {
  const baseUrlImg = localStorage.getItem('baseUrlImg')
  if (!baseUrlImg || !ExportExcel_URL.value) {
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการดาวน์โหลดไฟล์')

    return
  }
  window.open(baseUrlImg + ExportExcel_URL.value, '_blank')
}

const initialize = ref(true)

const GetList = async (isExportExcel = false) => {
  try {
    if (!filter.searchDate) {
      Swal.validateText('กรุณากรอกข้อมูลวันที่')

      return
    }

    const params = new URLSearchParams({
      Page: currentPage.value.toString(),
      PageSize: rowPerPage.value.toString()
    })

    // Append filter fields dynamically
    if (filter.searchDate) {
      const [DateStart, DateEnd] = filter.searchDate
      if (DateStart && DateEnd) {
        params.append('StartDate', adjustYear(DateStart))
        params.append('EndDate', adjustYear(DateEnd))
      }
    }
    if (filter.searchGoverment) params.append('Goverment', filter.searchGoverment)

    if (filter.searchAgenciresMainName) params.append('OrgGroup', filter.searchAgenciresMainName)

    if (filter.searchProvince) params.append('Province', filter.searchProvince)

    if (filter.searchAgenciresName) params.append('OrgStructure', filter.searchAgenciresName)

    if (filter.searchSubcommittee) params.append('Subcommittee', filter.searchSubcommittee)

    // Define the API URL based on export condition
    const apiUrl = isExportExcel
      ? `/report/GetTransactionAssessmentsAnswerExcel?${params.toString()}`
      : `/report/GetTransactionAssessmentsAnswer?${params.toString()}`

    const response = await callAxios.RequestGet(apiUrl)

    if (response.status === 200) {
      if (isExportExcel) {
        if (response.data.url) {
          ExportExcel_URL.value = response.data.url
          downloadExcel()
        } else {
          Swal.AddConditionFailText('ไม่สามารถส่งออกไฟล์ Excel ได้')
        }
      } else {
        ListItem.value = response.data.response || []
        totalRecords.value = response.data.count || 0
      }
    } else {
      Swal.AddConditionFailText('เกิดข้อผิดพลาดในการดึงข้อมูลรายงาน')
    }
  } catch (error) {
    console.error('Error fetching report:', error.response || error)
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการดึงข้อมูลรายงาน')
  }
}

const getDropdownItems = async () => {
  try {
    // Fetch data for ชื่อส่วนราชการ (เจ้าภาพหลัก)
    let response = await callAxios.RequestGet('/OtherMaster/DepartmentParrentLists?flag=true')
    if (response.status === 200) {
      const DepartmentParrent = listFilter.find(filter => filter.name === 'searchAgenciresMainName')

      if (DepartmentParrent) {
        DepartmentParrent.items = [{ name: 'ทั้งหมด', id: '' }, ...response.data.response]
      }
    }

    // Fetch data for Province
    response = await callAxios.RequestGet('/OtherMaster/ProvinceLists')
    if (response.status === 200) {
      const Province = listFilter.find(filter => filter.name === 'searchProvince')

      if (Province) {
        Province.items = [{ name: 'ทั้งหมด', id: '' }, ...response.data.response]
      }
    }

    // Fetch data for Goverment
    response = await callAxios.RequestGet('/TransactionRegister/GetDropDownGoverment')
    if (response.status === 200) {
      const Subcommittee = listFilter.find(filter => filter.name === 'searchGoverment')

      if (Subcommittee) {
        Subcommittee.items = [{ name: 'ทั้งหมด', govermentId: '' }, ...response.data.response]
      }
    }
  } catch (error) {
    console.error('Error fetching dropdown items:', error)
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการโหลดข้อมูล')
  }
}

// Fetch กรม/หน่วยงาน
const fetchDepartmentChildLists = async (departmentParrentId: string) => {
  if (!departmentParrentId) return

  try {
    const response = await callAxios.RequestGetById(
      '/OtherMaster/DepartmentChildLists',
      `?OrgGroupId=${departmentParrentId}&Id=${filter.searchAgenciresName}&flag=true`
    )

    if (response.status === 200) {
      const DepartmentChildLists = listFilter.find(filter => filter.name === 'searchAgenciresName')

      if (DepartmentChildLists) {
        DepartmentChildLists.items = [{ name: 'ทั้งหมด', id: '' }, ...response.data.response]
      }
    }
  } catch (error) {
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการโหลดข้อมูลอำเภอ')
  }
}

const fetchSubcommitteeLists = async (ProvinceId: string) => {
  if (!ProvinceId) return

  try {
    const response = await callAxios.RequestGetById(
      '/OtherMaster/SubcommitteeProviceLists',
      `?Province=${ProvinceId}`
    )

    if (response.status === 200) {
      const Submmittee = listFilter.find(filter => filter.name === 'searchSubcommittee')

      if (Submmittee) {
        Submmittee.items = [{ name: 'ทั้งหมด', id: '' }, ...response.data.response]
      }
    }
  } catch (error) {
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการโหลดข้อมูลอนุกรรมการ')
  }
}

const onPageChange = (event: { page: number; rows: number }) => {
  currentPage.value = event.page + 1
  rowPerPage.value = event.rows
  GetList()
}

const openViewPage = (data: string) => {
  router.push({
    name: 'apps-Report-SatisfactionEvaluationSummary-SatisfactionsReportView',
    query: {
      id: data,
      startDate: adjustYear(filter.searchDate[0]),
      endDate: adjustYear(filter.searchDate[1])
    }
  })
}

watch(
  () => filter.searchAgenciresMainName,
  async newDepartmentParrent => {
    filter.searchAgenciresName = ''
    if (newDepartmentParrent !== '') {
      fetchDepartmentChildLists(newDepartmentParrent)
    } else {
      const DepartmentChildLists = listFilter.find(filter => filter.name === 'searchAgenciresName')

      if (DepartmentChildLists) DepartmentChildLists.items = [{ name: 'ทั้งหมด', id: '' }]
    }
  }
)

watch(
  () => filter.searchProvince,
  async newProvince => {
    filter.searchSubcommittee = ''
    if (newProvince !== '') {
      fetchSubcommitteeLists(newProvince)
    } else {
      const Submmittee = listFilter.find(filter => filter.name === 'searchSubcommittee')

      if (Submmittee) Submmittee.items = [{ name: 'ทั้งหมด', id: '' }]
    }
  }
)

onMounted(async () => {
  getDropdownItems()
  initialize.value = false
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <FiltersAPI v-model="filter" :fields="listFilter" @submit="GetList" />
    <VCard>
      <VCardText>
        <div class="d-flex align-center flex-wrap gap-4">
          <VBtn
            class="mt-2"
            prepend-icon="mdi-file-excel"
            color="btn-excel"
            rounded="xl"
            variant="outlined"
            @click="GetList(true)"
          >
            ออกรายงาน Excel
          </VBtn>
        </div>
      </VCardText>
      <AppDataTableAPI
        :columns="listFields"
        :value="ListItem"
        :total-records="totalRecords"
        :header-no="false"
        @page="onPageChange"
      >
        <template #options="slotProps">
          <div class="text-center">
            <IconBtn @click="openViewPage(slotProps.data.govermentId)">
              <VIcon icon="mdi-eye-outline" />
              <VTooltip open-delay="500" location="top" activator="parent">ดูข้อมูล</VTooltip>
            </IconBtn>
          </div>
        </template>
      </AppDataTableAPI>
    </VCard>

    <VRow>
      <VCol cols="12" class="d-flex justify-end mt-5 me-4">
        <BtnGoBack />
      </VCol>
    </VRow>
  </div>
</template>
