<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import Filters from '@core/components/Filters.vue'
import { requiredValidator } from '@validators'

const router = useRouter()
const callAxios = useAxios()
const Swal = useSwal()

const searchQuery: Ref<string | number> = ref('')
const isEdit: Ref<boolean> = ref(false)
const refVForm: Ref<any> = ref(null)
const fileUpload: Ref<any> = ref([])

const editId: Ref<number> = ref(0)

interface FormType {
  listFields1: string
  listFields2: string
  listFields3: string
  listFields4: string
  listFields5: string
  listFields6: string
  contractorId: number
}

const form: Ref<FormType> = ref({
  listFields1: '',
  listFields2: '',
  listFields3: '13/09/2566',
  listFields4: 'นางสาวงดงาม จิตใจดี',
  listFields5: '13/09/2566',
  listFields6: 'นางสาวงดงาม จิตใจดี',
  contractorId: 0
})

const formRaw: Ref<FormType> = ref({
  listFields1: '',
  listFields2: '',
  listFields3: '13/09/2566',
  listFields4: 'นางสาวงดงาม จิตใจดี',
  listFields5: '13/09/2566',
  listFields6: 'นางสาวงดงาม จิตใจดี',
  contractorId: 0
})

const listStatus = ref([
  { id: 0, name: 'ใช้งาน' },
  { id: 1, name: 'ไม่ใช้งาน' }
])

// ทำสี
const statusActive = ref({
  ใช้งาน: 'success',
  ไม่ใช้งาน: 'error'
})

const breadcrumbItems = [
  {
    title: 'ผู้ดูแลระบบ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'จัดการประเภทผู้ใช้งาน',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const filter = ref({
  searchQuery: null
})

const listFilter = reactive([
  {
    name: 'searchQuery',
    type: 'text',
    label: 'ค้นหา'
  }
])

const listFields = reactive([
  {
    field: 'listFields1',
    header: 'ประเภทผู้ใช้งาน',
    sortable: true
  },
  {
    field: 'listFields2',
    header: 'สถานะการใช้งาน',
    sortable: true
  },
  {
    field: 'listFields3',
    header: 'วันที่สร้าง',
    sortable: true
  },
  {
    field: 'listFields4',
    header: 'ผู้สร้าง',
    sortable: true
  },
  {
    field: 'listFields5',
    header: 'วันที่แก้ไข',
    sortable: true
  },
  {
    field: 'listFields6',
    header: 'ผู้แก้ไข',
    sortable: true
  },

  {
    field: 'options',
    header: 'การจัดการ'
  }
])

const ListItem = ref([
  {
    listFields1: 'ผู้ดูแลระบบ',
    listFields2: 'ใช้งาน',
    listFields3: '13/09/2566',
    listFields4: 'นางมณี แก้วเวียงเดช',
    listFields5: '13/09/2566',
    listFields6: 'นางมณี แก้วเวียงเดช',

    contractorId: 0
  },
  {
    listFields1: 'นักทดสอบระบบ',
    listFields2: 'ใช้งาน',
    listFields3: '13/09/2566',
    listFields4: 'นายสมเดช ศักดิสิทธิ',
    listFields5: '13/09/2566',
    listFields6: 'นายสมเดช ศักดิสิทธิ',

    contractorId: 0
  },
  {
    listFields1: 'เจ้าหน้าที่',
    listFields2: 'ใช้งาน',
    listFields3: '13/09/2566',
    listFields4: 'นางสาวงดงาม จิตใจดี',
    listFields5: '13/09/2566',
    listFields6: 'นางสาวงดงาม จิตใจดี',

    contractorId: 0
  }
])

const ListItemStore = ref([])

const isDialogVisible = ref(false)

const GetList = () => {
  callAxios.RequestGet('/xxx/xxx').then(response => {
    if (response.status == 200) ListItem.value = response.data.response
    ListItemStore.value = response.data.response
  })
}

const GetListById = (id: number) => {
  form.value = ListItem.value[id]
  editId.value = id

  // callAxios
  //   .RequestGetById(
  //     `/xxx/xxx?xxx=${id}`,
  //     form
  //   )
  //   .then((response) => {
  //     if (response.status == 200) {
  //       form.value = response.data.response;
  //     }
  //   });
}

const AddForm = () => {
  Swal.ApproveConfirm().then(response => {
    if (response) {
      ListItem.value.push(form.value)
      onFormReset()

      // callAxios
      //   .RequestPost("/xxx/xxx", form.value)
      //   .then((response) => {
      //     if (response.status == 200) {
      //       uploadForm(response.data.transactionId);
      //     }
      //   });
    }
  })
}

const EditForm = (id: number) => {
  Swal.ApproveConfirm().then(response => {
    if (response) {
      ListItem.value.splice(id, 1, form.value)
      onFormReset()

      // callAxios
      //   .RequestPost("/xxx/xxx", form.value)
      //   .then((response) => {
      //     if (response.status == 200) {
      //       uploadForm(response.data.transactionId);
      //     }
      //   });
    }
  })
}

const uploadForm = (id: string) => {
  if (fileUpload.value !== null) {
    const formData = new FormData()

    formData.append('TransactionId', id)
    fileUpload.value.forEach((element: any) => {
      formData.append('DocumentName', element)
    })

    callAxios.RequestUpload('/xxx/xxx', formData).then(response => {
      if (response.status == 200) router.push({ path: '/apps/svs/contractor' })
    })
  } else {
    router.push({ path: '/apps/svs/contractor' })
  }
}

const onFormSubmit = () => {
  refVForm.value?.validate().then(({ valid: isValid }: { valid: boolean }) => {
    if (isValid) {
      if (isEdit.value === false) AddForm()
      else EditForm(editId.value)
    }
  })
}

const onFormReset = () => {
  isDialogVisible.value = false
  form.value = structuredClone(toRaw(formRaw.value))
}

const ApproveDelete = (id: number) => {
  const endpoint = `/xxx/xxx?xxx=${id}`

  console.log(listFields.value)

  ListItem.value.splice(id, 1)

  // Swal.ApproveDelete(endpoint).then((response) => {
  //   if (response) {
  //     // GetList();
  //   }
  // });
}
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <!-- {{ filter }} -->

    <Filters :fields="listFilter" :model-value="filter" />

    <VCard>
      <VCardText>
        <div class="d-flex align-center flex-wrap gap-4">
          <VBtn
            prepend-icon="mdi-plus"
            color="success-200"
            @click="((isDialogVisible = true), (isEdit = false))"
          >
            เพิ่ม
          </VBtn>
        </div>
      </VCardText>
      <!-- <AppDataTable :filters="filter.searchQuery" :columns="listFields" :value="ListItem"> -->
      <AppDataTable :filters="filter.searchQuery" :columns="listFields" :value="ListItem">
        <template #listFields2="slotProps">
          <div class="text-center">
            <!-- ทำสี -->
            <VChip :color="statusActive[slotProps.data.listFields2]" variant="elevated">
              {{ slotProps.data.listFields2 }}
            </VChip>
          </div>
        </template>

        <template #options="slotProps">
          <div class="text-center">
            <IconBtn
              @click="((isDialogVisible = true), (isEdit = true), GetListById(slotProps.index))"
            >
              <VIcon icon="mdi-pencil-outline" />
              <VTooltip open-delay="500" location="top" activator="parent">แก้ไขข้อมูล</VTooltip>
            </IconBtn>
            <IconBtn @click="ApproveDelete(slotProps.index)">
              <VIcon icon="mdi-delete-outline" />
              <VTooltip open-delay="500" location="top" activator="parent">ลบข้อมูล</VTooltip>
            </IconBtn>
            <!--
              <IconBtn @click="">
              <VIcon icon="mdi-cog-outline" />
              </IconBtn>
            -->
          </div>
        </template>
      </AppDataTable>
    </VCard>

    <VDialog v-model="isDialogVisible" persistent class="v-dialog-lg" no-click-animation scrollable>
      <VCard :title="isEdit ? 'แก้ไขข้อมูล' : 'เพิ่มประเภทผู้ใช้งาน'">
        <!-- List -->
        <DialogCloseBtn variant="text" size="small" @click="onFormReset" />

        <VCardText>
          <VForm ref="refVForm" @submit.prevent="onFormSubmit">
            <VRow class="mb-4 mx-2 mt-6">
              <VCol cols="12" md="1" />
              <VCol cols="12" md="10">
                <VRow>
                  <!-- 👉 outlined variant -->

                  <VCol cols="12" md="12">
                    <VRow class="mb-2" align="center">
                      <VCol cols="12" md="3" class="text-lg-end">
                        <label>
                          ประเภทผู้ใช้งาน :
                          <small class="text-error">*</small>
                        </label>
                      </VCol>
                      <VCol cols="12" md="9">
                        <!-- 👉 outlined variant -->

                        <VTextField v-model="form.listFields1" :rules="[requiredValidator]" />
                      </VCol>
                    </VRow>
                  </VCol>

                  <VCol cols="12" md="12">
                    <VRow class="mb-2" align="center">
                      <VCol cols="12" md="3" class="text-lg-end">
                        <label>
                          สถานะ :
                          <small class="text-error">*</small>
                        </label>
                      </VCol>
                      <VCol cols="12" md="9">
                        <!-- 👉 outlined variant -->
                        <VAutocomplete
                          v-model="form.listFields2"
                          :items="listStatus"
                          item-title="name"
                          item-value="name"
                          :rules="[requiredValidator]"
                        />
                      </VCol>
                    </VRow>
                  </VCol>
                </VRow>
              </VCol>
              <VCol cols="12" md="1" />
            </VRow>

            <!-- 👉 ปุ่มต่างๆ -->
            <div class="d-flex flex-wrap justify-center mt-5">
              <div class="demo-space-x">
                <VBtn type="submit" color="blue-600">บันทึก</VBtn>

                <VBtn color="error-300" @click="onFormReset">ยกเลิก</VBtn>
              </div>
            </div>
          </VForm>
        </VCardText>
      </VCard>
    </VDialog>
  </div>
</template>
