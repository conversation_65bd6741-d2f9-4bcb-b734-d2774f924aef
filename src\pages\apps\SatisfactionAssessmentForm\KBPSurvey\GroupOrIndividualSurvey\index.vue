<script setup>
import { filterMenu } from '@utils'

const inProcess = ref(true)

const breadcrumbItems = [
  {
    title: 'แบบประเมินความพึงพอใจ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'KBP Survey',
    disabled: false,
    to: ''
  },
  {
    title: 'แบบประเมินความพึงพอใจการให้บริการ',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const ListsMenu = ref([
  {
    title: 'แบบประเมินความพึงพอใจการให้บริการ (รายกลุ่ม)',
    toRaw: 'แบบประเมินความพึงพอใจการให้บริการ (รายกลุ่ม)',
    to: {
      name: 'apps-SatisfactionAssessmentForm-KBPSurvey-PublicServiceCenter-Group'
    },
    icon: 'mdi-note-plus',
    color: 'blue-700'
  },
  {
    title: 'แบบประเมินความพึงพอใจการให้บริการ (รายบุคคล)',
    toRaw: 'แบบประเมินความพึงพอใจการให้บริการ (รายบุคคล)',
    to: {
      name: 'apps-SatisfactionAssessmentForm-KBPSurvey-PublicServiceCenter-Individual'
    },
    icon: 'mdi-note-plus',
    color: 'success-200'
  }
])

onMounted(async () => {
  if (ListsMenu.value) {
    ListsMenu.value = await filterMenu(ListsMenu.value)
    inProcess.value = false
  } else {
    inProcess.value = false
  }
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <VCard class="mb-5">
      <VCardText>
        <BtnGoBack />
        <VRow class="align-center">
          <VCol
            v-for="(item, index) in ListsMenu"
            :key="index"
            cols="12"
            md="6"
            class="align-center"
          >
            <RouterLink :to="item.to">
              <VAlert prominent variant="outlined" :color="item.color" :icon="item.icon">
                {{ item.title }}
              </VAlert>
            </RouterLink>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
  </div>
</template>
