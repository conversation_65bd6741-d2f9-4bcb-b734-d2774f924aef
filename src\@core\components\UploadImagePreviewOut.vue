<script lang="ts" setup>
import { requiredChooseFile } from '@validators'

const props = defineProps({
  editDialog: {
    type: Boolean
  },
  urlImg: {
    type: String
  },
  rules: {
    type: Array,
    required: false,
    default: []
  },
  detail: {
    type: String,
    required: false
  },
  accept: {
    type: String,
    required: false,
    default: '.jpeg,.png,.jpg,GIF'
  },
  required: {
    type: Boolean
  },
  disabled: {
    type: Boolean,
    required: false,
    default: false
  }
})

const emit = defineEmits(['file-selected'])
const baseUrlImg = localStorage.baseUrlImg
const refInput = ref<HTMLElement>()

// const selectedFile = ref();
const selectedFile = ref<File | File[] | null>(null)
const selectedFileDisplay = ref([])

const accountDataLocal = ref({
  avatarImg: ''
})

const changeAvatar = (file: Event) => {
  const fileReader = new FileReader()
  const { files } = file.target as HTMLInputElement

  // selectedFile.value = files;
  selectedFile.value = files && files.length ? files[0] : null // Select only the first file or set to null

  emit('file-selected', selectedFile.value)

  if (files && files.length) {
    fileReader.readAsDataURL(files[0])
    fileReader.onload = () => {
      if (typeof fileReader.result === 'string')
        accountDataLocal.value.avatarImg = fileReader.result
    }
  }
}


const resetAvatar = () => {
  if (props.editDialog && props.urlImg) accountDataLocal.value.avatarImg = baseUrlImg + props.urlImg
  else if (!selectedFile.value) accountDataLocal.value.avatarImg = ''

  selectedFileDisplay.value = []
}

const addRules = () => {
  if (props.editDialog) {
    return props.rules
  } else {
    if (props.required) return [...props.rules, requiredChooseFile]
    else return props.rules
  }
}



watch(
  () => [props.editDialog, props.urlImg],
  ([editDialog, urlImg]) => {
    if (editDialog && urlImg) accountDataLocal.value.avatarImg = baseUrlImg + urlImg
    else if (!selectedFile.value) accountDataLocal.value.avatarImg = ''
  },
  { immediate: true } // ทำให้ watch ทำงานทันทีเมื่อ mount
)
</script>

<template>
  <VRow>
    <VCol cols="12" class="py-0">
      <div class="d-block d-sm-flex gap-4">
        <VFileInput
          ref="refInput"
          v-model="selectedFileDisplay"
          :accept="accept"
          density="comfortable"
          :rules="addRules()"
          label="เลือกไฟล์"
          :disabled="disabled"
          :bg-color="disabled ? 'grey-secondary' : ''"
          @input="changeAvatar"
          @click:clear="resetAvatar"
        >
          <template #append-inner>
            <VIcon icon="mdi-paperclip" class="icon-pointer" />
          </template>
          <template #details="{ isValid }">
            <span v-if="isValid.value == null || isValid.value" class="text-error">
              {{ props.detail }}
            </span>
          </template>
        </VFileInput>
   
      </div>
      <!-- ส่วน Preview รูปภาพ -->
    
    </VCol>
  </VRow>
</template>

<style scoped>
.icon-pointer {
  cursor: pointer;
}
</style>
