<script setup lang="ts">
import image1 from '@images/reportMenu/ReportMenu01.png'
import image2 from '@images/reportMenu/ReportMenu02.png'
import image3 from '@images/reportMenu/ReportMenu03.png'
import image4 from '@images/reportMenu/ReportMenu04.png'
import image5 from '@images/reportMenu/ReportMenu05.png'
import image6 from '@images/reportMenu/ReportMenu06.png'
import image7 from '@images/reportMenu/ReportMenu07.png'
import image8 from '@images/reportMenu/ReportMenu08.png'
import image9 from '@images/reportMenu/ReportMenu09.png'
import { filterMenuGECC } from '@utils'

const inProcess = ref(true)

const breadcrumbItems = [
  {
    title: 'รายงาน',
    disabled: false
  }
]

const ListsMenu = ref([
  {
    title: 'Dashboard ผลการสมัครรับรองมาตรฐาน GECC',
    toRaw: 'Dashboard ผลการสมัครรับรองมาตรฐาน GECC',
    to: {
      name: 'apps-Report-CertificationApplicationsDashboard'
    },
    iconPath: image1,
    color: 'info-500'
  },
  {
    title: 'Dashboard ผลประเมินการรักษามาตรฐาน GECC',
    toRaw: 'Dashboard ผลประเมินการรักษามาตรฐาน GECC',
    to: {
      name: 'apps-Report-MaintenanceEvaluationDashboard'
    },
    iconPath: image2,
    color: 'blue-700'
  },
  {
    title: 'Dashboard ภาพรวมการรับรองมาตรฐาน GECC',
    toRaw: 'Dashboard ภาพรวมการรับรองมาตรฐาน GECC',
    to: {
      name: 'apps-Report-CertificationOverview'
    },
    iconPath: image3,
    color: 'primary'
  },
  {
    title: 'รายงานข้อมูลการสมัคร',
    toRaw: 'รายงานข้อมูลการสมัคร',
    to: {
      name: 'apps-Report-CertificationApplicationData'
    },
    iconPath: image4,
    color: 'navy-300'
  },
  {
    title: 'ตารางสรุปผลการตรวจ (เขียว/แดง)',
    toRaw: 'ตารางสรุปผลการตรวจ (เขียว/แดง)',
    to: {
      name: 'apps-Report-GreenRedSummary'
    },
    iconPath: image5,
    color: 'success-200'
  },
  {
    title: 'สรุปภาพรวมรักษามาตรฐาน',
    toRaw: 'สรุปภาพรวมรักษามาตรฐาน',
    to: {
      name: 'apps-Report-GECCCertificationSummary'
    },
    iconPath: image6,
    color: 'purple-200'
  },
  {
    title: 'รายงานสรุปผลการประเมินความพึงพอใจ',
    toRaw: 'รายงานสรุปผลการประเมินความพึงพอใจ',
    to: {
      name: 'apps-Report-SatisfactionEvaluationSummary'
    },
    iconPath: image7,
    color: 'info-500'
  },
  {
    title: 'ผลการประเมินความพึงพอใจ',
    toRaw: 'ผลการประเมินความพึงพอใจ',
    to: {
      name: 'apps-Report-SatisfactionResults'
    },
    iconPath: image8,
    color: 'error-200'
  },
  {
    title: 'สรุปรายชื่อผู้ประสานงานประจำปี',
    toRaw: 'สรุปรายชื่อผู้ประสานงานประจำปี',
    to: {
      name: 'apps-Report-AnnualCoordinatorSummary'
    },
    iconPath: image9,
    color: 'navy'
  }
])

onMounted(async () => {
  if (ListsMenu.value) {
    ListsMenu.value = await filterMenuGECC(ListsMenu.value)
    inProcess.value = false
  } else {
    inProcess.value = false
  }
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <VCard class="mb-5">
      <VCardText>
        <VRow class="align-center">
          <VCol
            v-for="(item, index) in ListsMenu"
            :key="index"
            cols="12"
            md="6"
            class="align-center"
          >
            <RouterLink :to="item.to">
              <VAlert prominent variant="outlined" :color="item.color">
                <img :src="item.iconPath" alt="icon" width="75" height="75" class="mr-2" />
                {{ item.title }}
              </VAlert>
            </RouterLink>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
  </div>
</template>

<style scoped>
.v-card img {
  vertical-align: middle;
}
</style>
