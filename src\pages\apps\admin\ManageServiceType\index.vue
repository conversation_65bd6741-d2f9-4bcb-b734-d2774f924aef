<script setup lang="ts">
import { reactive, ref } from 'vue'
import DialogManageService from './DialogManageService.vue'
import BtnInsert from '@/@core/components/button/BtnInsert.vue'
import IconDelete from '@/@core/components/button/IconDelete.vue'
import IconEdit from '@/@core/components/button/IconEdit.vue'
import FiltersAPI from '@/components/FiltersAPI.vue'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import type { IGetSystemServiceTypeRes } from '@interfaces/UserManagementInterface'

const emit = defineEmits(['update-banners'])
const callAxios = useAxios()
const Swal = useSwal()
const currentPage: Ref<number> = ref(1)
const rowPerPage: Ref<number> = ref(20)
const totalRecords: Ref<number> = ref(0)
const editId = ref()

// State management
const isDialogVisible = ref(false)
const isAdd = ref(false)
const isEdit = ref(false)
const isView = ref(false)
const ListItem = ref<IGetSystemServiceTypeRes[]>([])

const openAddDialog = () => {
  isAdd.value = true
  isEdit.value = false
  isView.value = false
  isDialogVisible.value = true
}

const openEditDialog = (id: string) => {
  isEdit.value = true
  isView.value = false
  isAdd.value = false
  isDialogVisible.value = true
  editId.value = id
}

const openViewDialog = (id: string) => {
  isView.value = true
  isEdit.value = false
  isAdd.value = false
  isDialogVisible.value = true
  editId.value = id
}

const breadcrumbItems = [
  {
    title: 'ผู้ดูแลระบบ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'จัดการด้านตามลักษณะการให้บริการ',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const filter = reactive({
  searchWord: '',
  searchStatus: ''
})

const listFilter = ref([
  {
    name: 'searchStatus',
    type: 'select',
    label: 'สถานะ ',
    default: '',
    title: 'StatusName',
    value: 'StatusId',
    items: [
      {
        StatusName: 'ทั้งหมด',
        StatusId: ''
      },
      {
        StatusName: 'ไม่ใช้งาน',
        StatusId: 'false'
      },
      {
        StatusName: 'ใช้งาน',
        StatusId: 'true'
      }
    ]
  },
  {
    name: 'searchWord',
    type: 'text',
    label: 'คำค้น',
    placeholder: 'ระบุชื่อด้านตามลักษณะการให้บริการ'
  }
])

const listFields = ref([
  {
    field: 'no',
    header: 'ลำดับที่',
    sortable: false,
    style: { textAlign: 'center' }
  },
  {
    field: 'serviceTypeName',
    header: 'ชื่อด้านตามลักษณะการให้บริการ',
    sortable: false
  },
  {
    field: 'year',
    header: 'ปีเริ่มต้น-สิ้นสุด',
    sortable: false
  },
  {
    field: 'isActive',
    header: 'สถานะ',
    sortable: false
  },
  {
    field: 'createDate',
    header: 'วันที่สร้าง',
    sortable: false,
    style: { textAlign: 'center' }
  },
  {
    field: 'createBy',
    header: 'ชื่อผู้สร้าง',
    sortable: false,
    style: { textAlign: 'center' }
  },
  {
    field: 'updateDate',
    header: 'วันที่แก้ไข',
    sortable: false,
    style: { textAlign: 'center' }
  },
  {
    field: 'updateBy',
    header: 'ชื่อผู้แก้ไข',
    sortable: false,
    style: { textAlign: 'center' }
  },
  {
    field: 'options',
    header: 'การจัดการ',
    sortable: false,
    style: { textAlign: 'center' }
  }
])

const GetList = async () => {
  try {
    const queryParams = new URLSearchParams({
      Page: currentPage.value.toString(),
      PageSize: rowPerPage.value.toString()
    })

    if (filter.searchWord) queryParams.append('Keyword', filter.searchWord)
    if (filter.searchStatus) queryParams.append('Status', filter.searchStatus)

    const response = await callAxios.RequestGet(
      `/UserManagement/GetSystemServiceType?${queryParams.toString()}`
    )

    console.log('ข้อมูลรายการที่โหลดจาก API:', response.data.response)

    if (response.status === 200) {
      totalRecords.value = response.data.count
      ListItem.value = response.data.response as IGetSystemServiceTypeRes[]
    } else {
      Swal.AddConditionFailText('ไม่สามารถโหลดข้อมูลได้')
    }
  } catch (error) {
    Swal.callCatch()
  }
}

const ApproveDelete = (id: any) => {
  const endpoint = `/UserManagement/DeleteSystemServiceType?TransactionServiceTypeId=${id}`

  Swal.ApproveDelete(endpoint).then(response => {
    if (response) GetList()
  })
}

const handleDialogUpdate = () => {
  GetList() // Refresh the list after add/edit operation
  emit('update-banners')
}

const onPageChange = (event: { page: number; rows: number }) => {
  currentPage.value = event.page + 1
  rowPerPage.value = event.rows
  GetList()
}

const checkRowHighlight = (rowData: any) => {
  if (rowData && typeof rowData.no === 'string') {
    const noNumber = parseInt(rowData.no, 10)

    return noNumber % 2 === 0 ? 'bg-sub-no' : ''
  }

  return ''
}

onMounted(() => {
  GetList()
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <FiltersAPI v-model="filter" :fields="listFilter" @submit="GetList" />
    <VCard class="px-4">
      <VCardText>
        <div class="d-flex align-center flex-wrap gap-4">
          <BtnInsert
            class="mt-2"
            prepend-icon="mdi-plus-box-multiple"
            color="btn-add"
            rounded="xl"
            @click="openAddDialog"
          >
            เพิ่มรายการ
          </BtnInsert>
        </div>
      </VCardText>

      <AppDataTableAPI
        :total-records="totalRecords"
        :columns="listFields"
        :value="ListItem"
        :header-no="false"
        :row-class="checkRowHighlight"
        @page="onPageChange"
      >
        <!-- กำหนดสีของ Chip ในคอลัมน์สถานะ -->
        <template #isActive="slotProps">
          <ChipStatus
            :status="slotProps.data.isActive"
            :label="slotProps.data.isActive === 'ใช้งาน' ? 'ใช้งาน' : ''"
          />
        </template>

        <!-- ส่วน Icon การจัดการ -->
        <template #options="slotProps">
          <div class="text-center">
            <IconBtn @click="openViewDialog(slotProps.data.transactionServiceTypeId)">
              <VIcon icon="mdi-eye-outline" />
              <VTooltip open-delay="500" location="top" activator="parent">ดูข้อมูล</VTooltip>
            </IconBtn>

            <IconEdit @click="openEditDialog(slotProps.data.transactionServiceTypeId)" />
            <IconDelete @click="ApproveDelete(slotProps.data.transactionServiceTypeId)" />
          </div>
        </template>
      </AppDataTableAPI>
    </VCard>
    <DialogManageService
      v-model="isDialogVisible"
      :is-add="isAdd"
      :is-edit="isEdit"
      :is-view="isView"
      :view-id="editId"
      :edit-id="editId"
      @update="handleDialogUpdate"
    />
  </div>
</template>

<style scoped>
.no-select {
  user-select: none;
}
</style>
