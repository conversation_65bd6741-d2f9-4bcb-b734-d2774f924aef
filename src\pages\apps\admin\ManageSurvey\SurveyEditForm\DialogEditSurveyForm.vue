<script setup lang="ts">
import { defineProps, ref } from 'vue'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import { requiredValidator } from '@validators'

const props = defineProps({
  modelValue: <PERSON>olean,
  isAdd: <PERSON>olean,
  isEdit: <PERSON>olean,
  isView: <PERSON>olean,
  viewId: String,
  editId: String,
  assessmentsData: Array
})

const emit = defineEmits(['update:modelValue', 'update'])
const Swal = useSwal()
const callAxios = useAxios()
const refVForm: Ref<any> = ref(null)

const viewId = computed(() => props.viewId)
const editId = computed(() => props.editId)

const listStatus = ref([
  { id: 'ใช้งาน', name: 'ใช้งาน' },
  { id: 'ไม่ใช้งาน', name: 'ไม่ใช้งาน' }
])

const form = reactive({
  assessmentsName: '',
  isActive: 'ไม่ใช้งาน'
})

const createFormData = () => {
  return {
    assessmentsName: form.assessmentsName,
    isActive: form.isActive === 'ใช้งาน'
  }
}

const formDefault = structuredClone(toRaw(form))

const resetForm = () => {
  Object.assign(form, structuredClone(formDefault))
}

const onFormSubmit = async () => {
  try {
    if (!refVForm.value) return

    const { valid } = await refVForm.value.validate()

    if (valid) {
      if (props.isAdd) await AddForm()
      else if (props.isEdit) await EditForm()
      else throw new Error('Invalid state')
    }
  } catch (error) {
    Swal.AddConditionFailText('ไม่สามารถดำเนินการได้: ไม่มีสถานะที่ถูกต้อง')
  }
}

const AddForm = async () => {
  const confirmed = await Swal.ApproveConfirm()
  if (!confirmed) return

  const formData = createFormData()

  try {
    emit('update', {
      type: 'add',
      data: {
        ...formData,
        parentAssessmentsId: editId.value
      }
    })

    resetForm()
    isDialogVisible.value = false

    Swal.AddSuccess
  } catch (error) {
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการเพิ่มข้อมูล')
  }
}

const EditForm = async () => {
  try {
    const confirmed = await Swal.ApproveConfirm()
    if (!confirmed) return

    // Prepare the updated data
    const updatedData = {
      assessmentsName: form.assessmentsName.trim(),
      isActive: form.isActive === 'ใช้งาน',
      parentAssessmentsId: props.editId
    }

    emit('update', { type: 'edit', data: updatedData })

    // Log for debugging (optional)
    console.log('EditForm Emit Data:', { type: 'edit', data: updatedData })

    resetForm()
    isDialogVisible.value = false
  } catch (error) {
    console.error('EditForm Error:', error)

    Swal.EditFail(error.message || 'ไม่สามารถแก้ไขข้อมูลได้')
  }
}

const findSubModelRecursive = (list, targetName) => {
  for (const item of list) {
    if (item.assessmentsName === targetName) return item

    if (item.subModels && item.subModels.length > 0) {
      const found = findSubModelRecursive(item.subModels, targetName)
      if (found) return found
    }
  }

  return null
}

const ViewForm = async () => {
  try {
    console.log('editId', props.editId)

    // if (!props.editId?.includes("-")) {
    if (props.editId) {
      // Perform recursive search in assessmentsData
      const subModel = findSubModelRecursive(props.assessmentsData, props.editId)

      if (subModel) {
        form.assessmentsName = subModel.assessmentsName
        form.isActive = subModel.isActive === true ? 'ใช้งาน' : 'ไม่ใช้งาน'
        console.log('SubModel found:', subModel)
      } else {
        console.error('SubModel not found for editId:', props.editId)
        Swal.AddFailText('ไม่พบข้อมูลในรายการ')
      }
    }

    // } else {
    //   // Fetch existing item by `assessmentsId` (via API)
    //   const response = await callAxios.RequestGetById(
    //     "/UserManagement/GetAssessmentsById",
    //     `?AssessmentsId=${props.editId}`
    //   );

    //   if (response.status === 200) {
    //     const data = response.data.response;
    //     form.assessmentsName = data.assessmentsName;
    //     form.isActive = data.isActive;
    //   }
    // }
  } catch (error) {
    console.error('Error in ViewForm:', error)
    Swal.AddFailText(error.message || 'เกิดข้อผิดพลาด')
  }
}

const closeDialog = async () => {
  const response = await Swal.ApproveCancel()
  if (response) {
    resetForm()
    isDialogVisible.value = false
  }
}

const setTitle = computed(() => {
  if (props.isEdit) return 'แก้ไขข้อคำตอบ'

  return 'เพิ่มข้อคำตอบ'
})

const isDialogVisible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value)
})

watch(
  () => isDialogVisible.value,
  async newVal => {
    if (newVal && props.isEdit) await ViewForm()
  }
)

watch(
  () => props.assessmentsData,
  newVal => {
    console.log('assessmentsData in DialogEditSurveyForm:', newVal)
  }
)
</script>

<template>
  <div>
    <VDialog v-model="isDialogVisible" persistent class="v-dialog-lg" no-click-animation scrollable>
      <VCard :title="setTitle">
        <DialogCloseBtn variant="text" size="small" @click="closeDialog" />
        <VCardText>
          <VForm ref="refVForm" @submit.prevent="onFormSubmit">
            <VRow class="mb-4 mx-2 mt-3 justify-center">
              <VCol cols="12" md="10">
                <VRow>
                  <VCol cols="12" md="12">
                    <VRow class="mb-2" align="center">
                      <VCol cols="12" md="12" class="py-0 mb-2">
                        <label>
                          รายละเอียด :
                          <small class="text-error">*</small>
                        </label>
                      </VCol>
                      <VCol cols="12" md="12" class="py-0">
                        <VTextField
                          v-model="form.assessmentsName"
                          :rules="[requiredValidator]"
                          density="comfortable"
                          placeholder="ตัวอย่าง : ชาย"
                          :counter="100"
                          :maxlength="100"
                        />
                      </VCol>
                    </VRow>
                  </VCol>

                  <VCol cols="12" md="12">
                    <VRow class="mb-2" align="center">
                      <VCol cols="12" md="12" class="py-0 mb-2">
                        <label>
                          สถานะ :
                          <small class="text-error">*</small>
                        </label>
                      </VCol>
                      <VCol cols="12" md="12" class="py-0">
                        <VAutocomplete
                          v-model="form.isActive"
                          :rules="[requiredValidator]"
                          :items="listStatus"
                          item-title="name"
                          item-value="id"
                          density="comfortable"
                          placeholder="ตัวอย่าง : ใช้งาน"
                          class="no-select"
                        />
                      </VCol>
                    </VRow>
                  </VCol>
                </VRow>
              </VCol>
            </VRow>

            <div class="d-flex flex-wrap justify-center mt-5">
              <div class="demo-space-x">
                <VBtn type="submit" color="blue-600" rounded="xl" prepend-icon="mdi-content-save">
                  บันทึก
                </VBtn>
                <VBtn
                  color="error-300"
                  rounded="xl"
                  prepend-icon="mdi-close-circle"
                  @click="closeDialog"
                >
                  ยกเลิก
                </VBtn>
              </div>
            </div>
          </VForm>
        </VCardText>
      </VCard>
    </VDialog>
  </div>
</template>
