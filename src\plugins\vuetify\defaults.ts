export default {
  IconBtn: {
    icon: true,
    size: 'small',
    color: 'default',
    variant: 'text',
    VIcon: {
      size: 24
    }
  },
  VAlert: {
    VBtn: {
      color: undefined
    }
  },
  VAvatar: {
    // ℹ️ Remove after next release
    variant: 'flat',
    VIcon: {
      size: 24
    }
  },
  VBadge: {
    // set v-badge default color to primary
    color: 'primary'
  },
  VBtn: {
    // set v-btn default color to primary
    color: 'primary'
  },
  VChip: {
    elevation: 0
  },
  VList: {
    activeColor: 'primary',
    VIcon: {
      size: 24
    }
  },
  VPagination: {
    activeColor: 'primary',
    density: 'comfortable',
    size: '32'
  },
  VTabs: {
    // set v-tabs default color to primary
    color: 'primary',
    VSlideGroup: {
      showArrows: true
    }
  },
  VTooltip: {
    // set v-tooltip default location to top
    location: 'top'
  },
  VCheckboxBtn: {
    color: 'primary'
  },
  VCheckbox: {
    // set v-checkbox default color to primary
    color: 'primary',
    density: 'comfortable',
    hideDetails: 'auto'
  },
  VRadioGroup: {
    color: 'primary',
    hideDetails: 'auto'
  },
  VRadio: {
    hideDetails: 'auto'
  },
  VSelect: {
    variant: 'outlined',
    color: 'primary',
    hideDetails: 'auto'
  },
  VRangeSlider: {
    // set v-range-slider default color to primary
    color: 'primary',
    density: 'comfortable',
    thumbLabel: true,
    hideDetails: 'auto'
  },
  VRating: {
    density: 'compact',
    activeColor: 'warning',
    color: 'disabled'
  },
  VProgressCircular: {
    // set v-progress-circular default color to primary
    color: 'primary'
  },
  VSlider: {
    // set v-slider default color to primary
    color: 'primary',
    hideDetails: 'auto'
  },
  VTextField: {
    variant: 'outlined',
    color: 'primary',
    hideDetails: 'auto'
  },
  VAutocomplete: {
    variant: 'outlined',
    color: 'primary',
    hideDetails: 'auto'
  },
  VCombobox: {
    variant: 'outlined',
    color: 'primary',
    hideDetails: 'auto'
  },
  VFileInput: {
    variant: 'outlined',
    color: 'primary',
    hideDetails: 'auto'
  },
  VTextarea: {
    variant: 'outlined',
    color: 'primary',
    hideDetails: 'auto'
  },
  VSwitch: {
    // set v-switch default color to primary
    color: 'primary',
    hideDetails: 'auto'
  }
}
