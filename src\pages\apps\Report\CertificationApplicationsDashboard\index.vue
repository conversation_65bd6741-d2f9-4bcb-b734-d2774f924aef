<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import BtnGoBack from '@/@core/components/button/BtnGoBack.vue'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'

// Import Images
import image2 from '@images/report/Advance.png'
import image3 from '@images/report/Balance.png'
import image1 from '@images/report/Excellent.png'
import image4 from '@images/report/person-info.png'
import image6 from '@images/report/person-no.png'
import image5 from '@images/report/person-pass.png'

// Router and Axios instances
const router = useRouter()
const route = useRoute()
const callAxios = useAxios()
const Swal = useSwal()

// Query Params
const SystemDashboardId = ref('080e806c-aa9e-40fb-94f3-b15fd418a83b')

// State Variables
const statusSwitch = ref(true)
const statusChart = ref(true)

const rankOrder = ref([])

const breadcrumbItems = [
  { title: 'รายงาน', disabled: false, to: '/apps/report/menu' },
  {
    title: 'Dashboard ผลการสมัครรับรองมาตรฐาน GECC',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const listFields = ref([
  {
    field: 'no',
    header: 'ลำดับที่',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'year',
    header: 'ปี พ.ศ.',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'govermentName',
    header: 'ชื่อศูนย์ราชการสะดวก',
    sortable: true,
    style: { textAlign: 'center' }
  }
])

interface StatusItem {
  label: string
  value: any
  colorClass: string
  image: string
}

const currentPage = ref(1)
const rowPerPage = ref(10)
const totalCount = ref(0)

const selectedRank = ref(0)
const year = ref([])
const departmentParrent = ref(null)
const govermentName = ref<any[]>([])

const status = ref<StatusItem[]>([])
const ListItem = ref([])
const chartbarI = ref({})
const chartbarII = ref({})
const inProcess = ref(false)
const initialValue = ref(false)

// ฟังก์ชันสำหรับ Map ค่า Rank Order
const mapRankOrder = (data: any) => [
  {
    label: 'Excellent',
    sublabel: 'ระดับเป็นเลิศ',
    value: data.certificateMax ?? 0,
    colorClass: 'text-warning',
    image: image1
  },
  {
    label: 'Advance',
    sublabel: 'ระดับก้าวหน้า',
    value: data.certificateMid ?? 0,
    colorClass: 'text-secondary-100',
    image: image2
  },
  {
    label: 'Balance',
    sublabel: 'ระดับพื้นฐาน',
    value: data.certificateMin ?? 0,
    colorClass: 'text-primary',
    image: image3
  }
]

// ฟังก์ชันสำหรับ Map ค่า Status
const mapStatus = (data: any) => [
  {
    label: 'สมัคร',
    value: data.registerData || 0,
    colorClass: 'text-primary',
    image: image4
  },
  {
    label: 'ผ่าน',
    value: data.registerPass || 0,
    colorClass: 'text-success',
    image: image5
  },
  {
    label: 'ไม่ผ่าน',
    value: data.registerNot || 0,
    colorClass: 'text-error',
    image: image6
  }
]

// ตัวแปรเก็บค่าเริ่มต้น
const defaultRankOrder = ref(
  mapRankOrder({ certificateMax: 0, certificateMid: 0, certificateMin: 0 })
)

const defaultStatus = ref(mapStatus({ registerData: 0, registerPass: 0, registerNot: 0 }))

// Fetch Data Function
const fetchReportData = async () => {
  Swal.fetchLoadingApi()
  inProcess.value = true

  const currentYearThai = new Date().getFullYear() + 543

  const [StartYear, EndYear] =
    Array.isArray(year.value) && year.value.length > 0
      ? year.value
      : [currentYearThai, currentYearThai]

  try {
    const params = new URLSearchParams({
      SystemDashboardId: SystemDashboardId.value?.toString() || '',
      Page: currentPage.value.toString(),
      PageSize: rowPerPage.value.toString()
    })

    if (StartYear) params.append('StartYear', StartYear)
    if (EndYear) params.append('EndYear', EndYear)
    if (departmentParrent.value === 0 || departmentParrent.value)
      params.append('OrgStructureId', departmentParrent.value.toString())

    if (selectedRank.value !== null && selectedRank.value >= 0) {
      const certificateNameMap = ['ระดับเป็นเลิศ', 'ระดับก้าวหน้า', 'ระดับพื้นฐาน']

      const certificateName = certificateNameMap[selectedRank.value]
      if (certificateName) params.append('CertificateName', certificateName)
    }

    const response = await callAxios.RequestGet(
      `UserManagement/GetSystemDashboardDetailById?${params.toString()}`
    )

    if (response.status === 200) {
      totalCount.value = response.data.count

      const data = response.data.response

      if (Object.keys(data).length !== 0 && typeof data === 'object') {
        // อัปเดต Status และ Rank Order โดยอิงจากค่าเริ่มต้น
        status.value = mapStatus(data)
        rankOrder.value = mapRankOrder(data.data)

        ListItem.value = Array.isArray(data.data.dataCertificate)
          ? data.data.dataCertificate.map((item: any) => ({
              no: item.no,
              year: item.year,
              govermentName: item.govermentName || 'ไม่ระบุชื่อหน่วยงาน'
            }))
          : []

        // ChartCombobar Data
        chartbarI.value = {
          header: data.chartBar.header,
          isActive: data.chartBar.isActive,
          bar: {
            labelsYears: data.chartBar.labelsYears,
            datasets: data.chartBar.datasets
          }
        }

        chartbarII.value = {
          header: data.chart.header,
          isActive: data.chart.isActive,
          labels: data.chart.bar.labelsYears,
          datasets: data.chart.bar.datasets
        }

        if (data.chartBar.isActive) statusChart.value = true
        else if (data.chart.isActive) statusChart.value = false
      } else {
        // รีเซ็ตค่าเป็นค่าเริ่มต้น
        status.value = defaultStatus.value
        rankOrder.value = defaultRankOrder.value
        ListItem.value = []
        chartbarI.value = {}
        chartbarII.value = {}
        Swal.AddConditionFailText('ไม่พบข้อมูล')
      }
    } else {
      throw new Error(`Unexpected response status: ${response.status}`)
    }
  } catch (error) {
    console.error('Error fetching data:', error)
    Swal.AddFail()
  } finally {
    inProcess.value = false
    initialValue.value = true
    Swal.close()
  }
}

const getDropdownItems = async () => {
  try {
    // Fetch data for GetDropDownSystemOrgStructure
    const response = await callAxios.RequestGet(
      '/TransactionRegister/GetDropDownSystemOrgStructure'
    )

    if (response.status === 200) {
      govermentName.value = [{ name: 'ทั้งหมด', orgStructureId: '' }, ...response.data.response]
    }
  } catch (error) {
    console.error('Error fetching dropdown items:', error)
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการโหลดข้อมูล')
  }
}

const updateDashboardStatusIsActive = async (statusIsActive: boolean) => {
  const payload = {
    systemDashboardId: SystemDashboardId.value,
    typeFirst: !statusIsActive,
    typeSecond: false
  }

  try {
    const response = await callAxios.RequestPut(
      `Keyword/UpdateChart?systemDashboardId=${payload.systemDashboardId}&typeFirst=${payload.typeFirst}&typeSecond=${payload.typeSecond}`,
      payload
    )

    if (response.status === 200) {
      Swal.AddSuccess()
      console.log('Dashboard status updated:', payload)
    } else {
      throw new Error(`Unexpected response status: ${response.status}`)
    }
  } catch (error: any) {
    console.error('Error updating dashboard status:', error)
    Swal.AddFailText(error.data.message)
  }
}

const onPageChange = (event: { page: number; rows: number }) => {
  currentPage.value = event.page + 1
  rowPerPage.value = event.rows
  fetchReportData()
}

watch(statusSwitch, newVal => {
  statusChart.value = !statusChart.value
  updateDashboardStatusIsActive(statusChart.value)
})

watch(year, (newVal, oldVal) => {
  console.log('Year updated:', { newVal, oldVal })

  // if (!Array.isArray(newVal) || newVal.length === 0) {
  //   return;
  // }
  fetchReportData()
})

watch(departmentParrent, (newVal, oldVal) => {
  console.log('departmentParrent updated:', { newVal, oldVal })
  fetchReportData()
})

watch(selectedRank, (newVal, oldVal) => {
  console.log('CertificateName updated:', { newVal, oldVal })
  fetchReportData()
})

onMounted(() => {
  getDropdownItems()
  fetchReportData()
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <VRow>
      <!-- Left Side: Summary Cards and Data Table -->
      <VCol cols="12" md="5">
        <VCard>
          <VRow class="mb-4 mx-2 mt-2">
            <VCol v-for="(rank, index) in rankOrder" :key="index" cols="12" md="4" sm="4">
              <VCard
                class="d-flex flex-column align-center text-center py-6 hover-card"
                :class="[{ 'hover-active': selectedRank === index }]"
                link
                hover
                @click="selectedRank = index"
              >
                <!-- Image and Labels Row -->
                <VRow class="align-center justify-center">
                  <VCol cols="5" md="12" class="text-center">
                    <img :src="rank.image" alt="Icon" />
                  </VCol>
                  <VCol cols="7" md="12" class="text-center">
                    <div class="text-h6 font-weight-bold">
                      {{ rank.label }}
                    </div>
                    <div class="text-subtitle1">
                      {{ rank.sublabel }}
                    </div>
                  </VCol>
                </VRow>
                <!-- Value Row -->
                <VRow>
                  <VCol cols="12" class="text-end">
                    <div :class="rank.colorClass" class="text-h3">
                      {{ rank.value }}
                    </div>
                  </VCol>
                </VRow>
              </VCard>
            </VCol>
          </VRow>

          <!-- Data Table Section -->
          <VRow class="mb-4 mx-2 mt-2">
            <VCol cols="12">
              <AppDataTableAPI
                :columns="listFields"
                :value="ListItem"
                :paginator="totalCount"
                :total-records="totalCount"
                :header-no="false"
                :scrollable="true"
                @page="onPageChange"
              />
            </VCol>
          </VRow>
        </VCard>
      </VCol>

      <!-- Right Side: Application status Cards -->
      <VCol cols="12" md="7">
        <VRow>
          <VCol cols="6">
            <DateTimePicker
              v-model="year"
              format="YYYY"
              year-picker
              range
              no-data-text="ไม่มีข้อมูล"
              density="comfortable"
              placeholder="พ.ศ."
              bg-color="primary"
              rounded
              :icon-style-flag="true"
            />
          </VCol>

          <VCol cols="6">
            <VAutocomplete
              v-model="departmentParrent"
              :items="govermentName"
              item-title="name"
              item-value="orgStructureId"
              no-data-text="ไม่มีข้อมูล"
              density="default"
              rounded
              bg-color="primary"
              :persistent-placeholder="true"
              placeholder="เลือกหน่วยงาน"
            />
          </VCol>
        </VRow>

        <VCard class="d-flex align-center justify-center text-center px-4 py-4 mt-9 card-container">
          <VRow>
            <VCol
              v-for="(stat, index) in status"
              :key="index"
              cols="12"
              sm="4"
              md="4"
              class="stat-col"
              :class="{ 'with-border': index !== status.length - 1 }"
            >
              <VRow class="align-center w-100">
                <VCol cols="12" sm="6" class="order-1 order-sm-2 pl-0">
                  <img
                    :src="stat.image"
                    alt="Icon"
                    class="overlapping-image"
                    :class="{
                      'first-image': index === 0,
                      'second-image': index === 1,
                      'last-image': index === status.length - 1
                    }"
                  />
                </VCol>

                <VCol cols="5" sm="6" class="text-start order-2 order-sm-1 pr-0">
                  <div class="text-h5 font-weight-bold">
                    {{ stat.label }}
                  </div>
                </VCol>

                <VCol cols="7" sm="12" class="text-end order-3 order-sm-3">
                  <div :class="stat.colorClass" class="text-h3">
                    {{ stat.value }}
                  </div>
                </VCol>
              </VRow>
            </VCol>
          </VRow>
        </VCard>

        <VCard class="mt-4 mb-4">
          <VCardText>
            <template v-if="statusChart">
              <ChartComboBarLine class="mt-4" :chart-data="chartbarI" />
            </template>
            <template v-else>
              <ChartBarHorizontal class="mt-4" :chart-data="chartbarII" />
            </template>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>
    <div class="d-flex align-center justify-end flex-wrap gap-4">
      <BtnGoBack />
    </div>
  </div>
</template>

<style scoped>
.card-container {
  position: relative;
  overflow: visible;
}

.stat-col {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.text-h3 {
  margin-block-end: 0;
  margin-block-start: 0;
}

.v-col.v-col-12.text-end {
  padding: initial;
}

.overlapping-image {
  position: relative;
  block-size: 130px;
  inline-size: 130px;
  inset-block-start: -35px;
  margin-block: -20px -50px;
  object-fit: contain;
}

.with-border {
  border-right: 2px solid #d5d5d55c;
}

.with-border:last-child {
  border-right: none;
}

.rank-image {
  block-size: 70px;
  inline-size: 70px;
  margin-inline-end: 10px;
  object-fit: contain;
}

.hover-card {
  transition: all 0.3s ease;
}

.hover-card.hover-active {
  border: 2px solid #42a5f5;
  box-shadow: 0 4px 15px rgba(66, 165, 245, 50%);
  transform: scale(1.02);
}

::v-deep(.v-input input::placeholder) {
  color: white !important;
}

@media (max-width: 1599px) {
  .overlapping-image {
    block-size: 111px;
    inline-size: 111px;
    margin-block-start: -20px;
    max-inline-size: 100px;
  }
  .text-h5 {
    text-align: center;
  }
}

@media (max-width: 2160px) and (min-width: 1501px) {
  .overlapping-image.first-image {
    right: 15px;
  }
  .overlapping-image.second-image {
    right: 10px;
  }
  .overlapping-image.last-image {
    right: 5px;
  }
}

@media (max-width: 1500px) and (min-width: 1201px) {
  .overlapping-image.first-image {
    right: 9px;
  }
}

@media (max-width: 1200px) and (min-width: 1029px) {
  .overlapping-image.first-image {
    right: 20px;
  }
  .overlapping-image.second-image {
    right: 17px;
  }
}

@media (max-width: 1028px) and (min-width: 960px) {
  .text-h5 {
    font-size: 21px !important;
  }
  .text-h6 {
    font-size: 17px !important;
  }
  .text-subtitle1 {
    font-size: 14px !important;
  }
  .overlapping-image.first-image {
    right: 27px;
  }
  .overlapping-image.second-image {
    right: 21px;
  }
  .overlapping-image.last-image {
    right: 12px;
  }
}

@media (max-width: 959px) {
  .card-container {
    margin-top: 20px !important;
  }
  .overlapping-image {
    margin-top: 14px;
  }
}

@media (max-width: 800px) and (min-width: 683px) {
  .overlapping-image.first-image {
    right: 12px;
  }
  .overlapping-image.second-image {
    right: 8px;
  }
}

@media (max-width: 682px) and (min-width: 600px) {
  .text-h5 {
    font-size: 23px !important;
  }
  .overlapping-image {
    margin-top: 30px;
    block-size: 80px;
    inline-size: 80px;
  }
  .text-h6 {
    font-size: 17px !important;
  }
  .text-subtitle1 {
    font-size: 13px !important;
  }
}

@media (max-width: 599px) {
  .with-border {
    border-right: none;
    border-bottom: 2px solid #d5d5d55c;
  }
}

@media (max-width: 375px) {
  .text-h3 {
    font-size: 40px !important;
  }
  .with-border {
    border-right: none;
  }
}
</style>
