<script setup lang="ts">
import { computed, defineProps, reactive, ref, toRaw, watch } from 'vue'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import { booleanValidator, requiredValidator, validateCKEditor } from '@validators'

const props = defineProps({
  modelValue: Boolean,
  title: String,
  mainId: String,
  isAdd: Boolean,
  isEdit: Boolean,
  isView: Boolean,
  viewId: String,
  editId: String
})

const emit = defineEmits(['closeDialog', 'update:modelValue', 'update'])
const Swal = useSwal()
const callAxios = useAxios()

const refVForm: Ref<any> = ref(null)
const viewId = computed(() => props.viewId)
const editId = computed(() => props.editId)
const errorMessage = ref('')

const isDialogVisible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value)
})

const listStatus = ref([
  { id: true, name: 'ใช้งาน' },
  { id: false, name: 'ไม่ใช้งาน' }
])

const form = reactive({
  Id: '',
  listName: '',
  isActive: true,
  textEdit: ''
})

const formDefault = structuredClone(toRaw(form))

const resetForm = () => {
  Object.assign(form, structuredClone(formDefault))
  errorMessage.value = ''
  if (refVForm.value) refVForm.value.resetValidation()
}

const createFormData = () => {
  return {
    contentId: form.Id,
    contentName: form.listName,
    isActive: form.isActive,
    contents: form.textEdit
  }
}

const onFormSubmit = async () => {
  try {
    errorMessage.value = ''

    if (!refVForm.value) return

    const validationError = validateCKEditor(form.textEdit, true, true)
    if (validationError) {
      errorMessage.value = validationError

      return
    }

    const { valid } = await refVForm.value.validate()

    if (valid) {
      if (props.isAdd) await AddForm()
      else if (props.isEdit) await EditForm()
      else throw new Error('Invalid state')

      resetForm()
    }
  } catch (error) {
    console.error('Submission error:', error)
    errorMessage.value = 'ไม่สามารถดำเนินการได้: ไม่มีสถานะที่ถูกต้อง'
  }
}

const AddForm = async () => {
  const confirmed = await Swal.ApproveConfirm()
  if (!confirmed) return

  const formData = createFormData()
  try {
    const response = await callAxios.RequestPost('/UserManagement/AddSystemContent', formData)

    if (response.status === 200) {
      resetForm()
      isDialogVisible.value = false
      emit('update')
    } else {
      Swal.AddFail()
    }
  } catch (error) {
    console.error('Add form error:', error)
    Swal.AddConditionFailText('กรุณากรอกข้อมูลให้ถูกต้อง')
  }
}

const EditForm = async () => {
  const confirmed = await Swal.ApproveConfirm()
  if (!confirmed) return

  const formData = createFormData()
  try {
    const response = await callAxios.RequestPut(
      `/UserManagement/UpdateSystemContent?ContentId=${editId.value}`,
      formData
    )

    if (response.status === 200) {
      resetForm()
      isDialogVisible.value = false
      emit('update')
    } else {
      Swal.EditConditionFail()
    }
  } catch (error) {
    console.error('Edit form error:', error)
    Swal.EditFail()
  }
}

const ViewForm = async () => {
  try {
    Swal.fetchLoadingApi()

    const response = await callAxios.RequestGetById(
      '/UserManagement/GetSystemContentById',
      `?ContentId=${viewId.value}`
    )

    if (response.status === 200) {
      const contentData = response.data.response

      form.Id = contentData.contentId
      form.listName = contentData.contentName
      form.isActive = contentData.isActive
      form.textEdit = contentData.contents
    }
  } catch (error) {
    console.error('View form error:', error)
    Swal.ViewFail()
  } finally {
    Swal.close()
  }
}

watch(
  () => isDialogVisible.value,
  async newVal => {
    errorMessage.value = ''
    if (newVal && (props.isView || props.isEdit)) {
      if (!viewId.value) return

      await ViewForm()
    } else {
      resetForm()
    }
  }
)

const closeDialog = async () => {
  if (props.isView) {
    resetForm()
    isDialogVisible.value = false
    emit('update')

    return
  }
  const response = await Swal.ApproveCancel()
  if (response) {
    resetForm()
    isDialogVisible.value = false
    emit('update')
  }
}
</script>

<template>
  <VCard class="px-4">
    <VForm ref="refVForm" @submit.prevent="onFormSubmit">
      <VRow class="mb-4 mx-2 mt-3 justify-center">
        <VCol cols="12" md="10">
          <VRow>
            <VCol cols="6" md="6">
              <VRow class="mb-2" align="center">
                <VCol cols="12" md="12" class="py-0">
                  <label>
                    ชื่อรายการ :
                    <small class="text-error">*</small>
                  </label>
                </VCol>
                <VCol cols="12" md="12" class="py-0">
                  <VTextField
                    v-model="form.listName"
                    :rules="[requiredValidator]"
                    density="comfortable"
                    placeholder="ตัวอย่าง : ความเป็นมา"
                    :disabled="props.isView"
                    :counter="100"
                    :maxlength="100"
                  />
                </VCol>
              </VRow>
            </VCol>

            <VCol cols="6" md="6">
              <VRow class="mb-2" align="center">
                <VCol cols="12" md="6" class="py-0">
                  <label>
                    สถานะ :
                    <small class="text-error">*</small>
                  </label>
                </VCol>
                <VCol cols="12" md="12" class="py-0">
                  <VAutocomplete
                    v-model="form.isActive"
                    :rules="[booleanValidator]"
                    :items="listStatus"
                    item-title="name"
                    item-value="id"
                    density="comfortable"
                    placeholder="ระบุสถานะ"
                    :disabled="props.isView"
                    class="no-select"
                  />
                </VCol>
              </VRow>
            </VCol>

            <VCol cols="12" md="12">
              <VRow class="mb-2" align="center">
                <VCol cols="12" md="12" class="py-0">
                  <label>
                    เนื้อหา :
                    <small class="text-error">*</small>
                  </label>
                </VCol>
                <VCol cols="12" md="12" class="py-0">
                  <CkEditor
                    :value="form.textEdit"
                    :is-required="props.isAdd || props.isEdit"
                    :disabled="props.isView"
                    @input="form.textEdit = $event"
                  />
                  <span v-if="errorMessage" class="error">
                    {{ errorMessage }}
                  </span>
                </VCol>
              </VRow>
            </VCol>
          </VRow>
        </VCol>
      </VRow>

      <div class="d-flex flex-wrap justify-end mt-5 mb-3">
        <div class="demo-space-x">
          <VBtn
            v-if="!props.isView"
            type="submit"
            color="blue-600"
            rounded="xl"
            prepend-icon="mdi-content-save"
          >
            บันทึก
          </VBtn>
          <VBtn
            color="grey-500"
            style="color: black"
            class="backBtn"
            rounded="xl"
            prepend-icon="mdi-replay"
            @click="closeDialog"
          >
            ย้อนกลับ
          </VBtn>
        </div>
      </div>
    </VForm>
  </VCard>
</template>

<style scoped>
.error {
  color: #ff4d49;
  font-size: 12.77px;
}
</style>
