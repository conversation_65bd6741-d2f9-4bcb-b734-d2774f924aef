<script setup>
const breadcrumbItems = [
  {
    title: 'แบบประเมินความพึงพอใจ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'KBP Survey',
    disabled: false,
    to: ''
  },
  {
    title: 'แบบประเมินความพึงพอใจการให้บริการ',
    disabled: false,
    to: ''
  },
  {
    title: 'รายการ QR code (รายบุคคล)',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <BtnGoBack />
    <QRListTable />
  </div>
</template>
