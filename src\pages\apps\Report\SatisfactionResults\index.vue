<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'

const router = useRouter()
const callAxios = useAxios()
const Swal = useSwal()

const breadcrumbItems = [
  {
    title: 'รายงาน',
    disable: false,
    to: '/apps/report/menu'
  },
  {
    title: 'ผลการประเมินความพึงพอใจ',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const listFilter = reactive([
  {
    name: 'searchDate',
    type: 'dateRange',
    label: 'วันที่ (เริ่มต้น-สิ้นสุด)',
    required: true,
    default: '',
    title: '',
    value: '',
    items: [],
    placeholder: 'เลือกวันที่เริ่มต้น-สิ้นสุด'
  },
  {
    name: 'searchGoverment',
    type: 'select',
    label: 'ศูนย์ราชการสะดวก',
    default: '',
    title: 'name',
    value: 'govermentId',
    items: [{ name: 'ทั้งหมด', govermentId: '' }],
    placeholder: 'เลือกศูนย์ราชการสะดวก'
  },
  {
    name: 'searchAgenciresMainName',
    type: 'select',
    label: 'ชื่อส่วนราชการ (เจ้าภาพหลัก)',
    default: '',
    title: 'name',
    value: 'id',
    items: [{ name: 'ทั้งหมด', id: '' }]
  },
  {
    name: 'searchProvince',
    type: 'select',
    label: 'จังหวัด',
    default: '',
    title: 'name',
    value: 'id',
    items: [{ name: 'ทั้งหมด', id: '' }]
  },
  {
    name: 'searchAgenciresName',
    type: 'select',
    label: 'หน่วยงาน',
    default: '',
    title: 'name',
    value: 'id',
    items: [{ name: 'ทั้งหมด', id: '' }]
  },
  {
    name: 'searchSubcommittee',
    type: 'select',
    label: 'คณะอนุกรรมการ',
    default: '',
    title: 'name',
    value: 'id',
    items: [{ name: 'ทั้งหมด', id: '' }]
  }
])

const filter = reactive({
  searchDate: '',
  searchGoverment: '',
  searchAgenciresMainName: '',
  searchProvince: '',
  searchAgenciresName: '',
  searchSubcommittee: ''
})

const urlReport = ref('')

const adjustYear = dateString => {
  const [year, month, day] = dateString.split('-')

  return `${Number(year) + 543}-${month}-${day}`
}

const GetList = async () => {
  try {
    if (!filter.searchDate) {
      Swal.validateText('กรุณากรอกข้อมูลวันที่')

      return
    }
    const params = new URLSearchParams()

    if (filter.searchDate) {
      const [DateStart, DateEnd] = filter.searchDate
      if (DateStart && DateEnd) {
        params.append('StartDate', adjustYear(DateStart))
        params.append('EndDate', adjustYear(DateEnd))
      }
    }
    if (filter.searchGoverment) params.append('Goverment', filter.searchGoverment)

    if (filter.searchAgenciresMainName) params.append('OrgGroup', filter.searchAgenciresMainName)

    if (filter.searchProvince) params.append('Province', filter.searchProvince)

    if (filter.searchAgenciresName) params.append('OrgStructureName', filter.searchAgenciresName)

    if (filter.searchSubcommittee) params.append('Subcommittee', filter.searchSubcommittee)

    const response = await callAxios.RequestGet(`/Report/ReadReportAssessment?${params.toString()}`)

    if (response.status === 200) urlReport.value = response.data.response.reportLink
    else Swal.AddConditionFailText('เกิดข้อผิดพลาดในการดึงข้อมูลรายงาน')
  } catch (error) {
    console.error('Error fetching report:', error)
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการดึงข้อมูลรายงาน')
  }
}

const getDropdownItems = async () => {
  try {
    // Fetch data for ชื่อส่วนราชการ (เจ้าภาพหลัก)
    let response = await callAxios.RequestGet('/OtherMaster/DepartmentParrentLists?flag=true')
    if (response.status === 200) {
      const DepartmentParrent = listFilter.find(filter => filter.name === 'searchAgenciresMainName')

      if (DepartmentParrent) {
        DepartmentParrent.items = [{ name: 'ทั้งหมด', id: '' }, ...response.data.response]
      }
    }

    // Fetch data for Province
    response = await callAxios.RequestGet('/OtherMaster/ProvinceLists')
    if (response.status === 200) {
      const Province = listFilter.find(filter => filter.name === 'searchProvince')

      if (Province) {
        Province.items = [{ name: 'ทั้งหมด', id: '' }, ...response.data.response]
      }
    }

    // Fetch data for Goverment
    response = await callAxios.RequestGet('/TransactionRegister/GetDropDownGoverment')
    if (response.status === 200) {
      const Subcommittee = listFilter.find(filter => filter.name === 'searchGoverment')

      if (Subcommittee) {
        Subcommittee.items = [{ name: 'ทั้งหมด', govermentId: '' }, ...response.data.response]
      }
    }
  } catch (error) {
    console.error('Error fetching dropdown items:', error)
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการโหลดข้อมูล')
  }
}

// Fetch กรม/หน่วยงาน
const fetchDepartmentChildLists = async (departmentParrentId: string) => {
  if (!departmentParrentId) return

  try {
    const response = await callAxios.RequestGetById(
      '/OtherMaster/DepartmentChildLists',
      `?OrgGroupId=${departmentParrentId}&Id=${filter.searchAgenciresName}&flag=true`
    )

    if (response.status === 200) {
      const DepartmentChildLists = listFilter.find(filter => filter.name === 'searchAgenciresName')

      if (DepartmentChildLists) {
        DepartmentChildLists.items = [{ name: 'ทั้งหมด', id: '' }, ...response.data.response]
      }
    }
  } catch (error) {
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการโหลดข้อมูลอำเภอ')
  }
}

const fetchSubcommitteeLists = async (ProvinceId: string) => {
  if (!ProvinceId) return

  try {
    const response = await callAxios.RequestGetById(
      '/OtherMaster/SubcommitteeProviceLists',
      `?Province=${ProvinceId}`
    )

    if (response.status === 200) {
      const Submmittee = listFilter.find(filter => filter.name === 'searchSubcommittee')

      if (Submmittee) {
        Submmittee.items = [{ name: 'ทั้งหมด', id: '' }, ...response.data.response]
      }
    }
  } catch (error) {
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการโหลดข้อมูลอนุกรรมการ')
  }
}

watch(
  () => filter.searchAgenciresMainName,
  async newDepartmentParrent => {
    filter.searchAgenciresName = ''
    if (newDepartmentParrent !== '') {
      fetchDepartmentChildLists(newDepartmentParrent)
    } else {
      const DepartmentChildLists = listFilter.find(filter => filter.name === 'searchAgenciresName')

      if (DepartmentChildLists) DepartmentChildLists.items = [{ name: 'ทั้งหมด', id: '' }]
    }
  }
)

watch(
  () => filter.searchProvince,
  async newProvince => {
    filter.searchSubcommittee = ''
    if (newProvince !== '') {
      fetchSubcommitteeLists(newProvince)
    } else {
      const Submmittee = listFilter.find(filter => filter.name === 'searchSubcommittee')

      if (Submmittee) Submmittee.items = [{ name: 'ทั้งหมด', id: '' }]
    }
  }
)

onMounted(() => {
  getDropdownItems()
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <FiltersAPI v-model="filter" :fields="listFilter" @submit="GetList" />

    <VCard v-if="urlReport">
      <iframe
        v-if="urlReport"
        :src="urlReport"
        width="100%"
        height="800"
        frameborder="0"
        allowfullscreen
      />
    </VCard>

    <VRow>
      <VCol cols="12" class="d-flex justify-end mt-5 me-4">
        <BtnGoBack />
      </VCol>
    </VRow>
  </div>
</template>
