<script setup lang="ts">
import Footer from '@/layouts/components/Footer.vue'
import { useAppAbility } from '@/plugins/casl/useAppAbility'
import { useAuth } from '@/store/useAuth'
import { useAxios } from '@/store/useAxios'
import { useStatus } from '@/store/useStatus'

import image1 from '@images/iconMenu01.png'
import image2 from '@images/iconMenu02.png'
import image3 from '@images/iconMenu03.png'
import image4 from '@images/iconMenu04.png'
import bgLogin from '@images/pages/bgLogin.png'

import { filterMenuGECCHome } from '@utils'

const callAxios = useAxios()
const callAuth = useAuth()
const AppAbility = useAppAbility()
const router = useRouter()
const authStore = useAuth()

// const token = localStorage.getItem('token')
// console.log('ListMenu จาก store:', token)
const imgPath=[{
  title:'ผู้ดูแลระบบ',
  imgpath:image1,
  path_url:'apps-admin-ManageBanners'
}]
const ListsMenu = ref<any[]>([])



const navigateWithReload = (to: any) => {
  router.push(to).then(() => {
    window.location.reload()
  })
}

onMounted(async () => {
  const mainmenu= authStore.menus.filter(f=>f.parent_id==null);
  mainmenu.forEach(f=>{
    const img=imgPath.find(x=>x.title==f.title);
    ListsMenu.value.push({
      title: f.title,
      toRaw: f.title,
      to: {
        name: img?.path_url
      },
      iconPath: img?.imgpath,
      requiresAdmin: true 
    })
  })
  
})

</script>

<template>
  <HeaderBackoffice />

  <div class="auth-wrapper d-flex">
    <VContainer>
      <VRow class="pt-10 justify-center">
        <VCol v-for="(item, index) in ListsMenu" :key="index" cols="12" sm="6" md="3">
          <div class="menu-item" @click="navigateWithReload(item.to)">
            <img :src="item.iconPath" alt="icon" class="mr-2" />
            <h3>{{ item.title }}</h3>
          </div>
        </VCol>
      </VRow>
      <VRow class="pt-10 justify-center">
        <VCol cols="12 mt-6">
          <Footer />
        </VCol>
      </VRow>
    </VContainer>

    <VImg :src="bgLogin" role="presentation" class="d-none d-md-block auth-footer-mask" />
    <div class="auth-logo" />
  </div>
</template>

<style lang="css">
@import "@public/assets/css/style.css";
@import "@public/assets/css/skin/skin-2.css";
</style>

<style lang="css" scoped>
.v-container {
  position: relative;
  z-index: 1;
}

.menu-item {
  position: relative;
  cursor: pointer;
  text-align: center;
}

.menu-item img {
  transition: all 0.5s;
}

.menu-item:hover img {
  animation: bounce 3000ms infinite;
  transition: all 0.5s;
}

.v-col-sm-6:nth-child(odd) .menu-item {
  margin-block-start: 0;
}

.v-col-sm-6:nth-child(even) .menu-item {
  margin-block-start: 60px;
}

.menu-item ::before {
  position: absolute;
  z-index: -1;
  border-radius: 25px;
  background: #fff;
  block-size: 55%;
  box-shadow: 0 4px 4px rgba(213, 213, 213, 25%);
  content: "";
  inline-size: 100%;
  inset-block-start: 30%;
  inset-inline-start: 0;
  transform: skew(0deg, 5deg);
  transition: all 0.5s;
}

.menu-item:hover ::before {
  background: #f5f5f5;
  transition: all 0.5s;
}

.menu-item h3 {
  color: #2340ba;
  margin-block-start: 15px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 50%);
}

.auth-footer-mask {
  inset-block-end: 0% !important;
}

.v-btn--variant-plain {
  block-size: inherit !important;
}

/**
* Bounce Keyframes Animation
*/
@keyframes bounce {
  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translate3d(0, 0, 0);
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }

  40%,
  43% {
    transform: translate3d(0, -30px, 0);
    transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
  }

  70% {
    transform: translate3d(0, -15px, 0);
    transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
  }

  90% {
    transform: translate3d(0, -4px, 0);
  }
}

.bounce {
  animation-name: bounce;
  transform-origin: center bottom;
}
</style>

<style lang="scss">
@use "@core/scss/template/pages/page-auth.scss";
</style>

<style lang="scss" scoped>
.layout-blank {
  .auth-wrapper {
    min-block-size: calc(var(--vh, 1vh) * 0);

    &::before {
      position: absolute;
      z-index: 1;
      display: block;
      background:
        linear-gradient(
          180deg,
          rgb(var(--v-theme-blue-100)) 0%,
          rgba(255, 255, 255, 0%) 100%
        );
      block-size: 50%;
      content: "";
      inline-size: 100%;
      inset-block-start: 0;
      inset-inline-start: 0;
    }
  }

  .auth-card {
    z-index: 1 !important;
  }
}
</style>

<route lang="yaml">
meta:
  layout: blank
  action: read
  subject: Auth
  redirectIfLoggedIn: true
</route>
