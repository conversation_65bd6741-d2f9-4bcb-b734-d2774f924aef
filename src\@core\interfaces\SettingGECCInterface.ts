export interface IGetRoundTimelineRes {
  condition: string
  createBy: string
  createDatedate: string
  isActive: string
  no: string
  roundId: string
  roundName: string
  sortCreateDate: string
  sortUpdateDate: string
  updateBy: string
  updateDate: string
  year: string
}

export interface GetRoundTimelineByIdRes {
  roundId?: string
  roundTap1?: roundTap1
  roundTap2?: roundTap2
  roundTap3?: roundTap3
}

export interface roundTap1 {
  roundName: string
  year: string
  isActive: string
  formGECCId: string
  warranty: string
  condition: string
}

export interface roundTap2 {
  regisStartDate: string
  regisEndDate: string
  regisDetail: string
  auditStartDate: string
  auditEndDate: string
  auditDetail: string
  announceStartDate: string
  announceEndDate: string
  announceDetail: string
  visitStartDate: string
  visitEndDate: string
  visitDetail: string
  estimateStartDate: string
  estimateEndDate: string
  estimateDetail: string
  announceAuditStartDate: string
  announceAuditEndDate: string
  announceAuditDetail: string
  ceremonyStartDate: string
  ceremonyEndDate: string
  ceremonyDetail: string
}
export interface roundTap3 {
  isAppealRegister: string
  appealRegisterStartDate: string
  appealRegisterEndDate: string
  isAppealReview: string
  appealReviewStartDate: string
  appealReviewEndDate: string
  isCancel: string
  cancelStartDate: string
  cancelEndDate: string
  isCancelVisit: string
  cancelVisitStartDate: string
  cancelVisitEndDate: string
}

export interface IGetSystemEvaluationRes {
  no: string
  systemEvaluationId: string
  systemEvaluationName: string
  year: string
  isActive: string
  createBy: string
  createDate: string
  sortCreateDate: string
  updateBy: string
  updateDate: string
  sortUpdateDate: string
}
export interface IGetEvaluationRes {
  no: string
  evaluationId: string
  evaluationName: string
  evaluationType: boolean
  evaluationShortName: string
  systemEvaluationId: string
  sorting: number
  score: number
  scoreBacis: number
  scoreHigh: number
  isActive: string
  createBy: string
  createDate: string
  sortCreateDate: string
  updateBy: string
  updateDate: string
  sortUpdateDate: string
}

export interface IGetSystemServiceRes {
  no: string
  serviceId: string
  serviceName: string
  serviceType: string
  systemEvaluationId: string
  score: number
  isActive: string
  createBy: string
  createDate: string
  sortCreateDate: string
  updateBy: string
  updateDate: string
  sortUpdateDate: string
}
export interface IGetSubcommitteeRes {
  no: string
  year: string
  subcommitteeId: string
  group: 1
  chairman: string
  area: string[]
  subcommitteeName: string
  isActive: string
  createBy: string
  createDate: string
  sortCreateDate: string
  updateBy: string
  updateDate: string
  sortUpdateDate: string
}

export interface IGetSubcommitteeByIdRes {
  subcommitteeId: string
  area: string[]
  subcommitteeName: string[]
  yearStart: string
  yearEnd: string
  group: number
  chairman: string
}

export interface IGetSystemFormGECCRes {
  no: string
  systemFormGECCName: string
  systemFormGECCId: string
  reportName: string
  year: string
  isActive: string
  createBy: string
  createDate: string
  sortCreateDate: string
  updateBy: string
  updateDate: string
  sortUpdateDate: string
}

export interface IGetFormGECCQuestionRes {
  formGECCId: string
  systemFormGECCId: string
  evaluationId: string
  serviceId: string
  number: string
  detail: string
  guideline: string
  remark: string
  sorting: number
  evaluationLevel: string
  score: number
  isRequest: boolean
  isDetail: boolean
  isAttachment: boolean
  isTarotamane: boolean
  facilities: facilities[]
  facilitiesAttch: facilitiesAttch[]
}
export interface facilities {
  facilitiesId: string
  formGECCId: string
  facilitiesDetail: string
  isFacilities: boolean
  score: number
}
export interface facilitiesAttch {
  facilitiesId: string
  formGECCId: string
  facilitiesMainName: string
  facilitiesName: string
  isImportant: boolean
  isFacilities: boolean
}

export interface GetSystemFormGECCQuestionRes {
  formQuestion: formQuestion[]
  tapMenu: tapMenu[]
}
export interface formQuestion {
  serviceId: string
  serviceName: string
  question: question[]
}
export interface question {
  no: string
  detail: string
  evaluationId: string
  formGECCId: string
}
export interface tapMenu {
  evaluationId: string
  evaluationName: string
  evaluationType: boolean
  isActive: boolean
}
export interface IGetSystemFormGECCQuestionByIdRes {
  formGECCId: string
  evaluationLevel: number
  evaluationId: string
  serviceId: string
  score: number
  number: string
  isRequest: boolean
  isDetail: boolean
  isAttachment: boolean
  isTarotamane: boolean
  detail: string
  guideline: string
  remark: string
  systemFormGECCId: string
  refFiles: []
  facilities: facilities[]
  facilitiesAttch: facilitiesAttch[]
}

export interface IGetSystemSatisfactionRes {
  no: string
  systemSatisfactionId: string
  systemSatisfactionName: string
  reportName: string
  year: string
  isActive: string
  createBy: string
  createDate: string
  sortCreateDate: string
  updateBy: string
  updateDate: string
  sortUpdateDate: string
}

export interface IGetSatisfactionRes {
  detail: string
  satisfaction: satisfaction[]
}

export interface satisfaction {
  no: number
  systemSatisfactionId: string
  satisfactionId: string
  satisfactionName: string
  answerType: string
  subModels: subModels[]
}

export interface subModels {
  no: string
  systemSatisfactionId: string
  satisfactionId: string
  satisfactionName: string
  answerType: string
  subModels: subModels[]
}
