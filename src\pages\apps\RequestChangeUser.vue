<script setup lang="ts">
import { <PERSON>ol, VRow, VTextField } from 'vuetify/lib/components/index.mjs'
import Footer from '@/layouts/components/Footer.vue'
import { useAppAbility } from '@/plugins/casl/useAppAbility'
import { useAuth } from '@/store/useAuth'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import bgLogin from '@images/pages/bgLogin.png'
import {
  confirmedValidator,
  emailValidator,
  passwordValidator,
  requiredValidator
} from '@validators'

// const baseIMGURL = localStorage.baseURL;
const tooltipTitle = 'คำแนะนำในการกำหนดรหัสผ่าน'

const tooltipContent = `
  1. มีความยาว 8-12 ตัวอักษร<br />
  2. ตัวอักษรตัวแรกเป็นตัวพิมพ์ใหญ่ (A-Z)<br />
  3. ตัวอักษรถัดไปสามารถเป็น a-z อย่างน้อย 1 ตัวอักษร อักษรพิเศษ(#,?,!,@,$,%,^,&,*,-)<br />
  อย่างน้อย 1 ตัวอักษร และตัวเลข (0-9) อย่างน้อย 1 ตัวอักษร
`

const callAxios = useAxios()

const Swal = useSwal()
const callAuth = useAuth()
const AppAbility = useAppAbility()
const router = useRouter()
const refVForm = ref()

interface FormType {
  changeUserId: string
  oldEmail: string
  email: string
  password: string
  confirmPassword: string
  year: string
  detail: string
  titleId: number | null
  firstName: string
  lastName: string
  approveDetail: string
  positionName: string
  isApprove: 0
  attachmentName: string
  fileModel: array
}

const form: Ref<FormType & { fileModel: File[] }> = ref({
  changeUserId: '',
  oldEmail: '',
  email: '',
  password: '',
  year: '',
  detail: '',
  titleId: null,
  firstName: '',
  lastName: '',
  approveDetail: '',
  positionName: '',
  isApprove: 0,
  attachmentName: '',
  fileModel: [] // ตรวจสอบว่ากำหนดค่าเริ่มต้น
})

const isDisabled = ref(true)

const CheckOldEmail = async (oldEmail: string) => {
  // ตรวจสอบว่าค่าของ email ไม่ว่าง
  if (!oldEmail || oldEmail.trim() === '') {
    Swal.ApproveConditionclose('กรุณากรอกชื่อผู้ใช้งาน (อีเมลหน่วยงาน)')

    return
  }

  // ตรวจสอบรูปแบบอีเมลโดยใช้ Regular Expression
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailPattern.test(oldEmail)) {
    Swal.ApproveCondition('รูปแบบอีเมลไม่ถูกต้อง')

    return // จบการทำงานของฟังก์ชัน หากรูปแบบอีเมลไม่ถูกต้อง
  }

  try {
    const encodedEmail = encodeURIComponent(oldEmail)

    const response = await callAxios.RequestGet(`Authenticate/CheckOldEmail?Email=${encodedEmail}`)

    if (response.status === 200 && response.data) {
      Swal.ConditionSuccessText(response.data.message)
      isDisabled.value = false
    } else if (response.status === 428 && response.data) {
      Swal.ConditionWarningText(response.data.message)
      isDisabled.value = true
    }
  } catch (error) {
    Swal.AddConditionFailText('ไม่พบข้อมูล')
    isDisabled.value = true
  }
}

const isDisabledOldEmail = ref(true)

const CheckNewEmail = async (email: string) => {
  // ตรวจสอบว่าค่าของ email ไม่ว่าง
  if (!email || email.trim() === '') {
    Swal.ApproveConditionclose('กรุณากรอกชื่อผู้ใช้งาน (อีเมลหน่วยงาน)')

    return
  }

  // ตรวจสอบรูปแบบอีเมลโดยใช้ Regular Expression
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailPattern.test(email)) {
    Swal.ApproveCondition('รูปแบบอีเมลไม่ถูกต้อง')

    return // จบการทำงานของฟังก์ชัน หากรูปแบบอีเมลไม่ถูกต้อง
  }

  try {
    const encodedEmail = encodeURIComponent(email)

    const response = await callAxios.RequestGet(`Authenticate/CheckNewEmail?Email=${encodedEmail}`)

    if (response.status === 200 && response.data) {
      Swal.ConditionSuccessText(response.data.message)
      isDisabledOldEmail.value = false
    } else if (response.status === 428 && response.data) {
      Swal.ConditionWarningText(response.data.message)
      isDisabledOldEmail.value = true
    }
  } catch (error) {
    Swal.AddConditionFailText(error.data.message)
    isDisabledOldEmail.value = true
  }
}

const listTitle = ref([])

const GetTitleDropDown = () => {
  callAxios.RequestGet('/OtherMaster/SystemTitleMasterLists').then(response => {
    if (response.status == 200) listTitle.value = response.data.response
  })
}

GetTitleDropDown()

const isPasswordVisible = ref(false)
const isConFirmPasswordVisible = ref(false)

const listFields = ref([
  {
    field: 'attachmentName',
    header: 'ชื่อไฟล์',
    sortable: true
  },
  {
    field: 'Name',
    header: 'รายการไฟล์',
    sortable: true
  },
  {
    field: 'options',
    header: 'การจัดการ'
  }
])

const ListItemFile = ref<FileItem[]>([])

interface FileItem {
  Id: string
  Name: string
  attachmentName: string
  IsActive: boolean
  URL: string
  fileReference: File
}

// โค้ดเก่าก่อนแก้ไข
// const addFile = async () => {
//   // ตรวจสอบว่าฟิลด์ที่ต้องการไม่ว่างเปล่า
//   if (!form.value.attachmentName || !form.value.attachmentName.trim()) {
//     Swal.ConditionWarningText("กรุณากรอกชื่อไฟล์");
//     return;
//   }
//   if (!form.value.fileModel || form.value.fileModel.length === 0) {
//     Swal.ConditionWarningText("กรุณาเลือกไฟล์");
//     return;
//   }

//   // ตรวจสอบไฟล์ที่อัปโหลด
//   const invalidFiles = form.value.fileModel.filter((file: File) => {
//     const isImage = file.type.startsWith("image/");
//     const isPDF = file.type === "application/pdf";
//     const isUnderSizeLimit = file.size <= 5 * 1024 * 1024; // ขนาดไม่เกิน 5 MB
//     return !(isUnderSizeLimit && (isImage || isPDF));
//   });

//   if (invalidFiles.length > 0) {
//     Swal.ConditionWarningText(
//       "สามารถแนบเอกสารประกอบได้เฉพาะไฟล์สกุล PDF และไฟล์สกุลรูปภาพ ขนาดไม่เกิน 5 MB"
//     );
//     return;
//   }

//   // ยืนยันการเพิ่มไฟล์
//   const confirmResponse = await Swal.ApproveConfirm();
//   if (!confirmResponse) {
//     Swal.ConditionWarningText("ยกเลิกการเพิ่มไฟล์");
//     return;
//   }

//   // เพิ่มข้อมูลลงใน ListItemFile พร้อมเก็บข้อมูลไฟล์จริง
//   form.value.fileModel.forEach((file: File) => {
//     ListItemFile.value.push({
//       Id: Date.now().toString() + Math.random().toString(36).substring(2), // สร้าง Id ไม่ซ้ำ
//       Name: file.name,
//       attachmentName: form.value.attachmentName,
//       IsActive: true,
//       URL: URL.createObjectURL(file), // สร้าง URL สำหรับแสดงตัวอย่างไฟล์
//       fileReference: file, // เก็บไฟล์จริงในฟิลด์นี้
//     });
//   });

//   // เคลียร์ฟอร์ม
//   form.value.attachmentName = "";
//   form.value.fileModel = [];
//   Swal.ConditionSuccessText("เพิ่มไฟล์สำเร็จ");
// };

const addFile = async () => {
  if (!form.value.attachmentName || !form.value.attachmentName.trim()) {
    Swal.ConditionWarningText('กรุณากรอกชื่อไฟล์')

    return
  }
  if (!form.value.fileModel || form.value.fileModel.length === 0) {
    Swal.ConditionWarningText('กรุณาเลือกไฟล์')

    return
  }

  // ตรวจสอบไฟล์ที่ไม่ผ่านเงื่อนไข
  const invalidFiles = form.value.fileModel.filter((file: File) => {
    const isImage = file.type.startsWith('image/')
    const isPDF = file.type === 'application/pdf'
    const isUnderSizeLimit = file.size <= 5 * 1024 * 1024

    return !(isUnderSizeLimit && (isImage || isPDF))
  })

  if (invalidFiles.length > 0) {
    Swal.ConditionWarningText(
      'สามารถแนบเอกสารประกอบได้เฉพาะไฟล์สกุล PDF และไฟล์สกุลรูปภาพ ขนาดไม่เกิน 5 MB'
    )

    return
  }

  // คำนวณขนาดรวมของไฟล์ (ครั้งเดียว)
  const totalSize = form.value.fileModel.reduce((sum: number, file: File) => sum + file.size, 0)

  // ตรวจสอบขนาดไฟล์รวม
  if (totalSize > 50 * 1024 * 1024) {
    Swal.ConditionWarningText('ขนาดไฟล์รวมต้องไม่เกิน 50 MB')

    return
  }

  // ยืนยันการเพิ่มไฟล์
  const confirmResponse = await Swal.ApproveConfirm()
  if (!confirmResponse) {
    Swal.ConditionWarningText('ยกเลิกการเพิ่มไฟล์')

    return
  }

  // เพิ่มข้อมูลไฟล์
  form.value.fileModel.forEach((file: File) => {
    ListItemFile.value.push({
      Id: Date.now().toString() + Math.random().toString(36).substring(2),
      Name: file.name,
      attachmentName: form.value.attachmentName,
      IsActive: true,
      URL: URL.createObjectURL(file),
      fileReference: file
    })
  })

  // ล้างค่าฟอร์ม
  form.value.attachmentName = ''
  form.value.fileModel = []
  Swal.ConditionSuccessText('เพิ่มไฟล์สำเร็จ')
}

const removeFile = async (index: number) => {
  const confirmResponse = await Swal.Approveremove()
  if (!confirmResponse) {
    Swal.ConditionWarningText('ยกเลิกการลบไฟล์')

    return
  }

  // ลบไฟล์ตาม index
  ListItemFile.value.splice(index, 1)

  Swal.ConditionSuccessText('ลบไฟล์สำเร็จ')
}

// ส่งไฟล์ต่อไปยังเซิร์ฟเวอร์
const uploadFiles = async () => {
  try {
    const formData = new FormData()

    // เพิ่มไฟล์ลงใน FormData
    ListItemFile.value.forEach(file => {
      if (file.fileReference) formData.append('files', file.fileReference) // ใช้ฟิลด์ fileReference
    })

    // เรียก API อัปโหลดไฟล์
    const response = await callAxios.RequestUpload('/YourFileUploadEndpoint', formData)

    if (response.status === 200) Swal.ConditionSuccessText('อัปโหลดไฟล์สำเร็จ')
    else Swal.ConditionWarningText('เกิดข้อผิดพลาดในการอัปโหลดไฟล์')
  } catch (error) {
    Swal.ConditionWarningText(error.response?.data?.message || 'เกิดข้อผิดพลาดในการอัปโหลดไฟล์')
  }
}

const OpenFile = (link: string) => {
  if (link) window.open(link, '_blank')
  else Swal.isNotFound()
}

const DownloadFile = (link: string, fileName: string) => {
  if (!link) {
    Swal.isNotFound()

    return
  }

  // สร้าง element `<a>` สำหรับดาวน์โหลดไฟล์
  const a = document.createElement('a')

  a.href = link
  a.download = fileName
  a.style.display = 'none'
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
}

const isCardChangeUser = ref(true)
const isCardChangeUserSuccess = ref(false)

const AddForm = async () => {
  try {
    // ยืนยันการดำเนินการ
    const confirmResponse = await Swal.ApproveConfirm()

    if (confirmResponse) {
      // ตรวจสอบว่า ListItemFile มีไฟล์หรือไม่
      if (ListItemFile.value.length === 0) {
        Swal.ConditionWarningText('กรุณาแนบไฟล์')

        return // หยุดการทำงานของฟังก์ชัน
      }

      // เตรียมข้อมูลจากฟอร์ม
      const data = { ...form.value }

      // ส่งคำขอเปลี่ยนข้อมูลผู้ใช้งาน
      const response = await callAxios.RequestPost('/Authenticate/RequestChangeUser', data)

      if (response.status === 200) {
        isCardChangeUser.value = false
        isCardChangeUserSuccess.value = true

        const { changeUserId, oldEmail } = response.data.response

        // สร้าง JSON data สำหรับ query string
        const jsonData = ListItemFile.value.map(file => ({
          Name: file.Name,
          attachmentName: file.attachmentName
        }))

        // สร้าง FormData สำหรับแนบไฟล์
        const formData = new FormData()

        ListItemFile.value.forEach(file => {
          formData.append('Files', file.fileReference) // สมมติว่า file.fileReference คือไฟล์จริง
        })

        // เพิ่ม JsonData ลงใน FormData
        formData.append('JsonData', JSON.stringify(jsonData))

        // ส่งคำขอแนบไฟล์
        const transactionResponse = await callAxios.RequestUpload(
          `/Authenticate/TransactionChangeUserAttachment?ChangeUserId=${changeUserId}&OldEmail=${encodeURIComponent(
            oldEmail
          )}`,
          formData
        )

        if (transactionResponse.status === 200) {
          Swal.ConditionSuccessText('ส่งคำขอเปลี่ยนแปลงบัญชีผู้ใช้เรียบร้อย')

          // ทำขั้นตอนถัดไป เช่น เคลียร์ฟอร์มหรือเปลี่ยนหน้า
        } else {
          Swal.ConditionWarningText(
            transactionResponse.data?.message || 'เกิดข้อผิดพลาดในขั้นตอนแนบไฟล์'
          )
        }
      } else {
        Swal.AddConditionFailText(response.data?.message || 'Request failed. Please try again.')
      }
    }
  } catch (error) {
    Swal.AddConditionFailText(
      error.response?.data?.message || 'Bad request. Please check your input and try again.'
    )
  }
}

const onSumbit = () => {
  refVForm.value?.validate().then(({ valid: isValid }) => {
    if (isValid) AddForm()
  })
}

const onInputEmail = (event: Event, field: 'oldEmail' | 'email') => {
  const input = (event.target as HTMLInputElement).value

  // ลบตัวอักษรภาษาไทยออก
  const sanitizedInput = input.replace(/[\u0E00-\u0E7F]/g, '')

  form.value[field] = sanitizedInput
}

const isRecaptchaVerified = ref(false)

const onVerify = (token: string) => {
  console.log('reCAPTCHA verified! Token:', token)
  isRecaptchaVerified.value = true // เปลี่ยนสถานะเมื่อ Verify สำเร็จ
}

const goBack = async () => {
  router.go(-1)
}
</script>

<template>
  <HeaderPublic />

  <div class="auth-wrapper d-flex">
    <VContainer>
      <div class="mt-5 text-center">
        <h2>ขอเปลี่ยนแปลงอีเมลหน่วยงาน</h2>
      </div>
      <VRow class="pt-10">
        <VCol cols="12" justify="center">
          <VCard v-if="isCardChangeUser" class="auth-card login-card pa-2 pt-7">
            <VCardText>
              <VForm ref="refVForm" @submit.prevent="onSumbit">
                <VRow class="align-center">
                  <VCol cols="12" md="6" class="py-0">
                    <label>
                      ชื่อผู้ใช้งาน (อีเมลหน่วยงานเดิม) :
                      <small class="text-error">*</small>
                    </label>
                    <VTextField
                      v-model="form.oldEmail"
                      density="compact"
                      :counter="100"
                      :maxlength="100"
                      :readonly="!isDisabled"
                      :rules="[requiredValidator, emailValidator]"
                      placeholder="ตัวอย่าง : <EMAIL>"
                      :bg-color="!isDisabled ? 'grey-secondary' : undefined"
                      @input="(event: any) => onInputEmail(event, 'oldEmail')"
                    />
                  </VCol>

                  <VCol cols="12" md="6" class="py-0">
                    <!-- login button -->
                    <VBtn
                      color="error-200"
                      class="text-dark"
                      type="button"
                      :disabled="!isDisabled"
                      @click="CheckOldEmail(form.oldEmail)"
                    >
                      ตรวจสอบอีเมล
                      <VTooltip open-delay="500" location="top" activator="parent">
                        ตรวจสอบอีเมล
                      </VTooltip>
                    </VBtn>
                  </VCol>
                </VRow>
                <VRow class="align-center">
                  <VCol cols="12" md="6" class="py-0">
                    <label>
                      ชื่อผู้ใช้งาน (อีเมลหน่วยงานใหม่) :
                      <small class="text-error">*</small>
                    </label>
                    <VTextField
                      v-model="form.email"
                      density="compact"
                      :counter="100"
                      :maxlength="100"
                      :readonly="isDisabled"
                      :bg-color="isDisabled ? 'grey-secondary' : undefined"
                      :rules="isDisabled ? [] : [requiredValidator, emailValidator]"
                      placeholder="ตัวอย่าง : <EMAIL>"
                      @input="(event: any) => onInputEmail(event, 'email')"
                    />
                  </VCol>

                  <VCol cols="12" md="6" class="py-0">
                    <!-- login button -->
                    <VBtn
                      color="error-200"
                      class="text-dark"
                      type="button"
                      @click="CheckNewEmail(form.email)"
                    >
                      ตรวจสอบอีเมล
                      <VTooltip open-delay="500" location="top" activator="parent">
                        ตรวจสอบอีเมล
                      </VTooltip>
                    </VBtn>
                  </VCol>
                </VRow>
                <VRow class="align-start">
                  <VCol cols="12" md="6" class="py-0 mb-4">
                    <label>
                      รหัสผ่าน:
                      <small class="text-error">*</small>
                    </label>
                    <VTextField
                      v-model="form.password"
                      density="compact"
                      :type="isPasswordVisible ? 'text' : 'password'"
                      :append-inner-icon="
                        form.password
                          ? isPasswordVisible
                            ? 'mdi-eye-off-outline'
                            : 'mdi-eye-outline'
                          : ''
                      "
                      :counter="20"
                      :maxlength="20"
                      :readonly="isDisabledOldEmail"
                      :bg-color="isDisabledOldEmail ? 'grey-secondary' : undefined"
                      :rules="isDisabledOldEmail ? [] : [passwordValidator]"
                      @click:append-inner="isPasswordVisible = !isPasswordVisible"
                    />
                    <span class="guidTooltip">
                      <small class="text-error">ดูคำแนะนำในการกำหนดรหัสผ่าน</small>
                      <TooltipFormDialog :title="tooltipTitle" :content="tooltipContent" />
                    </span>
                  </VCol>

                  <VCol cols="12" md="6" class="py-0">
                    <label>
                      ยืนยันรหัสผ่าน:
                      <small class="text-error">*</small>
                    </label>
                    <VTextField
                      v-model="form.confirmPassword"
                      density="compact"
                      :type="isConFirmPasswordVisible ? 'text' : 'password'"
                      :append-inner-icon="
                        form.confirmPassword
                          ? isConFirmPasswordVisible
                            ? 'mdi-eye-off-outline'
                            : 'mdi-eye-outline'
                          : ''
                      "
                      :counter="20"
                      :maxlength="20"
                      :readonly="isDisabledOldEmail"
                      :bg-color="isDisabledOldEmail ? 'grey-secondary' : undefined"
                      :rules="
                        isDisabledOldEmail
                          ? []
                          : [
                              requiredValidator,
                              () => confirmedValidator(form.confirmPassword, form.password)
                            ]
                      "
                      @click:append-inner="isConFirmPasswordVisible = !isConFirmPasswordVisible"
                    />
                  </VCol>

                  <VCol cols="12" md="2" class="py-0">
                    <label>
                      คำนำหน้า :
                      <small class="text-error">*</small>
                    </label>
                    <VAutocomplete
                      v-model="form.titleId"
                      :rules="isDisabledOldEmail ? [] : [requiredValidator]"
                      :items="listTitle"
                      item-title="name"
                      item-value="id"
                      density="compact"
                      :readonly="isDisabledOldEmail"
                      :bg-color="isDisabledOldEmail ? 'grey-secondary' : undefined"
                      no-data-text="ไม่มีข้อมูล"
                      placeholder="ตัวอย่าง : นาย"
                    />
                  </VCol>
                  <VCol cols="12" md="4" class="py-0">
                    <label>
                      ชื่อ :
                      <small class="text-error">*</small>
                    </label>
                    <VTextField
                      v-model="form.firstName"
                      :counter="50"
                      :maxlength="50"
                      density="compact"
                      type="text"
                      :readonly="isDisabledOldEmail"
                      :bg-color="isDisabledOldEmail ? 'grey-secondary' : undefined"
                      :rules="isDisabledOldEmail ? [] : [requiredValidator]"
                      placeholder="ตัวอย่าง : สมศักดิ์"
                    />
                  </VCol>

                  <VCol cols="12" md="6" class="py-0">
                    <label>
                      นามสกุล :
                      <small class="text-error">*</small>
                    </label>
                    <VTextField
                      v-model="form.lastName"
                      :counter="50"
                      :maxlength="50"
                      density="compact"
                      type="text"
                      :readonly="isDisabledOldEmail"
                      :bg-color="isDisabledOldEmail ? 'grey-secondary' : undefined"
                      :rules="isDisabledOldEmail ? [] : [requiredValidator]"
                      placeholder="ตัวอย่าง : คงผาศักดิ์"
                    />
                  </VCol>
                  <VCol cols="12" md="12" class="py-0">
                    <label>
                      ตำแหน่ง :
                      <small class="text-error">*</small>
                    </label>
                    <VTextField
                      v-model="form.positionName"
                      density="compact"
                      :counter="100"
                      :maxlength="100"
                      type="text"
                      :readonly="isDisabledOldEmail"
                      :bg-color="isDisabledOldEmail ? 'grey-secondary' : undefined"
                      :rules="isDisabledOldEmail ? [] : [requiredValidator]"
                      placeholder="ตัวอย่าง : ชำนาญการพิเศษ"
                    />
                  </VCol>

                  <VCol cols="12" md="12" class="">
                    <label class="text-h6">แนบไฟล์ประกอบการพิจารณา</label>
                  </VCol>

                  <VCol cols="12" md="6" class="mb-4">
                    <label>
                      ชื่อไฟล์ :
                      <small class="text-error">*</small>
                    </label>
                    <VTextField
                      v-model="form.attachmentName"
                      :counter="100"
                      :maxlength="100"
                      density="compact"
                      type="text"
                      :readonly="isDisabledOldEmail"
                      :bg-color="isDisabledOldEmail ? 'grey-secondary' : undefined"
                      placeholder="ตัวอย่าง : เอกสารประกอบการพิจารณา"
                    />
                  </VCol>

                  <VCol cols="12" md="4" class="mb-4">
                    <label>
                      แนบไฟล์ :
                      <small class="text-error">*</small>
                    </label>
                    <VFileInput
                      v-model="form.fileModel"
                      density="compact"
                      multiple
                      append-inner-icon="mdi-tray-arrow-up"
                      :readonly="isDisabledOldEmail"
                      :bg-color="isDisabledOldEmail ? 'grey-secondary' : undefined"
                    />
                    <span class="guidTooltip">
                      <small class="text-error">
                        (สามารถแนบเอกสารประกอบได้เฉพาะไฟล์สกุล PDF และไฟล์สกุลรูปภาพ ขนาดไม่เกิน 5
                        MB)
                      </small>
                    </span>
                  </VCol>

                  <VCol cols="12" md="2" class="mb-4">
                    <VBtn
                      color="blue-600 mt-md-4"
                      :readonly="isDisabledOldEmail"
                      :bg-color="isDisabledOldEmail ? 'grey-secondary' : undefined"
                      @click="addFile"
                    >
                      บันทึก
                    </VBtn>
                  </VCol>

                  <VCol v-if="ListItemFile.length > 0" cols="12" md="12" class="py-0 mb-4">
                    <AppDataTable :columns="listFields" :value="ListItemFile" :paginator="false">
                      <template #options="slotProps">
                        <div class="text-center">
                          <IconBtn
                            @click="DownloadFile(slotProps.data.URL, slotProps.data.attachmentName)"
                          >
                            <VIcon icon="mdi-download-outline" />
                            <VTooltip open-delay="500" location="top" activator="parent">
                              ดาวน์โหลดไฟล์
                            </VTooltip>
                          </IconBtn>
                          <IconBtn @click="removeFile(slotProps.index)">
                            <VIcon icon="mdi-delete-outline" />
                            <VTooltip open-delay="500" location="top" activator="parent">
                              ลบข้อมูล
                            </VTooltip>
                          </IconBtn>
                        </div>
                      </template>
                    </AppDataTable>
                    <!-- {{ ListItemFile }} -->
                  </VCol>

                  <VCol cols="12" md="12" class="py-0">
                    <label>อื่น ๆ ระบุ (ถ้ามี) :</label>
                    <VTextarea
                      v-model="form.detail"
                      density="compact"
                      :counter="3000"
                      :maxlength="3000"
                      type="text"
                      :readonly="isDisabledOldEmail"
                      :bg-color="isDisabledOldEmail ? 'grey-secondary' : undefined"
                      placeholder="ตัวอย่าง : ชำนาญการพิเศษ"
                    />
                  </VCol>

                  <VCol cols="12">
                    <div class="text-center" role="presentation">
                      <Recaptcha @verify="onVerify" />
                    </div>
                  </VCol>
                  <VCol cols="12" class="d-flex flex-wrap justify-center">
                    <div class="demo-space-x">
                      <VBtn type="submit" color="blue-600" :disabled="!isRecaptchaVerified">
                        ส่งคำขอ
                      </VBtn>
                      <VBtn color="error-300" @click="goBack">ยกเลิก</VBtn>
                    </div>
                  </VCol>
                </VRow>
              </VForm>
            </VCardText>
          </VCard>

          <VCard v-if="isCardChangeUserSuccess" class="auth-card login-card pa-2 pt-7 mb-5">
            <VCardText class="text-center">
              <img src="@images/subscribe.png" alt="RegisterSuccess" />
              <h3 class="text-h6">
                ส่งคำขอเปลี่ยนแปลงบัญชีผู้ใช้เรียบร้อย
                <br />
                อยู่ระหว่างพิจารณาคำขอ กรุณารอการตอบกลับจาก E-mail
              </h3>
              <h4 class="text-h6">ขอบคุณค่ะ/ครับ</h4>

              <!-- <p>ส่งลิงก์ยืนยันซ้ำอีกครั้ง {{ formattedTime }}</p> -->

              <VBtn color="blue-600 mt-4" @click="goBack">เสร็จสิ้น</VBtn>
            </VCardText>
          </VCard>
        </VCol>

        <VCol cols="12">
          <Footer />
        </VCol>
      </VRow>
    </VContainer>

    <VImg :src="bgLogin" role="presentation" class="d-none d-md-block auth-footer-mask" />
    <div class="auth-logo" />
    <GovWebsiteAccess />
  </div>
</template>

<style lang="css">
@import '@public/assets/css/style.css';
@import '@public/assets/css/skin/skin-2.css';
</style>

<style lang="css" scoped>
.v-container {
  position: relative;
  z-index: 1;
}

.auth-footer-mask {
  inset-block-end: 0% !important;

  /* เปลี่ยนค่าเมื่อหน้าจอเล็กลง */
}
</style>

<style lang="scss">
@use '@core/scss/template/pages/page-auth.scss';
</style>

<style lang="scss" scoped>
.layout-blank {
  .auth-wrapper {
    min-block-size: calc(var(--vh, 1vh) * 0);

    &::before {
      position: absolute;

      /* Example background */
      z-index: 1;
      display: block;
      background: linear-gradient(
        180deg,
        rgb(var(--v-theme-blue-100)) 0%,
        rgba(255, 255, 255, 0%) 100%
      );
      block-size: 50%;
      content: '';
      inline-size: 100%;
      inset-block-start: 0;
      inset-inline-start: 0;

      /* Make sure it's behind other content */
    }
  }

  .auth-card {
    z-index: 1 !important;
  }
}
</style>

<route lang="yaml">
meta:
  layout: blank
  action: read
  subject: Auth
  redirectIfLoggedIn: true
</route>
