<script setup lang="ts">
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { VCol } from 'vuetify/lib/components/index.mjs'
import ChartTreatment from '../../admin/ManageDashboardReports/Chart/ChartTreatment.vue'
import BtnGoBack from '@/@core/components/button/BtnGoBack.vue'
import { breadcrumbItems } from '@/plugins/page/type'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'

import ChartDoughnutCondition from '@/@core/components/Chart/ChartDoughnutCondition.vue'
import GoogleMapBubble from '@/@core/components/Public/GoogleMapBubble.vue'
import image4 from '@images/report/HNY 1.png'

// Router and Axios instances
const route = useRoute()
const router = useRouter()
const callAxios = useAxios()
const Swal = useSwal()

const SystemDashboardId = ref('8a622573-4026-4351-8843-b27d400685f6')

const currentPage = ref(1)
const rowPerPage = ref(10)
const totalCount = ref(0)

const year = ref([])

const filter = reactive({
  GovermentName: ''
})

// const GovermentName = ref<any[]>([]);
const GovermentName = ref([{ name: 'ทั้งหมด', govermentId: '' }])
const ProvinceName = ref<any[]>([])
const Province = ref(null)
const CertificateName = ref('')
const inProcess = ref(false)
const initialValue = ref(false)
const status = ref<StatusItem[]>([])
const GECCName = ref(null)
const ListItem = ref([])

// State Variables
const statusSwitch = ref(true)
const statusChart = ref(true)

const statusSwitch2 = ref(true)
const statusChart2 = ref(true)

// Breadcrumbs data
const breadcrumbItems = [
  { title: 'รายงาน', disabled: false, to: '/apps/report/menu' },
  {
    title: 'Dashboard ผลประเมินการรักษามาตรฐาน GECC',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

// Data table fields
const listFields = ref([
  {
    field: 'no',
    header: 'ลําดับ',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'year',
    header: 'ปี พ.ศ.',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'GECCCode',
    header: 'รหัสตรา GECC',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'statusGoverment',
    header: 'ระดับการรับรอง',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'GECCName',
    header: 'ชื่อศูนย์ราชการสะดวก',
    sortable: true,
    style: { textAlign: 'left' }
  },
  {
    field: 'Examination',
    header: 'ผลการตรวจ',
    sortable: true,
    style: { textAlign: 'center' }
  }
])

interface StatusItem {
  label: string
  value: any
  image: string
}

const fetchReportData = async () => {
  Swal.fetchLoadingApi()
  inProcess.value = true

  const currentYearThai = new Date().getFullYear() + 543

  const [StartYear, EndYear] =
    Array.isArray(year.value) && year.value.length > 0
      ? year.value
      : [currentYearThai, currentYearThai]

  try {
    const params = new URLSearchParams({
      SystemDashboardId: SystemDashboardId.value?.toString() || '',
      Page: currentPage.value.toString(),
      PageSize: rowPerPage.value.toString()
    })

    if (StartYear) params.append('StartYear', StartYear.toString())
    if (EndYear) params.append('EndYear', EndYear.toString())
    if (GECCName.value || GECCName.value === 0)
      params.append('govermentId', GECCName.value.toString())
    if (Province.value || Province.value === 0) params.append('Province', Province.value)

    const response = await callAxios.RequestGet(
      `/Keyword/GetDashboardAssessment?${params.toString()}`
    )

    if (response.status === 200) {
      const data = response.data.response

      console.log('asdadad', data)

      // Update Status
      status.value = [
        {
          value: data.assessmentPass || 0,
          label: 'ผ่าน',
          image: ''
        },
        {
          label: '',
          value: '',
          image: image4
        },
        {
          label: 'ไม่ผ่าน',
          value: data.assessmentNot || 0,
          image: ''
        }
      ]

      totalCount.value = response.data.count

      if (Object.keys(data).length !== 0 && typeof data === 'object') {
        chartII.value = data.chartI
        chartIII.value = data.chartII
        chartBarI.value = data.chartbarI
        chartBarII.value = data.chartbarII
        DataMapGECC.value = data.mapGECC
      } else {
        chartII.value = {}
        chartIII.value = {}
        chartBarI.value = {}
        chartBarII.value = {}
        DataMapGECC.value = {}
        ListItem.value = []
        console.log('Not Found')
      }

      if (data.chartbarI.isActive) statusChart.value = true
      else if (data.chartI.isActive) statusChart.value = false

      if (data.chartbarII.isActive) statusChart2.value = true
      else if (data.chartII.isActive) statusChart2.value = false

      console.log('status1', statusChart.value)

      ListItem.value = Array.isArray(data.dataCertificate)
        ? data.dataCertificate.map((item: any) => ({
            no: item.no,
            year: item.year,
            GECCCode: item.geccCode,
            statusGoverment: item.certificateName,
            GECCName: item.govermentName,
            Examination: item.standardAuditResultId
          }))
        : []
    } else {
      throw new Error(`Unexpected response status: ${response.status}`)
    }
  } catch (error) {
    console.error('Error fetching data:', error)
    Swal.AddFail()
  } finally {
    inProcess.value = false
    initialValue.value = true
    Swal.close()
  }
}

const getDropdownItems = async () => {
  try {
    const response = await callAxios.RequestGet('/TransactionRegister/GetDropDownProvince')

    if (response.status === 200) {
      ProvinceName.value = [
        { provinceName: 'ทั้งหมด', provinceCode: '' },
        ...response.data.response
      ]
    }
  } catch (error) {
    console.error('Error fetching dropdown items:', error)
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการโหลดข้อมูล')
  }
}

const updateTypeFirst = async (statusIsActive: boolean) => {
  console.log('Updating TypeFirst, status:', statusIsActive)

  const payload = {
    systemDashboardId: SystemDashboardId.value,
    typeFirst: !statusIsActive,
    typeSecond: statusIsActive
  }

  try {
    const response = await callAxios.RequestPut(
      `Keyword/UpdateChart?systemDashboardId=${payload.systemDashboardId}&typeFirst=${payload.typeFirst}&typeSecond=${payload.typeSecond}`,
      payload
    )

    if (response.status === 200) {
      Swal.AddSuccess()
      console.log('TypeFirst updated successfully', payload)
    } else {
      throw new Error(`Unexpected response status: ${response.status}`)
    }
  } catch (error: any) {
    console.error('Error updating TypeFirst:', error)
    Swal.AddFailText(error.data.message)
  }
}

const updateTypeSecond = async (statusIsActive2: boolean) => {
  console.log('Updating TypeSecond, status:', statusIsActive2)

  const payload = {
    systemDashboardId: SystemDashboardId.value,
    typeSecond: !statusIsActive2,
    typeFirst: statusIsActive2
  }

  try {
    const response = await callAxios.RequestPut(
      `Keyword/UpdateChartSec?systemDashboardId=${payload.systemDashboardId}&typeSecond=${payload.typeSecond}&typeFirst=${payload.typeFirst}`,
      payload
    )

    if (response.status === 200) {
      Swal.AddSuccess()
      console.log('TypeSecond updated successfully', payload)
    } else {
      throw new Error(`Unexpected response status: ${response.status}`)
    }
  } catch (error: any) {
    console.error('Error updating TypeSecond:', error)
    Swal.AddFailText(error.data.message)
  }
}

watch(statusSwitch, newVal => {
  statusChart.value = !statusChart.value
  updateTypeFirst(statusChart.value)
})

watch(statusSwitch2, newVal => {
  console.log('statusSwitch2', statusChart2.value)
  statusChart2.value = !statusChart2.value
  updateTypeSecond(statusChart2.value)
})

watch(GECCName, (newVal, oldVal) => {
  console.log('GECCName updated:', { newVal, oldVal })
  fetchReportData()
})

watch(Province, async (newProvince, oldProvince) => {
  console.log('Province updated:', { newProvince, oldProvince })

  // Reset GovermentName to default
  GECCName.value = ''
  GovermentName.value = [{ name: 'ทั้งหมด', govermentId: '' }]
  if (newProvince) {
    try {
      const response = await callAxios.RequestGet(
        `/Keyword/GetDropDownGovermentAndOrgGroupByProvince?flag=true&ProvinceCode=${newProvince}`
      )

      if (response.status === 200) {
        GovermentName.value = [{ name: 'ทั้งหมด', govermentId: '' }, ...response.data.response]
        console.log('Updated GovermentName:', GovermentName.value)
      }
    } catch (error) {
      Swal.fire('เกิดข้อผิดพลาด', 'เกิดข้อผิดพลาดในการโหลดข้อมูลศูนย์ราชการ', 'error')
    }
  } else {
    console.log('No province selected, GovermentName reset to default.')
  }

  // Fetch updated report data
  fetchReportData()
})

watch(year, (newVal, oldVal) => {
  console.log('Year updated:', { newVal, oldVal })

  // if (!Array.isArray(newVal) || newVal.length === 0) {
  //   return;
  // }
  fetchReportData()
})

const onPageChange = (event: { page: number; rows: number }) => {
  currentPage.value = event.page + 1
  rowPerPage.value = event.rows
  fetchReportData()
}

onMounted(() => {
  fetchReportData()
  getDropdownItems()
})

const selectedRank = ref(0)

const chartBarI = ref({
  header: '',
  isActive: true,
  conditional: true,
  bar: {
    series: [],
    label: []
  },
  summary: ''
})

const chartBarII = ref({
  header: '',
  isActive: true,
  conditional: false,
  bar: {
    series: [],
    label: []
  },
  summary: ''
})

const chartII = ref({
  header: '',
  isActive: true,
  conditional: true,
  donut: {
    series: [],
    label: []
  },
  summary: ''
})

const chartIII = ref({
  header: '',
  isActive: true,
  conditional: false,
  donut: {
    series: [],
    label: []
  },
  summary: ''
})

const DataMapGECC = ref([
  {
    govermentName: '',
    lat: '',
    lng: '',
    detail: [],
    condition: true
  }
])

console.log('check here', DataMapGECC)
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems" class="mb-4">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <VRow>
      <VCol cols="12" md="8">
        <VCard class="d-flex align-center justify-center text-center card-container">
          <VRow class="top-row">
            <VCol
              v-for="(stat, index) in status"
              :key="index"
              cols="12"
              sm="4"
              md="4"
              class="d-flex flex-column align-center justify-center"
            >
              <VRow class="image-container">
                <VCol>
                  <img :src="stat.image" class="overlapping-image" />
                </VCol>
              </VRow>
              <VRow>
                <VCol cols="12" md="6" class="text-end align-center d-flex">
                  <div class="text-h2 text-value">
                    {{ stat.value }}
                  </div>
                </VCol>
              </VRow>
              <VRow class="align-center mt-0">
                <VCol cols="12">
                  <div class="text-start d-flex align-center text-h4 text-label">
                    {{ stat.label }}
                  </div>
                </VCol>
              </VRow>
            </VCol>
          </VRow>
        </VCard>
        <VCol col="12" md="12" class="px-0 pb-smallscreen">
          <VRow>
            <VCol col="6" md="6">
              <VCard class="h-100">
                <template v-if="statusChart">
                  <ChartTreatment class="mt-4" :chart-data="chartBarI" />
                </template>
                <template v-else>
                  <ChartDoughnutCondition class="mt-4" :chart-data="chartII" />
                </template>
              </VCard>
            </VCol>
            <VCol col="6">
              <VCard class="h-100 pb-smallscreen pt-smallscreen">
                <template v-if="statusChart2">
                  <ChartTreatment class="mt-4" :chart-data="chartBarII" />
                </template>
                <template v-else>
                  <ChartDoughnutCondition class="mt-4" :chart-data="chartIII" />
                </template>
              </VCard>
            </VCol>
          </VRow>
          <VCard class="mt-5">
            <AppDataTableAPI
              :columns="listFields"
              :value="ListItem"
              :paginator="totalCount"
              :total-records="totalCount"
              :header-no="false"
              :scrollable="true"
              @page="onPageChange"
            >
              <template #Examination="slotProps">
                <template v-if="slotProps.data.Examination === '0'">
                  {{ null }}
                </template>
              </template>
            </AppDataTableAPI>
          </VCard>
        </VCol>
      </VCol>
      <VCol cols="12" md="4" class="pt-smallscreen">
        <VCard class="py-5 pb-6 px-5 mb-6 mb-smallscreen">
          <VRow>
            <VCol cols="12">
              <label>ปี :</label>
              <DateTimePicker
                v-model="year"
                format="YYYY"
                year-picker
                range
                no-data-text="ไม่มีข้อมูล"
                density="default"
                placeholder="พ.ศ."
                bg-color="primary"
                rounded
                :icon-style-flag="true"
              />
            </VCol>

            <VCol cols="12">
              <label>จังหวัด :</label>
              <VAutocomplete
                v-model="Province"
                :items="ProvinceName"
                item-title="provinceName"
                item-value="provinceCode"
                no-data-text="ไม่มีข้อมูล"
                density="default"
                :persistent-placeholder="true"
                placeholder="เลือกจังหวัด"
                bg-color="primary"
                rounded
              />
            </VCol>
            <VCol cols="12">
              <label>ศูนย์ราชการสะดวก :</label>
              <VAutocomplete
                v-model="GECCName"
                :items="GovermentName"
                item-title="name"
                item-value="govermentId"
                no-data-text="ไม่มีข้อมูล"
                density="default"
                :persistent-placeholder="true"
                placeholder="เลือกศูนย์ราชการสะดวก"
                bg-color="primary"
                rounded
              />
            </VCol>
          </VRow>
        </VCard>
        <Card class="mt-6">
          <GoogleMapBubble class="fixed-height-map" height="1160px" :data-map-gecc="DataMapGECC" />
        </Card>
      </VCol>
    </VRow>

    <div class="d-flex align-center justify-end flex-wrap gap-4">
      <BtnGoBack />
    </div>
  </div>
</template>

<style scoped>
::v-deep(.v-input input::placeholder) {
  color: white !important;
}

.text-value,
.text-label {
  color: white;
}

.card-container {
  position: relative;
  overflow: visible;
  border-radius: 2.1875rem;
  background: linear-gradient(to right, #0c235f, #1846bf);
}

.text-h3 {
  margin-block-end: 0;
  margin-block-start: 0;
}

.v-col.v-col-12.text-end {
  padding: initial;
}

/* Default: Large Screens (Desktop) */
.overlapping-image {
  position: relative;
  overflow: hidden;
  block-size: auto;
  inline-size: 306px;
  inset-block-start: -8px;
  margin-block: -42px -50px;
  object-fit: cover;
}

/* For Screens Between 1490px and 1600px (Notebooks, iPads) */
.rank-image {
  block-size: 70px;
  inline-size: 70px;
  margin-inline-end: 10px;
  object-fit: contain;
}

.hover-card {
  transition: all 0.3s ease;
}

.hover-card.hover-active {
  border: 2px solid #42a5f5;
  box-shadow: 0 4px 15px rgba(66, 165, 245, 50%);
  transform: scale(1.02);
}

@media (max-width: 959px) {
  .overlapping-image {
    position: relative;
    overflow: hidden;
    block-size: auto;
    inline-size: 306px;
    inset-block-start: -8px;
    margin-block: -42px -50px;
    object-fit: cover;
  }
  .pt-smallscreen {
    padding-top: 0 !important;
  }
  .pb-smallscreen {
    padding-bottom: 8px !important;
  }

  .mb-smallscreen {
    margin-bottom: 20px !important;
  }
}

@media (max-width: 627px) {
  .overlapping-image {
    position: relative;
    overflow: hidden;
    block-size: auto;
    inline-size: 306px;
    inset-block-start: -9px;
    margin-block: -42px -50px;
    object-fit: cover;
  }
}

@media (max-width: 600px) {
  .overlapping-image {
    display: none !important;
  }
  .v-row .image-container {
    display: none !important;
  }
  .top-row > :nth-child(2) {
    display: none !important;
  }
  .top-row > :nth-child(3) {
    padding-top: 0 !important;
  }
  .top-row {
    justify-content: space-evenly !important;
  }
  .text-label {
    padding-bottom: 36px !important;
  }
}
</style>
