<script setup>
import { useAxios } from '@/store/useAxios'
import { requiredValidator } from '@validators'

const route = useRoute()
const router = useRouter()
const paramsId = ref(Number(route.params.id))
const callAxios = useAxios()
const refVForm = ref()

const form = ref({
  evaluationName: null,
  email: '',
  subject: '',
  message: ''
})

const itemsKPBFormDDL = ref([])

const breadcrumbItems = [
  {
    title: 'แบบประเมินความพึงพอใจ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'KBP Survey',
    disabled: false,
    to: ''
  },
  {
    title: 'แบบประเมินความพึงพอใจของผู้มีส่วนได้ส่วนเสีย',
    disabled: false,
    to: ''
  },
  {
    title: 'ส่งแบบประเมินความพึงพอใจของผู้มีส่วนได้ส่วนเสีย',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

function goBack() {
  router.go(-1)
}

const GetKPBFormDDL = () => {
  const endpoint = '/EvaluationForm/GetKPBFormDDL'

  callAxios.RequestGet(endpoint).then(response => {
    if (response.status == 200) itemsKPBFormDDL.value = response.data.response
  })
}

const GetKBPHistorySendEmailById = () => {
  const endpoint = `/EvaluationForm/GetKBPHistorySendEmailById?Id=${paramsId.value}`

  callAxios.RequestGet(endpoint).then(response => {
    if (response.status == 200) form.value = response.data.response
  })
}

onMounted(() => {
  GetKPBFormDDL()
  GetKBPHistorySendEmailById()
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <VCard class="mb-5">
      <VCardText>
        <VForm ref="refVForm">
          <VRow>
            <VCol cols="12">
              <VRow class="mb-2" align="center">
                <VCol cols="12" md="3" class="text-lg-end">
                  <label>
                    เลือกแบบประเมิน :
                    <small class="text-error">*</small>
                  </label>
                </VCol>
                <VCol cols="12" md="9">
                  <VTextField v-model="form.evaluationName" :rules="[requiredValidator]" readonly />
                </VCol>
              </VRow>
            </VCol>
            <VCol cols="12">
              <VRow class="mb-2" align="center">
                <VCol cols="12" md="3" class="text-lg-end">
                  <label>
                    เรื่อง :
                    <small class="text-error">*</small>
                  </label>
                </VCol>
                <VCol cols="12" md="9">
                  <VTextField
                    v-model="form.subject"
                    :rules="[requiredValidator]"
                    placeholder="กรอกชื่อเรื่อง"
                    readonly
                  />
                </VCol>
              </VRow>
            </VCol>
            <VCol cols="12">
              <VRow class="mb-2" align="center">
                <VCol cols="12" md="3" class="text-lg-end">
                  <label>
                    ถึง :
                    <small class="text-error">*</small>
                  </label>
                </VCol>
                <VCol cols="12" md="9">
                  <VTextField
                    v-model="form.email"
                    :rules="[requiredValidator]"
                    placeholder="ส่งถึง"
                    readonly
                  />
                </VCol>
              </VRow>
            </VCol>
            <VCol cols="12">
              <VRow class="mb-2" align="center">
                <VCol cols="12" md="3" class="text-lg-end">
                  <label>รายละเอียด :</label>
                </VCol>
                <VCol cols="12" md="9">
                  <VTextarea v-model="form.message" placeholder="กรอกรายละเอียด" readonly />
                </VCol>
              </VRow>
            </VCol>
          </VRow>
          <div class="d-flex flex-wrap justify-end mt-5">
            <div class="demo-space-x">
              <BtnGoBack />
            </div>
          </div>
        </VForm>
      </VCardText>
    </VCard>
  </div>
</template>
