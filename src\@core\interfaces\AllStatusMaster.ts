export interface IGroupUser {
  id: number
  name: string
}

export interface ILevelUser {
  id: number
  name: string
}
export interface ISubDistrict {
  id: number
  name: string
}
export interface IDistrict {
  create_by: string | null
  create_date: Date | null
  district_code: number
  district_id: number | null
  district_name: string
  modify_by: string | null
  modilfy_date: Date | null
  province_code: number
  province_id: number
}

export interface IDistrictFilter {
  district_id: number | null
  district_name: string
}
export interface IProvince {
  create_by: string
  create_date: Date | null
  latitude: number | null
  longtitude: number | null
  modify_by: string
  modilfy_date: Date | null
  province_code: number
  province_dopa_id: number
  province_id: number | null
  province_name: string
  region_name: string
  system_districts: any[] | []
  system_transaction_logs: any[] | []
}
export interface IProvinceFilter {
  province_id: number | null
  province_name: string

}
export interface ITitles {
  create_by: string | null
  create_date: Date | null
  is_active: boolean
  modify_by: string | null
  modilfy_date: Date | null
  title_id: number
  title_name: string
  title_nameen: string
  title_short_name: string
  title_short_nameen: string
}
export interface IRoles {
  create_by: string | null
  create_date: Date | null
  is_active: boolean
  modify_by: string | null
  modilfy_date: Date | null
  group_userid: string
  level_id: string
  role_id: number | null
  role_name: string
  timeout: string
}
export interface IRolesFilter {
  role_id: number | null
  role_name: string
}
export interface IOrganizations {
  create_by: string | null
  create_date: Date | null
  is_active: boolean
  modify_by: string | null
  modilfy_date: Date | null
  orggroup: string | null
  orggroup_id: number
  orgstructure_id: number
  orgstructure_name: string
  orgstructure_shortname: string
}
