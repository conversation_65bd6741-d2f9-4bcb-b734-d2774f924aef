<script setup>
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'

const callAxios = useAxios()
const Swal = useSwal()
const isDialogDisable = ref(false)

const list = ref([
  {
    title: 'Goverment Easy contact Center:GECC',
    to: 'apps-home',
    icon: {
      icon: 'mdi-home'
    },
    isInsert: false,
    isUpdate: false,
    isDelete: false,
    children: []
  },
  {
    title: 'รับรองมาตรฐาน GECC',
    to: 'apps-GECC-Menu',
    icon: {
      icon: 'mdi-file-document-outline'
    },
    isInsert: false,
    isUpdate: false,
    isDelete: false,
    children: []
  },
  {
    title: 'ปฏิทินการตรวจ GECC',
    to: 'apps-GECC-Calendar',
    icon: {
      icon: 'mdi-calendar'
    },
    isInsert: false,
    isUpdate: false,
    isDelete: false,
    children: []
  },
  {
    title: 'ผู้ดูแลระบบ',
    to: '-',
    icon: {
      icon: 'mdi-cogs'
    },
    isInsert: true,
    isUpdate: false,
    isDelete: false,
    children: [
      {
        title: 'จัดการแบนเนอร์',
        to: 'apps-admin-ManageBanners',
        isInsert: true,
        isUpdate: true,
        isDelete: true,
        children: []
      },
      {
        title: 'จัดการเมนู Webpage',
        to: 'apps-admin-ManageWebpage',
        isInsert: true,
        isUpdate: true,
        isDelete: true,
        children: []
      },
      {
        title: 'จัดการลิงก์ดาวน์โหลด',
        to: 'apps-admin-ManageDownloadLinks',
        isInsert: true,
        isUpdate: true,
        isDelete: true,
        children: []
      },
      {
        title: 'จัดการโลโก้',
        to: 'apps-admin-ManageLogos',
        isInsert: true,
        isUpdate: false,
        isDelete: false,
        children: []
      },
      {
        title: 'จัดการรูปแบบรายงาน Dashboard',
        to: 'apps-admin-ManageDashboardReports',
        isInsert: true,
        isUpdate: true,
        isDelete: true,
        children: []
      },
      {
        title: 'จัดการหน่วยงาน',
        to: 'apps-admin-ManageAgencies',
        isInsert: true,
        isUpdate: true,
        isDelete: true,
        children: []
      },
      {
        title: 'จัดการสิทธิการใช้งาน',
        to: 'apps-admin-ManageUserPermissions',
        isInsert: true,
        isUpdate: true,
        isDelete: true,
        children: []
      },
      {
        title: 'กำหนดสิทธิการเข้าถึงเมนู',
        to: 'apps-admin-ManageMenuAccess',
        isInsert: true,
        isUpdate: true,
        isDelete: true,
        children: []
      },
      {
        title: 'จัดการบัญชีผู้ใช้งาน',
        to: 'apps-admin-ManageUserAccounts',
        isInsert: true,
        isUpdate: true,
        isDelete: true,
        children: []
      },
      {
        title: 'ตั้งค่าอีเมลกลาง',
        to: 'apps-admin-SetUpEmail',
        isInsert: true,
        isUpdate: true,
        isDelete: true,
        children: []
      },
      {
        title: 'บล็อกอีเมล',
        to: 'apps-admin-BlockEmail',
        isInsert: true,
        isUpdate: true,
        isDelete: true,
        children: []
      },
      {
        title: 'ประวัติการใช้งาน',
        to: 'apps-admin-SystemUsageHistory',
        isInsert: true,
        isUpdate: true,
        isDelete: true,
        children: []
      },
      {
        title: 'หน้าจัดการ Template',
        to: 'apps-admin-ManageTemplate',
        isInsert: true,
        isUpdate: true,
        isDelete: true,
        children: []
      },
      {
        title: 'หน้าจัดการ Footer',
        to: 'apps-admin-ManageFooter',
        isInsert: true,
        isUpdate: true,
        isDelete: true,
        children: []
      },
      {
        title: 'จัดการเนื้อหา',
        to: 'apps-admin-ManageTransactionContent',
        isInsert: true,
        isUpdate: true,
        isDelete: true,
        children: []
      },
      {
        title: 'จัดการศูนย์ราชการ',
        to: 'apps-admin-ManageGovernmentCenter',
        isInsert: true,
        isUpdate: true,
        isDelete: true,
        children: []
      },
      {
        title: 'คำขอเปลี่ยนแปลงอีเมลหน่วยงาน',
        to: 'apps-admin-EmailChangeRequest',
        isInsert: true,
        isUpdate: true,
        isDelete: true,
        children: []
      },
      {
        title: 'จัดการคู่มือ/ใบสมัคร/แบบประเมิน',
        to: 'apps-admin-ManageDocument',
        isInsert: true,
        isUpdate: true,
        isDelete: true,
        children: []
      },
      {
        title: 'จัดการแบบสำรวจความคิดเห็น',
        to: 'apps-admin-ManageSurvey',
        isInsert: true,
        isUpdate: true,
        isDelete: true,
        children: []
      }
    ]
  },
  {
    title: 'รายงาน',
    to: 'apps-Report-Menu',
    icon: {
      icon: 'mdi-book-open-variant-outline'
    },
    isInsert: false,
    isUpdate: false,
    isDelete: false,
    children: []
  }
])

const breadcrumbItems = [
  {
    title: 'ระบบ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'จัดการเมนู',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const updateDialog = event => {
  const request = {
    menuId: event.id,
    title: event.title,
    pathUrl: event.to,
    icon: event.icon
  }

  callAxios.RequestPut(`/UserManagement/UpdateMenu?MenuId=${event.id}`, request).then(response => {
    if (response.status == 200) {
      isDialogDisable.value = false
      GetMenus()
    }
  })
}

const GetMenus = () => {
  callAxios.RequestGet('/UserManagement/GetMenus').then(response => {
    if (response.status == 200) list.value = response.data.response
  })
}

const UpdatePositionMenu = () => {
  Swal.ApproveConfirm().then(approve => {
    if (approve) {
      const BodyData = new FormData()

      BodyData.append('Position', JSON.stringify(list.value))
      callAxios.RequestUpload('/UserManagement/UpdateMenu', BodyData).then(response => {
        if (response.status == 200) GetMenus()
      })
    }
  })
}

onMounted(() => {
  GetMenus()
})
</script>

<template>
  <section>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <!-- 👉 Filters  -->
    <VCard title="" class="mb-6 mt-5">
      <VCardText class="d-flex align-center flex-wrap gap-4">
        <VBtn prepend-icon="mdi-plus" color="success-200" style="color: white">เพิ่มเมนู</VBtn>
      </VCardText>

      <VRow class="mb-4 mx-2">
        <VCol cols="12">
          <MenuNested
            v-model:isDialogDisable="isDialogDisable"
            :tasks="list"
            @fetch="GetMenus"
            @update:Dialog="event => updateDialog(event)"
          />
        </VCol>
        <VCol cols="12" class="d-flex flex-wrap justify-end mt-5">
          <div class="demo-space-x">
            <VBtn color="blue-600" @click="UpdatePositionMenu">บันทึก</VBtn>
          </div>
        </VCol>
      </VRow>
    </VCard>
  </section>
</template>
