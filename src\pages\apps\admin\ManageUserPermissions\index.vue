<script setup lang="ts">
import { reactive, ref } from 'vue'
import DialogUserPermissions from './DialogUserPermissions.vue'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import type { IGetSystemRolesRes } from '@interfaces/UserManagementInterface'

const callAxios = useAxios()
const Swal = useSwal()

// State management
const isDialogVisible = ref(false)
const isAdd = ref(false)
const isEdit = ref(false)
const isView = ref(false)
const editId = ref()

const currentPage: Ref<number> = ref(1)
const rowPerPage: Ref<number> = ref(20)
const totalRecords: Ref<number> = ref(0)
const ListItem = ref([{}])

// Breadcrumb items
const breadcrumbItems = [
  { title: 'ผู้ดูแลระบบ', to: '/apps/home' },
  {
    title: 'จัดการสิทธิการใช้งาน',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const listFilter = ref([
  {
    name: 'searchStatus',
    type: 'select',
    label: 'สถานะ',
    default: '',
    title: 'StatusName',
    value: 'StatusId',
    items: [
      {
        StatusName: 'ทั้งหมด',
        StatusId: ''
      },
      {
        StatusName: 'ไม่ใช้งาน',
        StatusId: 'false'
      },
      {
        StatusName: 'ใช้งาน',
        StatusId: 'true'
      }
    ]
  },
  {
    name: 'searchWord',
    type: 'text',
    label: 'คำค้น',
    placeholder: 'กรอกข้อมูลชื่อสิทธิที่ต้องการค้นหา'
  }
])

const filter = reactive({
  searchStatus: '',
  searchWord: ''
})

const listFields = ref([
  {
    field: 'name',
    header: 'ชื่อสิทธิ',
    sortable: true,
    style: { textAlign: 'start' }
  },
  {
    field: 'code',
    header: 'รหัสสิทธิ',
    sortable: true,
    style: { textAlign: 'start' }
  },
  {
    field: 'timeOut',
    header: 'Timeout (นาที)',
    sortable: true,
    style: { textAlign: 'start' }
  },
  { field: 'isActive', header: 'สถานะ', sortable: false },
  {
    field: 'createDate',
    header: 'วันที่สร้าง',
    sortable: true,
    style: { textAlign: 'center' },
    sortField: 'sortCreateDate'
  },
  {
    field: 'createBy',
    header: 'ชื่อผู้สร้าง',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'updateDate',
    header: 'วันที่แก้ไข',
    sortable: true,
    style: { textAlign: 'center' },
    sortField: 'sortUpdateDate'
  },
  {
    field: 'updateBy',
    header: 'ชื่อผู้แก้ไข',
    sortable: true,
    style: { textAlign: 'center' }
  },
  { field: 'options', header: 'การจัดการ', style: { textAlign: 'center' } }
])

// Open dialog functions
const openAddDialog = () => {
  isAdd.value = true
  isEdit.value = false
  isView.value = false
  isDialogVisible.value = true
}

const openEditDialog = (id: string) => {
  isEdit.value = true
  isView.value = false
  isAdd.value = false
  isDialogVisible.value = true
  editId.value = id
}

const openViewDialog = (id: string) => {
  isView.value = true
  isEdit.value = false
  isAdd.value = false
  isDialogVisible.value = true
  editId.value = id
}

const GetList = async () => {
  try {
    const queryParams = new URLSearchParams({
      Page: currentPage.value.toString(),
      PageSize: rowPerPage.value.toString()
    })

    if (filter.searchWord) queryParams.append('Keyword', filter.searchWord)

    if (filter.searchStatus) queryParams.append('Status', filter.searchStatus)

    const response = await callAxios.RequestGet(
      `/UserManagement/GetSystemRoles?${queryParams.toString()}`,
      true
    )

    if (response.status === 200) {
      totalRecords.value = response.data.count
      ListItem.value = response.data.response as IGetSystemRolesRes[]
    } else {
      Swal.AddConditionFailText('ไม่สามารถโหลดข้อมูลได้')
    }
  } catch (error) {
    Swal.callCatch()
  }
}

const ApproveDelete = (id: number, isActiveStatus: string) => {
  if (isActiveStatus === 'ใช้งาน') {
    // แสดงข้อความแจ้งเตือนเมื่อสถานะคือ "ใช้งาน"
    Swal.DeleteIsActiveCheck()
  } else if (isActiveStatus === 'ไม่ใช้งาน') {
    // ถ้าสถานะคือ "ไม่ใช้งาน" เรียก API เพื่อลบข้อมูล
    const endpoint = `/UserManagement/DeleteSystemRole?RoleId=${id}`

    Swal.ApproveDelete(endpoint).then(response => {
      if (response) GetList() // อัปเดตรายการหลังการลบสำเร็จ
    })
  }
}

const handleDialogUpdate = () => {
  GetList() // Refresh the list after add/edit operation
}

onMounted(() => {
  GetList()
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <FiltersAPI v-model="filter" :fields="listFilter" @submit="GetList" />

    <VCard>
      <VCardText>
        <div class="d-flex align-center flex-wrap gap-4">
          <BtnInsert
            class="mt-2"
            prepend-icon="mdi-plus-box-multiple"
            color="btn-add"
            rounded="xl"
            @click="openAddDialog"
          >
            เพิ่มรายการ
          </BtnInsert>
        </div>
      </VCardText>

      <AppDataTableAPI :columns="listFields" :value="ListItem" :total-records="totalRecords">
        <template #isActive="slotProps">
          <ChipStatus
            :status="slotProps.data.isActive"
            :label="slotProps.data.isActive === 'ใช้งาน' ? 'ใช้งาน' : ''"
          />
        </template>
        <template #options="slotProps">
          <div class="text-center">
            <IconBtn :to="{ name: 'apps-admin-ManageMenuAccess' }">
              <VIcon icon="mdi-shield" />
              <VTooltip open-delay="500" location="top" activator="parent">จัดการสิทธิ</VTooltip>
            </IconBtn>

            <IconBtn @click="openViewDialog(slotProps.data.roleId)">
              <VIcon icon="mdi-eye-outline" />
              <VTooltip open-delay="500" location="top" activator="parent">ดูข้อมูล</VTooltip>
            </IconBtn>

            <IconEdit @click="openEditDialog(slotProps.data.roleId)" />
            <IconDelete @click="ApproveDelete(slotProps.data.roleId, slotProps.data.isActive)" />
          </div>
        </template>
      </AppDataTableAPI>
    </VCard>
    <DialogUserPermissions
      v-model="isDialogVisible"
      :is-add="isAdd"
      :is-edit="isEdit"
      :is-view="isView"
      :view-id="editId"
      :edit-id="editId"
      @update="handleDialogUpdate"
    />
  </div>
</template>
