<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import Filters from '@core/components/Filters.vue'
import { booleanValidator, requiredValidator } from '@validators'

const router = useRouter()
const callAxios = useAxios()
const Swal = useSwal()

const searchQuery: Ref<string | number> = ref('')
const isEdit: Ref<boolean> = ref(false)
const refVForm: Ref<any> = ref(null)
const fileUpload: Ref<any> = ref([])

const editId: Ref<number> = ref(0)

interface FormType {
  roleName: string
  isActive: string
  createDate: string
  createBy: string
  updateDate: string
  updateBy: string
  orgStructureId: number
  roleId: number
}

const form: Ref<FormType> = ref({
  roleName: '',
  isActive: ''
})

const formRaw: Ref<FormType> = ref({
  roleName: '',
  isActive: ''
})

const listStatus = ref([
  { id: true, name: 'ใช้งาน' },
  { id: false, name: 'ไม่ใช้งาน' }
])

// ทำสี
const statusActive = ref({
  true: 'success',
  false: 'error'
})

const breadcrumbItems = [
  {
    title: 'ผู้ดูแลระบบ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'จัดการประเภทผู้ใช้งาน',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const filter = ref({
  searchQuery: null
})

const listFilter = reactive([
  {
    name: 'searchQuery',
    type: 'text',
    label: 'ค้นหา'
  }
])

const listFields = reactive([
  {
    field: 'roleName',
    header: 'ประเภทผู้ใช้งาน',
    sortable: true
  },
  {
    field: 'isActive',
    header: 'สถานะการใช้งาน',
    sortable: true
  },
  {
    field: 'createDate',
    header: 'วันที่สร้าง',
    sortable: true
  },
  {
    field: 'createBy',
    header: 'ผู้สร้าง',
    sortable: true
  },
  {
    field: 'updateDate',
    header: 'วันที่แก้ไข',
    sortable: true
  },
  {
    field: 'updateBy',
    header: 'ผู้แก้ไข',
    sortable: true
  },

  {
    field: 'options',
    header: 'การจัดการ'
  }
])

const ListItem = ref([])
const ListItemStore = ref([])

const isDialogVisible = ref(false)

const GetList = () => {
  callAxios.RequestGet('/UserManagement/GetRoles').then(response => {
    if (response.status == 200) {
      ListItem.value = response.data.response
      ListItemStore.value = response.data.response
    }
  })
}

onMounted(async () => {
  GetList()
})

const GetListById = id => {
  // form.value = ListItem.value[id];
  // editId.value = id;
  callAxios

    // .RequestGetById(
    //   `/SystemSetting/GetCertificateLogoByID?linkId=${id}`
    // )
    .RequestGetById('/UserManagement/GetRoleByRoleId', `?RoleId=${id}`)
    .then(response => {
      if (response.status == 200) {
        console.log(response)

        form.value = response.data.response
        editId.value = form.value.id
      }
    })
}

const AddForm = () => {
  form.value.orgStructureId = 1
  callAxios.RequestPost('/UserManagement/AddRole', form.value).then(response => {
    if (response.status == 200) {
      router.push({ path: '/apps/admin/manageusertypes' })
      isDialogVisible.value = false
      GetList()
    }
  })
}

const EditForm = id => {
  Swal.ApproveConfirm().then(response => {
    if (response) {
      // ListItem.value.splice(id, 1, form.value);
      // onFormReset();
      // form.value.createdDateTime = "1900-01-01";
      // form.value.modifiedDateTime = "1900-01-01";
      callAxios.RequestPut(`/UserManagement/UpdateRole?RoleId=${id}`, form.value).then(response => {
        if (response.status == 200) {
          isDialogVisible.value = false
          GetList()
        }
      })
    }
  })
}

const onFormSubmit = () => {
  refVForm.value?.validate().then(({ valid: isValid }: { valid: boolean }) => {
    if (isValid) {
      if (isEdit.value === false) AddForm()
      else EditForm(editId.value)
    }
  })
}

const onFormReset = () => {
  isDialogVisible.value = false
  form.value = structuredClone(toRaw(formRaw.value))
}

const ApproveDelete = id => {
  const endpoint = `/UserManagement/DeleteRole?RoleId=${id}`

  // console.log(listFields.value);

  // ListItem.value.splice(id, 1);

  Swal.ApproveDelete(endpoint).then(response => {
    if (response) GetList()
  })
}

function onSearch(values) {
  alert(JSON.stringify(values, null, 2))
}
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <!-- {{ filter }} -->

    <Filters :fields="listFilter" :model-value="filter" />

    <VCard>
      <VCardText>
        <div class="d-flex align-center flex-wrap gap-4">
          <BtnInsert
            prepend-icon="mdi-plus"
            color="success-200"
            @click="((isDialogVisible = true), (isEdit = false))"
          >
            เพิ่ม
          </BtnInsert>
        </div>
      </VCardText>
      <!-- <AppDataTable :filters="filter.searchQuery" :columns="listFields" :value="ListItem"> -->
      <AppDataTable :filters="filter.searchQuery" :columns="listFields" :value="ListItem">
        <template #isActive="slotProps">
          <div class="text-center">
            <!-- ทำสี -->
            <VChip :color="statusActive[slotProps.data.isActive]" variant="elevated">
              {{ slotProps.data.isActive ? 'ใช้งาน' : 'ไม่ใช้งาน' }}
            </VChip>
          </div>
        </template>

        <template #options="slotProps">
          <div class="text-center">
            <IconEdit
              @click="
                ((isDialogVisible = true), (isEdit = true), GetListById(slotProps.data.roleId))
              "
            />
            <IconDelete @click="ApproveDelete(slotProps.data.roleId)" />
            <!--
              <IconBtn @click="">
              <VIcon icon="mdi-cog-outline" />
              </IconBtn>
            -->
          </div>
        </template>
      </AppDataTable>
    </VCard>

    <VDialog v-model="isDialogVisible" persistent class="v-dialog-lg" no-click-animation scrollable>
      <VCard :title="isEdit ? 'แก้ไขข้อมูล' : 'เพิ่มประเภทผู้ใช้งาน'">
        <!-- List -->
        <DialogCloseBtn variant="text" size="small" @click="onFormReset" />

        <VCardText>
          <VForm ref="refVForm" @submit.prevent="onFormSubmit">
            <VRow class="mb-4 mx-2 mt-6">
              <VCol cols="12" md="1" />
              <VCol cols="12" md="10">
                <VRow>
                  <!-- 👉 outlined variant -->

                  <VCol cols="12" md="12">
                    <VRow class="mb-2" align="center">
                      <VCol cols="12" md="3" class="text-lg-end">
                        <label>
                          ชื่อกลุ่มผู้ใช้งาน (ภาษาอังกฤษ) :
                          <small class="text-error">*</small>
                        </label>
                      </VCol>
                      <VCol cols="12" md="9">
                        <!-- 👉 outlined variant -->

                        <VTextField v-model="form.name" :rules="[requiredValidator]" />
                      </VCol>
                      <VCol cols="12" md="3" class="text-lg-end">
                        <label>
                          ชื่อกลุ่มผู้ใช้งาน (ภาษาไทย) :
                          <small class="text-error">*</small>
                        </label>
                      </VCol>
                      <VCol cols="12" md="9">
                        <!-- 👉 outlined variant -->

                        <VTextField v-model="form.nameTH" :rules="[requiredValidator]" />
                      </VCol>
                    </VRow>
                  </VCol>

                  <VCol cols="12" md="12">
                    <VRow class="mb-2" align="center">
                      <VCol cols="12" md="3" class="text-lg-end">
                        <label>
                          สถานะ :
                          <small class="text-error">*</small>
                        </label>
                      </VCol>
                      <VCol cols="12" md="9">
                        <!-- 👉 outlined variant -->
                        <VAutocomplete
                          v-model="form.isActive"
                          :items="listStatus"
                          item-title="name"
                          item-value="id"
                          :rules="[booleanValidator]"
                        />
                      </VCol>
                    </VRow>
                  </VCol>
                </VRow>
              </VCol>
              <VCol cols="12" md="1" />
            </VRow>

            <!-- 👉 ปุ่มต่างๆ -->
            <div class="d-flex flex-wrap justify-center mt-5">
              <div class="demo-space-x">
                <VBtn type="submit" color="blue-600">บันทึก</VBtn>

                <VBtn color="error-300" @click="onFormReset">ยกเลิก</VBtn>
              </div>
            </div>
          </VForm>
        </VCardText>
      </VCard>
    </VDialog>
  </div>
</template>
