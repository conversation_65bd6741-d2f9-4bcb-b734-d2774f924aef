<script setup>
import { useAxios } from '@/store/useAxios'
import { Swiper, SwiperSlide } from 'swiper/vue'
import blogGridPic1 from '@public/assets/images/blog/blog-grid-1/pic1.jpg'
import blogGridPic2 from '@public/assets/images/blog/blog-grid-1/pic2.jpg'
import blogGridPic3 from '@public/assets/images/blog/blog-grid-1/pic3.jpg'
import { RouterLink } from 'vue-router'
import { Autoplay, Navigation } from 'swiper/modules'
const callAxios = useAxios()

const props = defineProps({
  modelValue: {
    type: Number,
    required: false
  }
})

const setAttribute = ref({
  modules: [Navigation, Autoplay],
  navigation: {
    prevEl: '.prev',
    nextEl: '.next'
  },
  slidesPerView: 4,
  loop: true,
  speed: 1300,
  breakpoints: {
    1180: { slidesPerView: 3 },
    775: { slidesPerView: 2 },
    200: { slidesPerView: 1 }
  },
  autoplay: {
    delay: 1200
  }
})

const carousel = ref([])
const getEventByDate = () => {
  callAxios
    .RequestGet(`/OtherMaster/GetEventByDate?TypeEvent=${props.modelValue}`)
    .then(response => {
      if (response.status == 200) {
        carousel.value = response.data.response
      }
    })
}

onMounted(() => {
  getEventByDate()
})
watch(props, () => {
  if (props.modelValue) {
    getEventByDate()
  }
})
</script>

<template>
  <main style="position: relative">
    <Swiper
      v-bind="setAttribute"
      class="blog-carousel1 owl-carousel owl-theme owl-btn-1 owl-btn-center-lr owl-dots-none owl-btn-primary"
    >
      <SwiperSlide class="item" v-for="(item, ind) in carousel" :key="ind">
        <div class="dlab-blog style-1 bg-white">
          <div class="dlab-media">
            <a :href="item.url" target="_blank">
              <img
                :src="item.logoThumbnail"
                alt=""
                class="object-fit-contain"
                style="height: 250px"
              />
            </a>
          </div>
          <div class="dlab-info">
            <h5 class="dlab-title" style="height: 80px">
              <a :href="item.url" target="_blank">{{ item.name }}</a>
            </h5>
            <div class="dlab-meta meta-bottom pt-0">
              <ul class="d-inline">
                <li class="mb-1">
                  <i class="far fa-clock m-r10"></i>
                  {{ item.start }} - {{ item.end }}
                </li>
                <li class="mb-1">
                  <i class="fas fa-users m-r10"></i>
                  จำนวนเปิดรับ : {{ item.participantsUnit ? item.participantsUnit : 'ไม่จำกัด' }}
                </li>
                <li class="mb-1">
                  <i class="fas fa-location-arrow m-r10"></i>
                  {{ item.organizer }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </SwiperSlide>
    </Swiper>
    <!-- <div class="next"><i class="fa fa-arrow-right"></i></div>
        <div class="prev"><i class="fa fa-arrow-left"></i></div> -->
  </main>
</template>

<style>
.prev {
  position: absolute;
  top: 50%;
  left: -100px;
  transform: translateY(-50%);
  color: #fff;
  background: var(--primary);
  padding: 8px 15px;
  border-radius: 3px;
  box-shadow: 1px 0 15px rgba(0, 0, 0, 0.37);
  z-index: 99;
  cursor: pointer;
}

.next {
  position: absolute;
  top: 50%;
  right: -100px;
  transform: translateY(-50%);
  color: #fff;
  background: var(--primary);
  padding: 8px 15px;
  border-radius: 3px;
  box-shadow: 1px 0 15px rgba(0, 0, 0, 0.37);
  z-index: 99;
  cursor: pointer;
}

@media screen and (max-width: 1300px) {
  .prev {
    left: -50px;
  }

  .next {
    right: -50px;
  }
}

@media screen and (max-width: 1070px) {
  .prev {
    left: -10px;
  }

  .next {
    right: -10px;
  }
}
</style>
