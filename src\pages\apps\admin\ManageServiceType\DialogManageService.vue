<script lang="ts" setup>
import { computed, reactive, ref } from 'vue'
import { VTextarea } from 'vuetify/lib/components/index.mjs'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import type { FormServiceType } from '@interfaces/FormInterface'
import type { IGetSystemServiceTypeByIdRes } from '@interfaces/UserManagementInterface'
import { booleanValidator, requiredValidator } from '@validators'

// Props and Emits
const props = defineProps({
  modelValue: Boolean,
  isAdd: Boolean,
  isEdit: Boolean,
  isView: Boolean,
  viewId: String,
  editId: String
})

const emit = defineEmits(['update:modelValue', 'update'])

const callAxios = useAxios()
const Swal = useSwal()
const refVForm: Ref<any> = ref(null)
const viewId = computed(() => props.viewId)
const editId = computed(() => props.editId)

const form = reactive<FormServiceType>({
  transactionServiceTypeId: '',
  serviceTypeName: '',
  isActive: false,
  description: '',
  yearStart: '',
  yearEnd: '',
  contentName: '',
  year: []
})

const createFormBody = (sanitizedserviceTypeName: string, sanitizeddescription?: string) => {
  const body: any = {
    serviceTypeName: sanitizedserviceTypeName,
    isActive: form.isActive,
    yearStart: form.yearStart ? form.yearStart : '',
    yearEnd: form.yearEnd ? form.yearEnd : ''
  }

  if (sanitizeddescription && sanitizeddescription.trim() !== '')
    body.description = sanitizeddescription

  return body
}

const formDefault = structuredClone(toRaw(form))

const resetForm = () => {
  Object.assign(form, structuredClone(formDefault))
}

const closeDialog = async () => {
  if (props.isView) {
    resetForm()
    isDialogVisible.value = false

    return
  }
  const response = await Swal.ApproveCancel()
  if (response) {
    resetForm()
    isDialogVisible.value = false
  }
}

const onFormSubmit = async () => {
  try {
    if (!refVForm.value) return
    const { valid } = await refVForm.value.validate()

    if (valid) {
      if (props.isAdd) await AddForm()
      else if (props.isEdit) await EditForm()
      else throw new Error('Invalid state')
    }
  } catch (error) {
    Swal.AddConditionFailText('ไม่สามารถดำเนินการได้: ไม่มีสถานะที่ถูกต้อง')
  }
}

const onYearRangeChange = (value: string[]) => {
  if (value && value.length === 2) {
    form.yearStart = value[0].toString()
    form.yearEnd = value[1].toString()
  } else {
    form.yearStart = ''
    form.yearEnd = ''
  }
  form.year = value
}

const checkDuplicateData = async (error: any) => {
  if (error?.response?.status === 409) {
    const apiMessage = error.response.data?.message || 'ข้อมูลมีอยู่แล้วในระบบ'

    Swal.AddConditionFailText(apiMessage)
  } else {
    // กรณีอื่น ๆ
    Swal.AddConditionFailText('กรุณากรอกข้อมูลให้ถูกต้อง')
  }
}

const sanitizeContent = (content: string): string => {
  return content
    .replace(/\s+/g, ' ') // แทนที่ช่องว่างหลายช่องด้วยช่องว่างเดียว
    .trim() // ลบช่องว่างส่วนต้นและส่วนท้าย
}

const AddForm = async () => {
  const confirmed = await Swal.ApproveConfirm()
  if (!confirmed) return

  try {
    const sanitizedserviceTypeName = sanitizeContent(form.serviceTypeName)
    const sanitizeddescription = sanitizeContent(form.description)

    const formBody = createFormBody(sanitizedserviceTypeName, sanitizeddescription)

    const response = await callAxios.RequestPost('/UserManagement/AddSystemServiceType', formBody)

    if (response.status === 200) {
      resetForm()
      isDialogVisible.value = false
      emit('update')
    } else {
      Swal.AddFail()
    }
  } catch (error: any) {
    await checkDuplicateData(error)
  }
}

const EditForm = async () => {
  const confirmed = await Swal.ApproveConfirm()
  if (!confirmed) return

  const sanitizedserviceTypeName = sanitizeContent(form.serviceTypeName)
  const sanitizeddescription = sanitizeContent(form.description)

  // ตรวจสอบและใช้ค่าเดิม
  const yearStart = form.yearStart || ''
  const yearEnd = form.yearEnd || ''

  const formBody = {
    transactionServiceTypeId: form.transactionServiceTypeId || null,
    serviceTypeName: sanitizedserviceTypeName,
    isActive: form.isActive,
    description: sanitizeddescription || '',
    yearStart,
    yearEnd
  }

  try {
    const response = await callAxios.RequestPut(
      `/UserManagement/UpdateSystemServiceType?TransactionServiceTypeId=${editId.value}`,
      formBody
    )

    if (response.status === 200) {
      Swal.EditSuccess()
      resetForm()
      isDialogVisible.value = false
      emit('update')
    } else {
      Swal.EditConditionFail()
    }
  } catch (error) {
    await checkDuplicateData(error)
  }
}

const ViewForm = async () => {
  try {
    const response = await callAxios.RequestGetById(
      '/UserManagement/GetSystemServiceTypeById',
      `?TransactionServiceTypeId=${viewId.value}`
    )

    if (response.status === 200) {
      resetForm()

      const ServerTypeData: IGetSystemServiceTypeByIdRes = response.data.response

      form.transactionServiceTypeId = ServerTypeData.transactionServiceTypeId
      form.serviceTypeName = ServerTypeData.contentName
      form.description = ServerTypeData.description
      form.isActive = ServerTypeData.isActive

      // แปลงค่า year ให้เป็น string[]
      if (ServerTypeData.year && ServerTypeData.year.length === 2) {
        form.year = ServerTypeData.year.map(y => y.toString())
        form.yearStart = ServerTypeData.year[0]
        form.yearEnd = ServerTypeData.year[1]
      } else {
        form.year = []
        form.yearStart = ''
        form.yearEnd = ''
      }
    }
  } catch (error) {
    Swal.ViewFail()
  }
}

const isDialogVisible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value)
})

watch(
  () => isDialogVisible.value,
  async newVal => {
    if (newVal && props.isView) await ViewForm()
    else if (newVal && props.isEdit) await ViewForm()
  }
)

const setTitle = computed(() => {
  if (props.isView) return 'ดูข้อมูล'
  if (props.isEdit) return 'แก้ไขด้านตามลักษณะการให้บริการ'

  return 'เพิ่มด้านตามลักษณะการให้บริการ'
})

const listStatus = ref([
  { id: true, name: 'ใช้งาน' },
  { id: false, name: 'ไม่ใช้งาน' }
])
</script>

<template>
  <VDialog v-model="isDialogVisible" persistent class="v-dialog-sm" no-click-animation scrollable>
    <VCard :title="setTitle">
      <DialogCloseBtn variant="text" size="small" @click="closeDialog" />
      <VCardText>
        <VForm ref="refVForm" @submit.prevent="onFormSubmit">
          <VRow class="mb-4 mx-2 mt-3 justify-center">
            <VCol cols="12" md="10">
              <VRow>
                <VCol cols="12" md="12">
                  <VRow class="mb-2" align="center">
                    <VCol cols="12" md="12" class="py-0 mb-2">
                      <label>
                        ชื่อด้านตามลักษณะการให้บริการ :
                        <small class="text-error">*</small>
                      </label>
                    </VCol>
                    <VCol cols="12" md="12" class="py-0">
                      <VTextField
                        v-model="form.serviceTypeName"
                        density="comfortable"
                        placeholder="ระบุชื่อด้านตามลักษณะการให้บริการ"
                        class="no-select"
                        :rules="[requiredValidator]"
                        :disabled="props.isView"
                        :bg-color="isView ? 'grey-secondary' : undefined"
                        :counter="150"
                        :maxlength="150"
                      />
                    </VCol>
                  </VRow>
                </VCol>

                <VCol cols="12" md="12">
                  <VRow class="mb-2" align="center">
                    <VCol cols="12" md="12" class="py-0 mb-2">
                      <label>
                        ปีเริ่มต้น-สิ้นสุด :
                        <small class="text-error">*</small>
                      </label>
                    </VCol>
                    <VCol cols="12" md="12">
                      <DateTimePicker
                        v-model="form.year"
                        teleport-center
                        year-picker
                        placeholder="ระบุปีเริ่มต้น-สิ้นสุด"
                        :disabled="props.isView"
                        :range="true"
                        :rules="[requiredValidator]"
                        :bg-color="isView ? 'grey-secondary' : undefined"
                        @update:modelValue="onYearRangeChange"
                      />
                    </VCol>
                  </VRow>
                </VCol>

                <VCol cols="12" md="12">
                  <VRow class="mb-2" align="center">
                    <VCol cols="12" md="12" class="py-0 mb-2">
                      <label>รายละเอียด :</label>
                    </VCol>
                    <VCol cols="12" md="12" class="py-0">
                      <VTextarea
                        v-model="form.description"
                        density="comfortable"
                        placeholder="ระบุรายละเอียด"
                        class="no-select"
                        :disabled="props.isView"
                        :bg-color="isView ? 'grey-secondary' : undefined"
                        :counter="200"
                        :maxlength="200"
                      />
                    </VCol>
                  </VRow>
                </VCol>

                <VCol cols="12" md="12">
                  <VRow class="mb-2" align="center">
                    <VCol cols="12" md="12" class="py-0 mb-2">
                      <label>
                        สถานะ :
                        <small class="text-error">*</small>
                      </label>
                    </VCol>
                    <VCol cols="12" md="12" class="py-0">
                      <VAutocomplete
                        v-model="form.isActive"
                        density="comfortable"
                        placeholder="ระบุสถานะ"
                        item-title="name"
                        item-value="id"
                        class="no-select"
                        :rules="[booleanValidator]"
                        :items="listStatus"
                        :disabled="props.isView"
                        :bg-color="isView ? 'grey-secondary' : undefined"
                      />
                    </VCol>
                  </VRow>
                </VCol>
              </VRow>
            </VCol>
          </VRow>

          <div class="d-flex flex-wrap justify-center mt-5">
            <div class="btn-group">
              <template v-if="!props.isView">
                <VBtn
                  color="blue-600"
                  rounded="xl"
                  prepend-icon="mdi-content-save"
                  @click="onFormSubmit"
                >
                  บันทึก
                </VBtn>
                <VBtn
                  color="error-300"
                  rounded="xl"
                  prepend-icon="mdi-close-circle"
                  @click="closeDialog"
                >
                  ยกเลิก
                </VBtn>
              </template>

              <template v-else>
                <VBtn color="grey-800" rounded="xl" prepend-icon="mdi-close" @click="closeDialog">
                  ปิด
                </VBtn>
              </template>
            </div>
          </div>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style scoped>
.no-select {
  user-select: none;
}

.btn-group {
  display: flex;
  justify-content: center;
  gap: 16px;
}

@media (max-width: 320px) {
  .btn-group {
    display: flex;
    justify-content: center;
    gap: 16px;
  }
}
</style>
