import { setupLayouts } from 'virtual:generated-layouts'
import { createRouter, createWebHistory } from 'vue-router'
import { useAuth } from '@/store/useAuth'
import { useAxios } from '@/store/useAxios'
import routes from '~pages'

const IsAUTHEN = import.meta.env.VITE_AUTHLINK

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    // ℹ️ We are redirecting to different pages based on role.
    // NOTE: Role is just for UI purposes. ACL is based on abilities.
    {
      path: '/',
      redirect: () => ({ name: 'Public' })
    },
    ...setupLayouts(routes)
  ]
})

// Docs: https://router.vuejs.org/guide/advanced/navigation-guards.html#global-before-guards
router.beforeEach(async (to, from) => {
  const callAxios = useAxios()
  const Auth = useAuth()

  // ตรวจสอบว่า URL เป็นหนึ่งใน path ที่ต้องมี query parameter
  const restrictedPaths = [
    '/public/Umanual',
    '/public/DownloadApplicationForm',
    '/public/ReadinessAssessment'
  ]

  if (restrictedPaths.includes(to.path) && Object.keys(to.query).length === 0)
    return router.push({ path: '/not-found' })

  if (
    to.name &&
    to.name !== 'apps-login' &&
    to.name !== 'apps-forgotPassword' &&
    to.name !== 'apps-ResetPassword' &&
    to.name !== 'apps-Register' &&
    to.name !== 'apps-RegisterAgain' &&
    to.name !== 'apps-RegisterSuccess' &&
    to.name !== 'apps-RequestChangeUser' &&
    to.name !== 'apps-callback' &&
    to.name !== 'apps' &&
    to.name !== 'Public' &&
    to.name !== 'Public-survey' &&
    to.name !== 'Public-bestPractice' &&
    to.name !== 'apps-public-EvaluationForm-id' &&
    to.name !== 'thank-you' &&
    to.name !== 'not-found' &&
    to.name !== 'apps-ReportController'
  ) {
    // callAxios
    //   .RequestGet(`/OtherMaster/MenuPublic?Path=${path}`)
    //   .then((response) => {
    //     if (response.status === 200) {
    //       Auth.Permission = response.data.response;
    //       const isRead = response.data.response.isRead;
    //       if (path == "apps-system-menu") {
    //         return true;
    //       }
    //       if (localStorage.getItem('token') == null) {
    //         let textAlert = "เซสซันหมดอายุ โปรดเข้าสู่ระบบอีกครั้ง";
    //         if (router.currentRoute.value.query?.by === "public")
    //           textAlert = "กรุณาเข้าสู่ระบบเพื่อใช้งาน";
    //         Swal.fire({
    //           icon: "error",
    //           title: "แจ้งเตือน",
    //           text: textAlert,
    //           showCancelButton: false,
    //           confirmButtonText: "ตกลง",
    //           allowOutsideClick: false,
    //         }).then(async (result) => {
    //           if (result.isConfirmed) {
    //             localStorage.removeItem("token");
    //             localStorage.removeItem("userAbilities");
    //             localStorage.removeItem("username");
    //             window.location.href = "/apps/login";
    //           }
    //         });
    //         // return true;
    //       } else {
    //         if (IsAUTHEN == "1") {
    //           if (isRead != undefined) {
    //             if (isRead) {
    //               return true;
    //             } else {
    //               router.push({ path: "/not-authorized" });
    //             }
    //           } else {
    //             router.push({ path: "/not-authorized" });
    //           }
    //         }
    //       }
    //     }
    //   });
  }

  /*

    ℹ️ Commented code is legacy code

    if (!canNavigate(to)) {
      // Redirect to login if not logged in
      // ℹ️ Only add `to` query param if `to` route is not index route
      if (!isLoggedIn)
        return next({ name: 'login', query: { to: to.name !== 'index' ? to.fullPath : undefined } })

      // If logged in => not authorized
      return next({ name: 'not-authorized' })
    }

    // Redirect if logged in
    if (to.meta.redirectIfLoggedIn && isLoggedIn)
      next('/')

    return next()

    */
  // if (canNavigate(to)) {
  //   if (to.meta.redirectIfLoggedIn && isLoggedIn)
  //     return '/'
  // }
  // else {
  //   if (isLoggedIn)
  //     return { name: 'not-authorized' }
  //   else
  //     return { name: 'login', query: { to: to.name !== 'index' ? to.fullPath : undefined } }
  // }
})

// Register the afterEach hook
router.afterEach((to, from) => {
  // Check if the navigation is from /public/* to a different subfolder
  const isFromPublic = from.path.startsWith('/public')

  const isToDifferentSubfolder = to.path.split('/')[1] !== from.path.split('/')[1]

  if (isFromPublic && isToDifferentSubfolder) {
    // Reload the page
    window.location.reload()
  }
})
export default router
