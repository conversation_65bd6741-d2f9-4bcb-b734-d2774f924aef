<script setup>
const router = useRouter()
import { useAxios } from '@/store/useAxios'
const callAxios = useAxios()

const imgLogo = ref('')
</script>

<template>
  <div>
    <HeaderPublic :logo="imgLogo" />

    <policy />

    <FooterPublic :logo="imgLogo" />
  </div>
</template>

<style lang="scss">
@use '@public/assets/css/style.css';
@use '@public/assets/css/skin/skin-2.css';
</style>

<route lang="yaml">
meta:
  layout: blank
  action: read
</route>
