<script setup>
const route = useRoute()
const paramsId = ref(Number(route.params.id))

const breadcrumbItems = [
  {
    title: 'แบบประเมินความพึงพอใจ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'KBP Survey',
    disabled: false,
    to: ''
  },
  {
    title: 'แบบประเมินความพึงพอใจของผู้มีส่วนได้ส่วนเสีย',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <EvaluationForm title="แบบประเมินความพึงพอใจของผู้มีส่วนได้ส่วนเสีย" />
  </div>
</template>
