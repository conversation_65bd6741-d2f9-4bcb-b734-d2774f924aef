<script setup lang="ts">
import { useAppAbility } from '@/plugins/casl/useAppAbility'
import { useGenerateImageVariant } from '@/@core/composable/useGenerateImageVariant'
import authV1LoginMaskDark from '@images/pages/auth-v1-login-mask-dark.png'
import authV1LoginMaskLight from '@images/pages/auth-v1-login-mask-light.png'
import { VNodeRenderer } from '@layouts/components/VNodeRenderer'
import { themeConfig } from '@themeConfig'
import { useAxios } from '@/store/useAxios'
import { useAuth } from '@/store/useAuth'
import { useSwal } from '@/store/useSwal'

const AUTHEN = import.meta.env.VITE_URL_AUTHEN
const Swal = useSwal()
const callAxios = useAxios()
const callAuth = useAuth()
const router = useRouter()
const route = useRoute()
const AppAbility = useAppAbility()
const processing = ref(0)

const authV1ThemeLoginMask = useGenerateImageVariant(authV1LoginMaskLight, authV1LoginMaskDark)

const clearSession = () => {
  localStorage.removeItem('token')
  localStorage.removeItem('username')
  localStorage.removeItem('roleName')
  localStorage.removeItem('userAbilities')
}

const login = token => {
  callAxios
    .RequestPost('/Authenticate/EServiceAuthenticate', { token })
    .then(response => {
      if (response.status == 200) {
        processing.value = 1

        const userAbilities = JSON.stringify([{ action: 'manage', subject: 'all' }])

        localStorage.setItem('userAbilities', userAbilities)
        AppAbility.update(userAbilities)
        localStorage.setItem('token', response.data.token)
        localStorage.setItem('username', response.data.fullName)
        localStorage.setItem('userId', response.data.userId)
        localStorage.setItem('roleName', response.data.role)
        Swal.LoginSuccess()
        router.push('/')
      }
    })
    .catch(e => {
      processing.value = 2
      Swal.LoginFail()
      console.error(e.response.data)
    })
}

const EServiceAuthenticate = () => {
  clearSession()
  if (route.query.code) {
    const code = route.query.code

    login(code)
  } else {
    window.location.href = AUTHEN
  }
}

onMounted(() => {
  EServiceAuthenticate()
})
</script>

<template>
  <div class="auth-wrapper d-flex align-center justify-center pa-4">
    <VCard class="auth-card pa-2 pt-7 rounded-shaped" max-width="448">
      <VCardItem class="justify-center">
        <template #prepend>
          <div class="me-n2">
            <VNodeRenderer :nodes="themeConfig.app.logo" />
          </div>
        </template>
      </VCardItem>
      <VCardText class="pt-2 text-center">
        <h5 class="text-h5 mb-1">กำลังเข้าสู่ระบบ</h5>
        <VProgressCircular v-if="processing == 0" :size="60" color="info" indeterminate />
        <VIcon v-if="processing == 1" :size="60" color="success" icon="mdi-check-bold" />
        <VIcon v-if="processing == 2" :size="60" color="error" icon="mdi-close-thick" />
      </VCardText>
    </VCard>
    <VImg :src="authV1ThemeLoginMask" class="d-none d-md-block auth-footer-mask" />
    <div class="auth-logo" />
  </div>
</template>

<style lang="scss">
@use '@core/scss/template/pages/page-auth.scss';
</style>

<route lang="yaml">
meta:
  layout: blank
  action: read
  subject: Auth
  redirectIfLoggedIn: true
</route>
