export interface IGetSystemDepartmentRes {
  no: string
  id: string
  name: string
  shortName: string
  isActive: string
  createBy: Date
  createDate: Date
  sortCreateDate: Date
  updateBy: string
  updateDate: Date
  sortUpdateDate: Date
  subDepartment: ISubDepartment[]
}

export interface ISubDepartment {
  no: string
  id: string
  name: string
  shortName: string
  isActive: string
  createBy: Date
  createDate: Date
  sortCreateDate: Date
  updateBy: string
  updateDate: Date
  sortUpdateDate: Date
}

export interface IGetSystemLinkRes {
  no: string
  linkId: string
  linkName: string
  isActive: boolean
  url: string
  createBy: string
  createDate: string
  sortCreateDate: string
  updateBy: string
  updateDate: string
  sortUpdateDate: string
}

export interface IGetSystemLinkByIdRes {
  linkId: string
  linkName: string
  fileName: string
  pathName: string
  fileNamePic: string
  pathNamePic: string
  fileNamePDF: string
  pathNamePDF: string
  isActive: boolean
  isUrl: boolean
  isFile: boolean
  url: string
}

export interface IGetSystemLogoRes {
  logoId: string
  systemName: string
  logoName: string
  fileName: string
  pathName: string
  isActive: boolean
  createBy: string
  createDate: string
  sortCreateDate: string
  updateBy: string
  updateDate: string
  sortUpdateDate: string
}

export interface IGetSystemLogoByIdRes {
  logoId: string
  systemName: string
  logoName: string
  fileName: string
  pathName: string
  isActive: boolean
  createBy: string
  createDate: string
  sortCreateDate: string
  updateBy: string
  updateDate: string
  sortUpdateDate: string
}

export interface IGetSystemBannersRes {
  bannerId: string
  sorting: number
  bannerName: string
  fileName: string
  pathName: string
  url: string
  isActive: boolean
  createBy: string
  createDate: string
  sortCreateDate: string
  updateBy: string
  updateDate: string
  sortUpdateDate: string
}

export interface IGetSystemBannersByIdRes {
  banner_id: number
  banner_name: string
  file_name: string
  url: string
  is_active: boolean
  sorting: number
  create_date: Date
  create_by: string
  modilfy_date: Date | null
  modify_by: Date | null
  period_typeid: number
  start_date: Date | null
  end_date: Date | null
}

export interface IGetSystemWebpagesRes {
  no: string
  webpageId: string
  webpageName: string
  sorting: number
  subWebpage: any[]
  fileName: string
  pathName: string
  url: string
  isActive: boolean
  createBy: string
  createDate: string
  sortCreateDate: string
  updateBy: string
  updateDate: string
  sortUpdateDate: string
}

export interface IGetSystemWebpagesByIdRes {
  webpageId: string
  webpageName: string
  sorting: number
  fileName: string
  pathName: string
  url: string
  isActive: boolean
  createBy: string
  createDate: string
  sortCreateDate: string
  updateBy: string
  updateDate: string
  sortUpdateDate: string
  isNavbar: boolean
  isURL: boolean
  isSubWebpage: boolean
  parrentId: string
  contentId: string
}

export interface IGetSystemRolesRes {
  no: string
  roleId: string
  name: string
  code: string
  timeOut: number
  isActive: boolean
  createBy: string
  createDate: string
  sortCreateDate: string
  updateBy: string
  updateDate: string
  sortUpdateDate: string
}

export interface IGetSystemDepartmentByIdRes {
  groupId: string
  groupName: string
  id: string
  name: string
  shortName: string
  isActive: boolean
}

export interface IGetConfigurationEmailsRes {
  systemEmailServiceId: number
  systemId: number
  smtpHost: string
  smtpEmail: string
  smtpPort: string
  activessl: boolean
  activeAuthentication: boolean
  username: string
  password: string
  senderName: string
  timeOut: number
}

export interface IGetLogRes {
  no: string
  event: string
  fullName: string
  systemName: string
  sortDate: string
  actionDate: string
  logId: string
}

export interface IGetSystemBlockEmailRes {
  no: string
  blockEmailId: string
  email: string
  createBy: string
  createDate: string
  sortCreateDate: string
  updateBy: string
  updateDate: string
  sortUpdateDate: string
}

export interface IGetSystemTemplateRes {
  no: string
  templateId: string
  templateName: string
  templateColor: string
  pathName: string
  isActive: string
  isDefault: string
  createBy: string
  createDate: string
  sortCreateDate: string
  updateBy: string
  updateDate: string
  sortUpdateDate: string
}

export interface IGetSystemFooterRes {
  footerId: string
  address: string
  phone: string
  fax: string
  email: string
  facebook: string
  twitter: string
  instagram: string
  tiktok: string
  youtube: string
  provision: string
  policy: string
  isActive: boolean
  name: string
  detail: string
}

export interface IGetSystemFooterDataRes {
  no: number
  name: string
  detail: string
}

export interface ISystemFooterApiResponse {
  response: IGetSystemFooterRes
  data: IGetSystemFooterDataRes[]
}

export interface IGetSystemTemplateByIdRes {
  templateId: string
  templateName: string
  templateColor: string
  pathName: string
  isActive: boolean
  isDefault: boolean
}

export interface IGetSystemServiceTypeRes {
  no: string
  transactionServiceTypeId: string
  serviceTypeName: string
  year: string
  description: string
  isActive: string
  createBy: string
  createDate: string
  sortCreateDate: string
  updateBy: string
  updateDate: string
  sortUpdateDate: string
}

export interface IGetSystemServiceTypeByIdRes {
  transactionServiceTypeId: string
  serviceTypeName: string
  contentName: string
  isActive: boolean
  description: string
  year: string[]
  yearStart: string
  yearEnd: string
}
