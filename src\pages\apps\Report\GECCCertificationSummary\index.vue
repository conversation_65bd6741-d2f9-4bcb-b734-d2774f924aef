<script setup lang="ts">
import { useRouter } from 'vue-router'
import { VBreadcrumbs } from 'vuetify/lib/components/index.mjs'
import FiltersAPI from '@/components/FiltersAPI.vue'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'

const router = useRouter()
const callAxios = useAxios()
const Swal = useSwal()

const breadcrumbItems = [
  {
    title: 'รายงาน',
    disable: false,
    to: '/apps/report/menu'
  },
  {
    title: 'สรุปภาพรวมรักษามาตรฐาน',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const listFilter = reactive([
  {
    name: 'searchYear',
    type: 'dateYearRange',
    label: 'ปี (เริ่มต้น-สิ้นสุด)',
    placeholder: 'ระบุ ปี/ชื่อรอบการเปิดรับสมัคร'
  },
  {
    name: 'searchFormCode',
    type: 'text',
    label: 'เลขที่ใบสมัคร',
    default: '',
    placeholder: 'ตัวอย่าง : G670001'
  },
  {
    name: 'searchAgenciresMainName',
    type: 'select',
    label: 'ชื่อส่วนราชการ (เจ้าภาพหลัก)',
    default: '',
    title: 'name',
    value: 'id',
    items: [{ name: 'ทั้งหมด', id: '' }]
  },
  {
    name: 'searchAgenciresName',
    type: 'select',
    label: 'หน่วยงาน',
    default: '',
    title: 'name',
    value: 'id',
    items: [{ name: 'ทั้งหมด', id: '' }]
  },
  {
    name: 'searchProvince',
    type: 'select',
    label: 'จังหวัด',
    default: '',
    title: 'name',
    value: 'id',
    items: [{ name: 'ทั้งหมด', id: '' }]
  },
  {
    name: 'searchSubcommittee',
    type: 'select',
    label: 'คณะอนุกรรมการ',
    default: '',
    title: 'name',
    value: 'id',
    items: [{ name: 'ทั้งหมด', id: '' }]
  },
  {
    name: 'searchStandardAudit',
    type: 'select',
    label: 'ผลการตรวจรักษามาตรฐาน',
    default: '',
    title: 'StandardAudit',
    value: 'StandardAuditId',
    items: [
      { StandardAudit: 'ทั้งหมด', StandardAuditId: '' },
      { StandardAudit: 'ผ่าน', StandardAuditId: '1' },
      { StandardAudit: 'ไม่ผ่าน', StandardAuditId: '2' }
    ]
  },
  {
    name: 'searchWord',
    type: 'text',
    label: 'คำค้น',
    default: '',
    placeholder: 'ระบุ ศูนย์ราชการสะดวก/ระดับการรับรอง'
  }
])

const filter = reactive({
  searchYear: '',
  searchFormCode: '',
  searchAgenciresMainName: '',
  searchAgenciresName: '',
  searchProvince: '',
  searchSubcommittee: '',
  searchStandardAudit: '',
  searchWord: ''
})

const urlReport = ref('')

const GetList = async () => {
  try {
    const queryParams = new URLSearchParams()

    if (filter.searchYear) {
      const [DateStart, DateEnd] = filter.searchYear
      if (DateStart && DateEnd) {
        queryParams.append('StartDate', DateStart)
        queryParams.append('EndDate', DateEnd)
      }
    }

    if (filter.searchSubcommittee) queryParams.append('Subcommittee', filter.searchSubcommittee)

    if (filter.searchFormCode) queryParams.append('FormCode', filter.searchFormCode)

    if (filter.searchAgenciresMainName)
      queryParams.append('OrgGroup', filter.searchAgenciresMainName)

    if (filter.searchStandardAudit) queryParams.append('StandardAudit', filter.searchStandardAudit)

    // เพิ่มจังหวัด
    if (filter.searchProvince) queryParams.append('Province', filter.searchProvince)

    if (filter.searchWord) queryParams.append('Keyword', filter.searchWord)

    // เรียก API
    const response = await callAxios.RequestGet(`/Report/ReadReportAudit?${queryParams.toString()}`)

    // ตรวจสอบสถานะและจัดการผลลัพธ์
    if (response.status === 200) urlReport.value = response.data.response.reportLink
    else Swal.AddConditionFailText('ไม่สามารถดึงข้อมูลรายงานได้')
  } catch (error) {
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการดึงข้อมูลรายงาน')
    console.error(error)
  }
}

const listFields = ref([
  {
    field: 'applicationNumber',
    header: 'เลขที่ใบสมัคร',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'centerName',
    header: 'ชื่อศูนย์ราชการสะดวก',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'certificate',
    header: 'ระดับการรับรอง',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'certificateYear',
    header: 'ปีที่รับรอง',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'standardTreatment',
    header: 'ผลการตรวจรักษามาตราฐาน',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'information',
    header: 'ข้อมูลใบสมัคร',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'informationStandard',
    header: 'ข้อมูลใบตรวจประเมิน',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'dateChange',
    header: 'วันที่แก้ไข',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'lastEditedBy',
    header: 'ชื่อผู้แก้ไข',
    sortable: true,
    style: { textAlign: 'center' }
  }
])

const ListItem = ref([
  {
    applicationNumber: 'G670001',
    centerName: 'ศูนย์ราชการสะดวก A',
    certificate: 'ระดับพื่นฐาน',
    certificateYear: '2567-2569',
    standardTreatment: 'ผ่าน',
    dateChange: '01/01/2567 13:30:22',
    lastEditedBy: 'คณะกรรมการ'
  },
  {
    applicationNumber: 'G670002',
    centerName: 'ศูนย์ราชการสะดวก B',
    certificate: 'ระดับพื่นฐาน',
    certificateYear: '2567-2569',
    standardTreatment: 'ผ่าน',
    dateChange: '01/01/2567 13:30:22',
    lastEditedBy: 'คณะกรรมการ'
  },
  {
    applicationNumber: 'G670003',
    centerName: 'ศูนย์ราชการสะดวก C',
    certificate: 'ระดับพื่นฐาน',
    certificateYear: '2567-2569',
    standardTreatment: 'ผ่าน',
    dateChange: '01/01/2567 13:30:22',
    lastEditedBy: 'คณะกรรมการ'
  },
  {
    applicationNumber: 'G670004',
    centerName: 'ศูนย์ราชการสะดวก D',
    certificate: 'ระดับพื่นฐาน',
    certificateYear: '2567-2569',
    standardTreatment: 'ไม่ผ่าน',
    dateChange: '01/01/2567 13:30:22',
    lastEditedBy: 'คณะกรรมการ'
  },
  {
    applicationNumber: 'G670005',
    centerName: 'ศูนย์ราชการสะดวก E',
    certificate: 'ระดับพื่นฐาน',
    certificateYear: '2567-2569',
    standardTreatment: 'ผ่าน',
    dateChange: '01/01/2567 13:30:22',
    lastEditedBy: 'คณะกรรมการ'
  }
])

const getDropdownItems = async () => {
  try {
    // Fetch data for ชื่อส่วนราชการ (เจ้าภาพหลัก)
    let response = await callAxios.RequestGet('/OtherMaster/DepartmentParrentLists?flag=true')
    if (response.status === 200) {
      const DepartmentParrent = listFilter.find(filter => filter.name === 'searchAgenciresMainName')

      if (DepartmentParrent) {
        DepartmentParrent.items = [{ name: 'ทั้งหมด', id: '' }, ...response.data.response]
      }
    }

    // Fetch data for Province
    response = await callAxios.RequestGet('/OtherMaster/ProvinceLists')
    if (response.status === 200) {
      const Province = listFilter.find(filter => filter.name === 'searchProvince')

      if (Province) {
        Province.items = [{ name: 'ทั้งหมด', id: '' }, ...response.data.response]
      }
    }
  } catch (error) {
    console.error('Error fetching dropdown items:', error)
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการโหลดข้อมูล')
  }
}

// Fetch กรม/หน่วยงาน
const fetchDepartmentChildLists = async (departmentParrentId: string) => {
  if (!departmentParrentId) return

  try {
    const response = await callAxios.RequestGetById(
      '/OtherMaster/DepartmentChildLists',
      `?OrgGroupId=${departmentParrentId}&Id=${filter.searchAgenciresName}&flag=true`
    )

    if (response.status === 200) {
      const DepartmentChildLists = listFilter.find(filter => filter.name === 'searchAgenciresName')

      if (DepartmentChildLists) {
        DepartmentChildLists.items = [{ name: 'ทั้งหมด', id: '' }, ...response.data.response]
      }
    }
  } catch (error) {
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการโหลดข้อมูลอำเภอ')
  }
}

const fetchSubcommitteeLists = async (ProvinceId: string) => {
  if (!ProvinceId) return

  try {
    const response = await callAxios.RequestGetById(
      '/OtherMaster/SubcommitteeProviceLists',
      `?Province=${ProvinceId}`
    )

    if (response.status === 200) {
      const Submmittee = listFilter.find(filter => filter.name === 'searchSubcommittee')

      if (Submmittee) {
        Submmittee.items = [{ name: 'ทั้งหมด', id: '' }, ...response.data.response]
      }
    }
  } catch (error) {
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการโหลดข้อมูลอนุกรรมการ')
  }
}

watch(
  () => filter.searchAgenciresMainName,
  async newDepartmentParrent => {
    filter.searchAgenciresName = ''
    if (newDepartmentParrent !== '') {
      fetchDepartmentChildLists(newDepartmentParrent)
    } else {
      const DepartmentChildLists = listFilter.find(filter => filter.name === 'searchAgenciresName')

      if (DepartmentChildLists) DepartmentChildLists.items = [{ name: 'ทั้งหมด', id: '' }]
    }
  }
)

watch(
  () => filter.searchProvince,
  async newProvince => {
    filter.searchSubcommittee = ''
    if (newProvince !== '') {
      fetchSubcommitteeLists(newProvince)
    } else {
      const Submmittee = listFilter.find(filter => filter.name === 'searchSubcommittee')

      if (Submmittee) Submmittee.items = [{ name: 'ทั้งหมด', id: '' }]
    }
  }
)

onMounted(() => {
  getDropdownItems()
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <FiltersAPI v-model="filter" :fields="listFilter" @submit="GetList" />

    <VCard v-if="urlReport">
      <iframe
        v-if="urlReport"
        :src="urlReport"
        width="100%"
        height="800"
        frameborder="0"
        allowfullscreen
      />
    </VCard>

    <!--
      <VCard>
      <VCardText>
      <div class="d-flex align-center flex-wrap gap-4">
      <VBtn
      class="mt-2 border-error"
      color="background"
      border-color="error"
      rounded="xl"
      >
      <img src="@images/pdf.png" class="me-2" />
      ออกรายงาน PDF
      </VBtn>
      </div>
      </VCardText>
      <h4 class="ms-5 mb-5 text-primary">
      รวมทั้งหมด = 5 | ผ่าน 4 | ไม่ผ่าน = 1
      </h4>
      <AppDataTableAPI :columns="listFields" :value="ListItem">
      <template #information="slotProps">
      <div class="text-center">
      <IconBtn>
      <VIcon icon="mdi-eye-outline" />
      <VTooltip open-delay="500" location="top" activator="parent">
      แก้ไข
      </VTooltip>
      </IconBtn>
      </div>
      </template>
      <template #informationStandard="slotProps">
      <div class="text-center">
      <IconBtn>
      <VIcon icon="mdi-eye-outline" />
      <VTooltip open-delay="500" location="top" activator="parent">
      แก้ไข
      </VTooltip>
      </IconBtn>
      </div>
      </template>
      </AppDataTableAPI>
      </VCard>
    -->
    <VRow>
      <VCol cols="12" class="d-flex justify-end mt-5 me-4">
        <BtnGoBack />
      </VCol>
    </VRow>
  </div>
</template>
