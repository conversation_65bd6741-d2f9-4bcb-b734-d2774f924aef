<script setup>
import { useAxios } from '@/store/useAxios'

const callAxios = useAxios()
const search = ref(null)

const breadcrumbItems = [
  {
    title: 'แบบประเมินความพึงพอใจ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'KBP Survey',
    disabled: false,
    to: ''
  },
  {
    title: 'แบบประเมินความพึงพอใจของผู้มีส่วนได้ส่วนเสีย',
    disabled: false,
    to: ''
  },
  {
    title: 'ประวัติการส่งแบบประเมินความพึงพอใจของผู้มีส่วนได้ส่วนเสีย',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const columns = ref([
  {
    field: 'sendDate',
    header: 'วันที่ส่ง',
    sortable: true
  },
  {
    field: 'evaluationName',
    header: 'ชื่อแบบประเมิน',
    sortable: true
  },
  {
    field: 'subject',
    header: 'เรื่อง',
    sortable: true
  },
  {
    field: 'options',
    header: 'การจัดการ'
  }
])

const items = ref([])

const GetKBPHistorySendEmails = id => {
  const endpoint = '/EvaluationForm/GetKBPHistorySendEmails'

  callAxios.RequestGet(endpoint).then(response => {
    if (response.status == 200) items.value = response.data.response
  })
}

onMounted(() => {
  GetKBPHistorySendEmails()
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <BtnGoBack />
    <VCard>
      <VCardText>
        <VRow justify="space-between" align="center">
          <VCol>
            <BtnInsert
              class="py-2"
              :to="{ name: 'apps-SatisfactionAssessmentForm-KBPSurvey-MOC-KBPS-10-Send' }"
              color="success-200"
            >
              ส่งแบบประเมิน
            </BtnInsert>
          </VCol>
          <VCol cols="12" md="3">
            <VTextField v-model="search" label="ค้นหา" placeholder="กรอกข้อมูลที่ต้องการค้นหา" />
          </VCol>
        </VRow>
        <VSpacer />
      </VCardText>
      <AppDataTable :filters="search" :columns="columns" :value="items">
        <template #options="slotProps">
          <div class="text-center">
            <IconBtn
              :to="{
                name: 'apps-SatisfactionAssessmentForm-KBPSurvey-MOC-KBPS-10-Preview-id',
                params: { id: slotProps.data.id }
              }"
            >
              <VIcon icon="mdi-eye-outline" />
              <VTooltip open-delay="500" location="top" activator="parent">ดูข้อมูล</VTooltip>
            </IconBtn>
          </div>
        </template>
      </AppDataTable>
    </VCard>
  </div>
</template>
