<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'

const props = defineProps({
  queryId: Number,
  formQuestion: [Object],
  formQuery: {},
  formQueryStatus: {}
})

const router = useRouter()
const callAxios = useAxios()
const Swal = useSwal()

const form = reactive({
  additionalNote: '',
  additionalEstimate: ''
})

const isDialogVisible = ref(false)

const listFields = ref([
  {
    field: 'detail',
    header: 'รายละเอียด',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'actualize',
    header: 'การดำเนินการ',
    sortable: false,
    style: { textAlign: 'center' }
  },
  {
    field: 'dataregis',
    header: 'กรอกข้อมูลใบสมัคร',
    sortable: false,
    style: { textAlign: 'center' }
  },
  {
    field: 'note',
    header: 'ผลการประเมิน/หมายเหตุ',
    sortable: false,
    style: { textAlign: 'center' }
  }
])

const listFieldsFile = ref([
  {
    field: 'fileName',
    header: 'สิ่งอำนวยความสะดวก',
    sortable: false
  },
  {
    field: 'refFile',
    header: 'รายการไฟล์',
    sortable: false
  },
  {
    field: 'createDate',
    header: 'วันที่บันทึก',
    sortable: false
  },
  {
    field: 'options',
    header: 'การจัดการ',
    sortable: false
  }
])

const ListItem = ref([
  {
    id: '86c17f01-3c33-4b8e-9308-0fdc19912a9b',
    detail:
      'ดีลเลอร์ เชอร์รี่แบดเอสเปรสโซ ฮ็อตด็อกติงต๊องจ๊าบ ช็อปซี้สุริยยาตร รีโมตบ๊วยมยุราภิรมย์ดอกเตอร์ บุ๋น ฟอยล์ซีดานออร์เดอร์ บุญคุณวิก ปาร์ตี้ มอคคาซี้ ดีเจ เดชานุภาพบ๊วยซาบะเทรนด์ รีสอร์ทไฮเวย์แหววดั๊มพ์อาข่า สจ๊วตดีพาร์ทเมนท์กรุ๊ปเฟรช สปอร์ตหม่านโถว เซ่นไหว้ฉลุยรีวิวเกรดสตาร์ธรรมาภิบาล ฮาโลวีนทำงาน เอ็นจีโอจัมโบ้แช่แข็ง ออโต้โลชั่นแป๋วเบบี้จุ๊ย เมจิกบอดี้ล็อบบี้ ',
    actualize: 'มี',
    dataregis: [
      {
        id: '6706a6b1-8d9f-489c-b7e9-0a4c8cd3e8de',
        fileName: 'WithholdingTax.pdf',
        refFile: 'file01.pdf',
        createDate: '10/10/2567 เวลา 14:32 น.'
      },
      {
        id: '8004173f-f221-4ee2-b1ea-fcd8a67305bd',
        fileName: '11708e69bdca.docx',
        refFile: 'file01.pdf',
        createDate: '10/10/2567 เวลา 14:32 น.'
      }
    ]
  }
])

const selectedRadio = ref(0)
const radioGroup = ['ผ่าน', 'ไม่ผ่าน', 'รอประเมิน']
const currentStep = ref(0)
</script>

<template>
  <VCard class="mb-3">
    <div class="my-2">
      <VCardTitle>แบบประเมินความพร้อม</VCardTitle>
    </div>
    <VRow>
      <VCol col="12" md="12" lg="12">
        <AppDataTableAPI :paginator="false" :columns="listFields" :value="ListItem">
          <template #dataregis="slotProps">
            <AppDataTableAPI
              :paginator="false"
              :columns="listFieldsFile"
              :value="slotProps.data.dataregis"
            />
          </template>

          <template #note="slotProps">
            <Row>
              <VCol>
                <VRadioGroup v-model="selectedRadio">
                  <VRadio v-for="index in radioGroup" :key="index" :label="index" :value="index" />
                </VRadioGroup>
                <VTextarea
                  v-model="form.additionalEstimate"
                  placeholder="ตัวอย่าง : หน่วยงานไม่ผ่านการประเมินตนเอง"
                  :counter="300"
                  :maxlength="300"
                  density="comfortable"
                />
              </VCol>
            </Row>
          </template>
        </AppDataTableAPI>
      </VCol>
    </VRow>
  </VCard>

  <AddFormTopic v-model="isDialogVisible" />
</template>
