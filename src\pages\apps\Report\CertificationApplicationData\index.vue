<script setup lang="ts">
import { useRouter } from 'vue-router'
import { VBreadcrumbs } from 'vuetify/lib/components/index.mjs'
import FiltersAPI from '@/components/FiltersAPI.vue'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'

const router = useRouter()
const callAxios = useAxios()
const Swal = useSwal()
const dateRange = ref('')

const form = reactive({
  dateyear: '',
  orgGoverment: '',
  departmentChildLists: ''
})

const breadcrumbItems = [
  {
    title: 'รายงาน',
    disable: false,
    to: '/apps/report/menu'
  },
  {
    title: 'รายงานข้อมูลการสมัคร',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const listFilter = reactive([
  {
    name: 'reportYearlyAndDaily',
    type: 'radio',
    items: [
      { value: 'yearly', label: 'ออกรายงานรายปี' },
      { value: 'daily', label: 'ออกรายงานรายวัน' }
    ],
    default: ''
  },
  {
    name: 'searchYear',
    label: computed(() => {
      if (filter.reportYearlyAndDaily === 'yearly') {
        filter.searchYear = ''

        return 'ปี'
      } else {
        filter.searchYear = ''

        return 'วันที่'
      }
    }),
    required: true,
    default: '',
    title: '',
    value: '',
    type: computed(() => {
      if (filter.reportYearlyAndDaily === 'yearly') return 'dateYear'
      else return 'dateRange'
    }),
    items: []
  },
  {
    name: 'searchAgenciresMainName',
    type: 'select',
    label: 'ชื่อส่วนราชการ (เจ้าภาพหลัก)',
    default: '',
    title: 'name',
    value: 'id',
    items: [{ name: 'ทั้งหมด', id: '' }]
  },
  {
    name: 'searchAgenciresName',
    type: 'select',
    label: 'หน่วยงาน',
    default: '',
    title: 'name',
    value: 'id',
    items: [{ name: 'ทั้งหมด', id: '' }]
  },
  {
    name: 'searchProvince',
    label: 'จังหวัด',
    type: 'select',
    default: '',
    title: 'name',
    value: 'id',
    items: [{ name: 'ทั้งหมด', id: '' }],
    placeholder: 'ค้นหาจังหวัด'
  },
  {
    name: 'reportYear',
    type: 'reportButton',
    label: 'ออกรายงานรายวัน',
    color: 'warning'
  },
  {
    name: 'reportProvince',
    type: 'reportButton',
    label: 'ออกรายงานรายจังหวัด',
    color: 'warning'
  },
  {
    name: 'reportAgencires',
    type: 'reportButton',
    label: 'ออกรายงานรายเจ้าภาพ',
    color: 'warning'
  }
])

const filter = reactive({
  searchYear: '',
  buttonType: '',
  searchAgenciresMainName: '',
  searchAgenciresName: '',
  searchProvince: '',
  reportProvince: '',
  reportAgencires: '',
  reportYearlyAndDaily: 'yearly'
})

const adjustYear = dateString => {
  const [year, month, day] = dateString.split('-')

  return `${Number(year) + 543}-${month}-${day}`
}

const urlReport = ref('')

const GetList = async () => {
  try {
    const queryParams = new URLSearchParams()

    if (filter.reportYearlyAndDaily === 'yearly') {
      queryParams.append('IsYear', 'true')
      if (filter.searchYear) queryParams.append('Year', filter.searchYear.toString())
    } else if (filter.reportYearlyAndDaily === 'daily') {
      queryParams.append('IsYear', 'false')

      const [DateStart, DateEnd] = filter.searchYear
      if (DateStart && DateEnd) {
        queryParams.append('StartDate', adjustYear(DateStart))
        queryParams.append('EndDate', adjustYear(DateEnd))
      }
    }
    if (filter.buttonType) queryParams.append('Type', filter.buttonType)

    // เพิ่มส่วนราชการ (เจ้าภาพหลัก)
    if (filter.searchAgenciresMainName)
      queryParams.append('OrgGroup', filter.searchAgenciresMainName)

    // เพิ่มหน่วยงาน
    if (filter.searchAgenciresName) queryParams.append('OrgStructure', filter.searchAgenciresName)

    // เพิ่มจังหวัด
    if (filter.searchProvince) queryParams.append('Province', filter.searchProvince)

    // เรียก API
    const response = await callAxios.RequestGet(
      `/Report/ReadReportRegister?${queryParams.toString()}`
    )

    // ตรวจสอบสถานะและจัดการผลลัพธ์
    if (response.status === 200) urlReport.value = response.data.response.reportLink
    // urlReport.value = "https://report.opmgecc.com/ReportForm/RP_RegisteDate.aspx?Code=V21wQk1sbFVSVFJaTWtsMFdXcHNhMDVUTURCT1ZHY3hURmRGTlU5RVozUk5NazVzV21wS2JFNHlTVEphYWs1b1NtcEpkMDFxVlhkTlZFa3dUV3BKZDA1VVFYYz0=&StartDate=2568-01-24&EndDate=2568-01-24&Type=1&OrgGroup=&OrgStructure=&Province=";
    else Swal.AddConditionFailText('ไม่สามารถดึงข้อมูลรายงานได้')
  } catch (error) {
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการดึงข้อมูลรายงาน')
    console.error(error)
  }
}

const getDropdownItems = async () => {
  try {
    // Fetch data for ชื่อส่วนราชการ (เจ้าภาพหลัก)
    let response = await callAxios.RequestGet('/OtherMaster/DepartmentParrentLists?flag=true')
    if (response.status === 200) {
      const DepartmentParrent = listFilter.find(filter => filter.name === 'searchAgenciresMainName')

      if (DepartmentParrent) {
        DepartmentParrent.items = [{ name: 'ทั้งหมด', id: '' }, ...response.data.response]
      }
    }

    // Fetch data for Province
    response = await callAxios.RequestGet('/OtherMaster/ProvinceLists')
    if (response.status === 200) {
      const Province = listFilter.find(filter => filter.name === 'searchProvince')

      if (Province) {
        Province.items = [{ name: 'ทั้งหมด', id: '' }, ...response.data.response]
      }
    }
  } catch (error) {
    console.error('Error fetching dropdown items:', error)
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการโหลดข้อมูล')
  }
}

// Fetch กรม/หน่วยงาน
const fetchDepartmentChildLists = async (departmentParrentId: string) => {
  if (!departmentParrentId) return

  try {
    const response = await callAxios.RequestGetById(
      '/OtherMaster/DepartmentChildLists',
      `?OrgGroupId=${departmentParrentId}&Id=${filter.searchAgenciresName}&flag=true`
    )

    if (response.status === 200) {
      const DepartmentChildLists = listFilter.find(filter => filter.name === 'searchAgenciresName')

      if (DepartmentChildLists) {
        DepartmentChildLists.items = [{ name: 'ทั้งหมด', id: '' }, ...response.data.response]
        if (DepartmentChildLists.items.length === 1) {
          form.departmentChildLists = (DepartmentChildLists.items[0] as { id: string }).id
        }
      }
    }
  } catch (error) {
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการโหลดข้อมูลอำเภอ')
  }
}

const onReportButtonClick = async (buttonName: string) => {
  if (
    !filter.searchYear ||
    (Array.isArray(filter.searchYear) && filter.searchYear.some(date => !date))
  ) {
    Swal.validateText('กรุณากรอกข้อมูลปีหรือช่วงวันที่ก่อนดำเนินการ')

    return
  }
  if (buttonName === 'reportYear') filter.buttonType = '1'
  else if (buttonName === 'reportProvince') filter.buttonType = '2'
  else if (buttonName === 'reportAgencires') filter.buttonType = '3'

  await GetList()
}

watch(
  () => filter.searchAgenciresMainName,
  async newDepartmentParrent => {
    filter.searchAgenciresName = ''
    if (newDepartmentParrent !== '') {
      fetchDepartmentChildLists(newDepartmentParrent)
    } else {
      const DepartmentChildLists = listFilter.find(filter => filter.name === 'searchAgenciresName')

      if (DepartmentChildLists) DepartmentChildLists.items = [{ name: 'ทั้งหมด', id: '' }]
    }
  }
)

onMounted(() => {
  getDropdownItems()
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <FiltersAPI v-model="filter" :fields="listFilter" @reportButtonClick="onReportButtonClick" />

    <VCard v-if="urlReport">
      <!-- <h6>{{ urlReport }}</h6> -->
      <iframe
        v-if="urlReport"
        :src="urlReport"
        width="100%"
        height="800"
        frameborder="0"
        allowfullscreen
      />
    </VCard>
    <VRow>
      <VCol cols="12" class="d-flex justify-end mt-5 me-4">
        <BtnGoBack />
      </VCol>
    </VRow>
  </div>
</template>
