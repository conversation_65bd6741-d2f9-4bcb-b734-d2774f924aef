{"compilerOptions": {"baseUrl": "./", "target": "ES2020", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "isolatedModules": true, "strict": true, "jsx": "preserve", "jsxFactory": "h", "jsxFragmentFactory": "Fragment", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "paths": {"@/*": ["src/*"], "@themeConfig": ["themeConfig.ts"], "@layouts/*": ["src/@layouts/*"], "@layouts": ["src/@layouts"], "@core/*": ["src/@core/*"], "@core": ["src/@core"], "@utils": ["src/plugins/utils.ts"], "@images/*": ["src/assets/images/*"], "@styles/*": ["src/styles/*"], "@axios": ["src/plugins/axios"], "@validators": ["src/@core/utils/validators"], "@interfaces/*": ["src/@core/interfaces/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"], "skipLibCheck": true, "types": ["vite/client", "vite-plugin-pages/client", "vite-plugin-vue-layouts/client", "unplugin-vue-define-options/macros-global"]}, "include": ["vite.config.*", "env.d.ts", "shims.d.ts", "src/**/*", "src/**/*.vue", "themeConfig.ts", "auto-imports.d.ts", "components.d.ts"], "exclude": ["dist", "node_modules", "src/@iconify/*"], "plugins": [{"name": "@vue/typescript-plugin"}]}