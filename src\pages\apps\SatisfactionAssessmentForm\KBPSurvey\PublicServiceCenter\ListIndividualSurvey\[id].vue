<script setup>
const route = useRoute()
const paramsId = ref(Number(route.params.id))

const breadcrumbItems = [
  {
    title: 'แบบประเมินความพึงพอใจ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'KBP Survey',
    disabled: false,
    to: ''
  },
  {
    title: 'แบบประเมินความพึงพอใจการให้บริการ',
    disabled: false,
    to: ''
  },
  {
    title: 'แบบประเมินความพึงพอใจการให้บริการของสำนักงานพาณิชย์จังหวัด (รายบุคคล)',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <EvaluationForm title="แบบประเมินความพึงพอใจการให้บริการของสำนักงานพาณิชย์จังหวัด" />
  </div>
</template>
