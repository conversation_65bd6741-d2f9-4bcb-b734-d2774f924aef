<script setup lang="ts">
import type { Ref } from 'vue'
import { onMounted, reactive, ref } from 'vue'
import type { IGetSystemDepartmentRes } from '@/src/@core/interfaces/UserManagementInterface'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import { useStatus } from '@/store/useStatus'
import { IRoles } from '@/@core/interfaces/AllStatusMaster'
import { format } from 'date-fns'
// Dependencies
const callAxios = useAxios()
const Swal = useSwal()
const router = useRouter()

// Reactive State
const searchQuery: Ref<string | number> = ref('')
const currentPage = ref(1)
const rowPerPage = ref(20)
const totalCount = ref(0)
const ListItem = ref<IGetSystemDepartmentRes[]>([])
const statusState = useStatus()
const selectedData = ref<any[]>([null])
const roleList = ref<IRoles[]>([])
// Breadcrumb Items
const breadcrumbItems = ref([
  { title: 'ผู้ดูแลระบบ', disabled: false, to: '/apps/home' },
  {
    title: 'อนุมัติการลงทะเบียน',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
])
onBeforeMount(async () => {
  if (statusState.listProvince.length == 0) {
    await statusState.getProvince()
  }
  if (statusState.listTitles.length == 0) {
    await statusState.getTitles()
  }
  if (statusState.listRoles.length == 0 && statusState.listRolesFilter.length == 0) {
    await statusState.getRoles()
  }
  if (statusState.listOrganizations.length == 0) {
    await statusState.getOrganizations()
  }
})
// Filters
const approveStatus = [
  { name: 'ทั้งหมด', value: null },
  { name: 'รออนุมัติ', value: 2 },
  { name: 'อนุมัติ', value: 3 },
  { name: 'ไม่อนุมัติ', value: 4 }
]

const listFilter = ref([
  {
    name: 'dateRange',
    type: 'dateRange',
    label: 'วันที่ลงทะเบียน (เริ่มต้น-สิ้นสุด)',
    default: ''
  },
  {
    name: 'role_id',
    type: 'select',
    label: 'สิทธิการใช้งาน',
    default: '',
    value: 'role_id',
    title: 'role_name',
    items: computed(() => statusState.listRolesFilter)
  },

  {
    name: 'province_id',
    type: 'select',
    label: 'จังหวัด',
    default: '',
    title: 'province_name',
    value: 'province_id',
    items: computed(() => statusState.listProvinceFilter)
  },
  {
    name: 'district_id',
    type: 'select',
    label: 'อำเภอ',
    default: '',
    title: 'district_name',
    value: 'district_id',
    items: computed(() => statusState.listDistrictFilter)
  },
  {
    name: 'is_approve',
    type: 'select',
    label: 'สถานะการลงทะเบียน',
    default: '',
    value: 'value',
    title: 'name',
    items: approveStatus
  },
  {
    name: 'keyWord',
    type: 'text',
    label: 'คำค้น',
    default: '',
    placeholder: 'ระบุ ชื่อผู้ใช้งาน/อีเมล'
  }
])
const filter = reactive({
  keyWord: '',
  is_active: '',
  role_id: null,
  district_id: null,
  province_id: null,
  dateRange: null,
  is_approve: null
})
// Data Table Fields
const listFields = ref([
  {
    field: 'options',
    header: 'การจัดการ',
    sortable: false,
    style: { textAlign: 'center' }
  },
  { field: 'no', header: 'ลำดับที่', sortable: true },
  { field: 'first_name', header: 'ชื่อผู้ใช้งาน', sortable: true },
  { field: 'email', header: 'อีเมล', sortable: true },
  { field: 'province', header: 'จังหวัด', sortable: true },
  { field: 'district', header: 'อำเภอ', sortable: true },
  { field: 'role_id', header: 'สิทธิการใช้งาน', sortable: true },
  { field: 'org_structure', header: 'สถานะการลงทะเบียน', sortable: true },
  { field: 'create_date', header: 'วันที่สร้าง', sortable: true },
  { field: 'create_by', header: 'ชื่อผู้สร้าง', sortable: true },
  { field: 'modify_date', header: 'วันที่แก้ไข', sortable: true },
  {
    field: 'modify_by',
    header: 'ชื่อผู้แก้ไข',
    sortable: true,
    sortField: 'sortUpdateDate'
  }
])

watch(filter, async (newVal, oldVal) => {
  filter.district_id = null
  if (newVal.province_id) {
    statusState.getDistrict(newVal.province_id)
  }
})

// Fetch List Data
const GetList = async (isLoading = true) => {
  try {
    const queryParams = new URLSearchParams({
      current_page: currentPage.value.toString(),
      page_size: rowPerPage.value.toString()
    })

    if (filter.keyWord) queryParams.append('keyword', filter.keyWord)
    if (filter.province_id) queryParams.append('province_id', filter.province_id)
    if (filter.district_id) queryParams.append('district_id', filter.district_id)
    if (filter.role_id) queryParams.append('role_id', filter.role_id)
    queryParams.append('is_approve', filter.is_approve==null?1:filter.is_approve)
    if (filter.dateRange && filter.dateRange?.length === 2) {
      queryParams.append('from_date', format(new Date(dateRange.value[0]), 'yyyy-MM-dd').toString())
      queryParams.append('to_date', format(new Date(dateRange.value[1]), 'yyyy-MM-dd').toString())
    }

    const response = await callAxios.RequestGet(
      `/users/search?${queryParams.toString()}`,
      isLoading
    )

    if (response.status === 200) {
      ListItem.value = response.data.data
      totalCount.value = response.data.total_rows
    } else {
      onNotFound()
    }
  } catch (error) {
    onNotFound()
  }
}

const onNotFound = () => {
  ListItem.value = []
  totalCount.value = 0
  currentPage.value = 0
  rowPerPage.value = 10
}

const onPageChange = (event: { page: number; rows: number }) => {
  currentPage.value = event.page + 1
  rowPerPage.value = event.rows
  GetList()
}

const ApproveDelete = async (id: number) => {
  const endpoint = `/users/${id}`
  const response = await Swal.ApproveDelete(endpoint)
  if (response) GetList()
}

const handleEdit = (id: number) => {
  statusState.setUsetId(id, 'edit')
  router.push({
    name: 'apps-admin-ApproveRegister-Manage'
  })
}

const handleView = (id: number) => {
  statusState.setUsetId(id, 'view')
  router.push({
    name: 'apps-admin-ApproveRegister-Manage'
  })
}

onMounted(() => {
  GetList()
  statusState.setUsetId(null, 'add')
  roleList.value = statusState.listRoles
})
const selected = (data: any) => {
  selectedData.value = data
}

const appprove = async () => {
  const confirmResponse = await Swal.ApproveConfirmCustom(
    'ท่านต้องการอนุมัติรายการดังนี้ใช่หรือไม่ ?',
    'อนุมัติ'
  )
  if (confirmResponse) {
    const req = selectedData.value.map((item: any) => {
      return {
        user_id: item.user_id,
        redirect_url: `${window.location.origin}/public/login`
      }
    })
    const response = await callAxios.RequestPost(`/users/approve-all`, req)
    if (response.status === 200 || response.status === 201) {
      Swal.AddSuccess()
      GetList(false)
    } else {
      Swal.AddConditionFailText(response.data?.message || 'Request failed. Please try again.')
    }
  }
}
</script>
a

<template>
  <div>
    <!-- Breadcrumbs -->
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <FiltersAPI v-model="filter" :fields="listFilter" @submit="GetList" />

    <!-- Data Table -->
    <VCard class="px-4">
      <VCardText>
        <div class="d-flex align-center flex-wrap gap-4">
          <VBtn
            :disabled="selectedData.length < 1"
            @click="appprove"
            class="mt-2 text-white"
            color="bg-btn-save"
          >
            อนุมัติ
          </VBtn>
        </div>
      </VCardText>
      <AppDataTable
        :filters="searchQuery"
        :columns="listFields"
        :value="ListItem"
        :total-records="totalCount"
        :header-no="false"
        :selectionMode="true"
        @update:selection="value => selected(value)"
        @page="onPageChange"
      >
        <template #no="slotProps">
          <div class="text-center">
            {{ slotProps.index + 1 }}
          </div>
        </template>
        <template #role_id="slotProps">
          {{ statusState.getRoleUser(slotProps.data.role_id)?.role_name }}
        </template>
        <template #first_name="slotProps">
          <div class="text-center">
            {{
              `${slotProps.data.title_name} ${slotProps.data.first_name} ${slotProps.data.last_name}`
            }}
          </div>
        </template>
        <template #is_active="slotProps">
          <div class="text-center">
            <ChipStatus
              :status="slotProps.data.is_active"
              :label="slotProps.data.is_active ? 'ใช้งาน' : 'ไม่ใช้งาน'"
            />
          </div>
        </template>

        <!-- Action Column -->
        <template #options="slotProps">
          <div class="text-center">
            <IconBtn @click="handleView(slotProps.data.user_id)">
              <VIcon icon="mdi-eye-outline" />
              <VTooltip open-delay="500" location="top" activator="parent">ดูข้อมูล</VTooltip>
            </IconBtn>
            <IconBtn
              v-if="slotProps.data.is_approve == null"
              @click="handleEdit(slotProps.data.user_id)"
            >
              <VIcon icon="mdi-pencil-outline" />
              <VTooltip open-delay="500" location="top" activator="parent">แก้ไข</VTooltip>
            </IconBtn>
            <IconDelete @click="ApproveDelete(slotProps.data.user_id)">
              <VIcon icon="mdi-delete-outline" />
              <VTooltip open-delay="500" location="top" activator="parent">ลบข้อมูล</VTooltip>
            </IconDelete>
          </div>
        </template>
      </AppDataTable>
    </VCard>
  </div>
</template>

<style scoped>
.no-select {
  user-select: none;
}
</style>
