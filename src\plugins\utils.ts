import { useAuth } from '@/store/useAuth'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'

const callAxios = useAxios()
const Auth = useAuth()
const Swal = useSwal()

export const formatInput = (value: string | number) => {
  if (value === '') return 0

  // Convert value to a string if it's not already
  value = value.toString()

  let isNegative = false

  // Check if the input starts with a minus sign
  if (value.charAt(0) === '-') {
    isNegative = true
    value = value.substring(1) // Remove the minus sign
  }

  let inputValue = value.replace(/[^0-9.]/g, '').replace(/\.(?=.*\.)/g, '')

  // Remove leading zeros
  if (inputValue.length > 1 && inputValue.charAt(0) === '0') inputValue = inputValue.substring(1)

  if (inputValue.includes('.')) {
    const parts = inputValue.split('.')

    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    inputValue = `${parts[0]}.${parts[1] ? parts[1].substring(0, 2) : ''}`
  } else {
    const parts = inputValue.split('.')

    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    inputValue = parts.join('.')
    if (parts.length > 1) inputValue = parseFloat(inputValue).toFixed(2)
  }

  // Add the minus sign back if it was initially negative
  if (isNegative) inputValue = `-${inputValue}`

  return inputValue
}

// แปลงข้อมูลจากวันที่เป็นรูปแบบที่ใช้ในการค้นหา api เช่น ได้วันที่รูปแบบนี้มา 2023-06-08T07:45:02.954Z แปลงให้เป็น 08/06/2566
export const formatDateTH = (value: string | number | Date) => {
  const date = new Date(value)

  const options = {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }

  return date.toLocaleDateString('th-TH', options)
}

// เปลี่ยนข้อมูลจาก 08/06/2566 แปลงให้เป็น 2023-06-08T07:45:02.954Z
export const formatDateToDatePicker = (value: any) => {
  // Input date string: 08/06/2566
  const inputDate = value

  // Split the date string into day, month, and year
  const parts = inputDate.split('/')

  // Create a new Date object using the extracted values
  const date = new Date(parts[2] - 543, parts[1] - 1, parts[0])

  // Get the ISO 8601 string representation of the date in UTC format
  return date.toISOString()
}

export const TreeSelectConvertToOptions = (options: any, selectedOptions: { [x: string]: any }) => {
  const filterOptions = (options: any[]) => {
    return options.filter((option: { children: any; id: string | number }) => {
      if (option.children) {
        const filteredChildren = filterOptions(option.children)

        option.children = filteredChildren

        return filteredChildren.length > 0
      } else {
        return selectedOptions[option.id]
      }
    })
  }

  return filterOptions(options)
}

export const TreeSelectConvertToValue = (list: any[]) => {
  return list.reduce(
    (acc: { [x: string]: { checked: boolean; partialChecked: boolean } }, item: { id: any }) => {
      const { id } = item

      acc[id] = { checked: true, partialChecked: false }

      return acc
    },
    {}
  )
}

export const deepEqual = (obj1: { [x: string]: any } | null, obj2: { [x: string]: any } | null) => {
  if (obj1 === obj2) return true

  if (typeof obj1 !== 'object' || obj1 === null || typeof obj2 !== 'object' || obj2 === null)
    return false

  const keys1 = Object.keys(obj1)
  const keys2 = Object.keys(obj2)

  if (keys1.length !== keys2.length) return false

  for (const key of keys1) {
    if (!deepEqual(obj1[key], obj2[key])) return false
  }

  return true
}

export const formatCurrency = (value: {
  toLocaleString: (arg0: string, arg1: { style: string; currency: string }) => any
}) => {
  return value.toLocaleString('th-TH', { style: 'currency', currency: 'THB' })
}
export const hasDuplicates = (list: any, property: string | number, valueToCheck: any) => {
  let count = 0

  for (const item of list) {
    if (item[property] === valueToCheck) {
      count++
      if (count > 1) return true // Duplicate found with the specific value in the specified property
    }
  }

  return false // No duplicate values found for the specified property
}
export const convertToCurrency = (value: any) => {
  if (typeof value === 'number') return formatInput(value)

  return value
}

export const convertToCurrencyPoint = (value: any) => {
  if (typeof value === 'number') return formatInput(value.toFixed(2)) // แปลงเลขให้เป็นทศนิยมสองตำแหน่ง

  return formatInput(value)
}

const calculateDifference = (base: number, value: number) => {
  return value - base
}

export const calculatePercentageTotal = (base: number, value: number) => {
  // Check if the necessary data is available and not zero
  if (!base || base === 0 || !value) return '' // Handle invalid data here

  if (base === 0) return '' // Handle division by zero

  const total = (value / base) * 100

  // Check if the result is NaN or Infinity
  if (isNaN(total) || !isFinite(total)) return '' // Handle invalid result

  return `${total.toFixed(2)} %`
}

export const validateOnFocus = (value: { id: string }) => {
  if (value.id) {
    const firstErrorFieldElement = document.getElementById(value.id)
    if (firstErrorFieldElement) {
      console.log(firstErrorFieldElement)

      return firstErrorFieldElement.focus()
    } else {
      // When DateTimePicker is not found Id
      const currentIdNumber = parseInt(value.id.split('-')[1])
      if (!isNaN(currentIdNumber)) {
        const newIdNumber = currentIdNumber + 1
        const inputId = `dp-input-input-${newIdNumber}`
        const ErrorFieldElement = document.getElementById(inputId)
        if (ErrorFieldElement) {
          return ErrorFieldElement.focus()
        } else {
          // When it's be VTextarea
          const textAreaId = `input-${newIdNumber}`
          const ErrorTextareaElement = document.getElementById(textAreaId)
          if (ErrorTextareaElement) return ErrorTextareaElement.focus()
        }
      }
    }
  }
}

export const getComponentByRoute = async (routeName: string) => {
  const response = await callAxios.RequestGet(
    `/UserManagement/MenuPermissionRole?Path=${routeName}`
  )

  if (response.status === 200) return response.data.response
}

export const filterMenu = async menu => {
  const route = useRoute()
  const components = await getComponentByRoute(route.name)

  if (components) {
    return menu.filter(menuItem => {
      const foundItem = components.find(item => item === menuItem.toRaw)

      return foundItem !== undefined
    })
  }
}

export const canPermission = async (routeName: string) => {
  const response = await callAxios.RequestGet(`/UserManagement/MenuPermission?Path=${routeName}`)

  if (response.status === 200) return response.data.menuShow
}

export const MenuPermissionHome = async () => {
  const response = await callAxios.RequestGet('/UserManagement/MenuPermissionRolePathHome')

  if (response.status === 200) return response.data.response
}

export const filterMenuGECCHome = async menu => {
  const route = useRoute()
  const components = await MenuPermissionHome(route.name)

  if (components) {
    return menu.filter(menuItem => {
      const foundItem = components.find(item => item === menuItem.toRaw)

      return foundItem !== undefined
    })
  }
}

export const MenuPermission = async (routeName: string) => {
  const response = await callAxios.RequestGet(
    `/UserManagement/MenuPermissionRolePath?Path=${routeName}`
  )

  if (response.status === 200) return response.data.response
}

export const filterMenuGECC = async menu => {
  const route = useRoute()
  const components = await MenuPermission(route.name)

  if (components) {
    return menu.filter(menuItem => {
      const foundItem = components.find(item => item === menuItem.toRaw)

      return foundItem !== undefined
    })
  }
}

// เปลี่ยนข้อมูลจาก timeStamp แปลงให้เป็น 2023-06-08
// แนะนำสำหรับออกรายงาน
export const formatDateToDefaultDate = value => {
  const date = new Date(value)

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0') // Month is zero-based
  const day = String(date.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}
