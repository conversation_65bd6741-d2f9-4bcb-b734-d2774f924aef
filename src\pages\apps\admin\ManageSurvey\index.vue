<script setup lang="ts">
import { useRouter } from 'vue-router'
import DialogEditSurvey from './DialogEditSurvey.vue'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'

const callAxios = useAxios()
const Swal = useSwal()
const router = useRouter()

const isDialogVisible = ref(false)
const isView: Ref<boolean> = ref(false)
const isEdit: Ref<boolean> = ref(false)
const isAdd: Ref<boolean> = ref(false)
const editId: Ref<string> = ref('')

const currentPage = ref(1)
const rowPerPage = ref(20)
const ListItem = ref<[]>([])
const totalCount = ref(0)
const inProcess = ref(false)

const handleConfirmEdit = () => {
  Swal.ApproveConfirm().then(response => {
    if (response) {
      // ถ้าผู้ใช้ยืนยัน จะเรียกใช้ฟังก์ชันสำหรับอัปเดตข้อมูล
    }
  })
}

const breadcrumbItems = [
  {
    title: 'ผู้ดูแลระบบ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'จัดการแบบสำรวจความคิดเห็น',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const listFilter = ref([
  {
    name: 'Status',
    type: 'select',
    label: 'สถานะ',
    default: '',
    title: 'title',
    value: 'value',
    items: [
      { title: 'ทั้งหมด', value: '' },
      { title: 'ไม่ใช้งาน', value: 'false' },
      { title: 'ใช้งาน', value: 'true' }
    ],
    placeholder: 'กรอกข้อมูลที่ต้องการค้นหา'
  },
  {
    name: 'Keyword',
    type: 'text',
    label: 'คำค้น',
    default: '',
    items: [],
    placeholder: 'ระบุ แบบสำรวจความคิดเห็น'
  }
])

const filter = reactive({
  Keyword: '',
  Status: ''
})

const listFields = ref([
  {
    field: 'no',
    header: 'ลำดับที่',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'systemAssessmentsName',
    header: 'แบบสำรวจความคิดเห็น',
    sortable: true
  },
  {
    field: 'isActive',
    header: 'สถานะ',
    sortable: true
  },
  {
    field: 'createDate',
    header: 'วันที่สร้าง',
    sortable: true
  },
  {
    field: 'createBy',
    header: 'ชื่อผู้สร้าง',
    sortable: true
  },
  {
    field: 'updateDate',
    header: 'วันที่แก้ไข',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'updateBy',
    header: 'ชื่อผู้แก้ไข',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'options',
    header: 'การจัดการ',
    sortable: true,
    style: { textAlign: 'center' }
  }
])

const openAddDialog = () => {
  isAdd.value = true
  isEdit.value = false
  isView.value = false
  isDialogVisible.value = true
}

const openEditDialog = (id: string) => {
  isEdit.value = true
  isView.value = false
  isAdd.value = false
  isDialogVisible.value = true
  editId.value = id
}

const openview = (id: string) => {
  router.push({
    name: 'apps-admin-ManageSurvey-SurveyViewForm',
    query: { id }
  })
}

const openEditForm = (id: string) => {
  router.push({
    name: 'apps-admin-ManageSurvey-SurveyEditForm',
    query: { id }
  })
}

const GetList = async () => {
  try {
    console.log('GetList', filter)

    const response = await callAxios.RequestGet(
      `/UserManagement/GetSystemAssessments?Page=${currentPage.value}&PageSize=${rowPerPage.value}&Keyword=${filter.Keyword}&Status=${filter.Status}`
    )

    if (response.status === 200) {
      if (response.data.count > 0) {
        totalCount.value = response.data.count
        ListItem.value = response.data.response

        inProcess.value = false
      } else {
        ListItem.value = []
        inProcess.value = false
      }
    } else {
      ListItem.value = []
      inProcess.value = false

      // Swal.isNotFound()
      onNotFound()
    }
  } catch (error) {
    console.log('error', error)
    Swal.callCatch()
  }
}

const ApproveDelete = async (id: number) => {
  const endpoint = `/UserManagement/DeleteSystemAssessments?SystemAssessmentsId=${id}`
  try {
    const response = await Swal.ApproveDelete(endpoint)
    if (response) await GetList()
  } catch (error) {
    console.error('Error approving delete:', error)
  }
}

const onPageChange = (event: { page: number; rows: number }) => {
  currentPage.value = event.page + 1
  rowPerPage.value = event.rows
  GetList()
}

const onNotFound = () => {
  ListItem.value = []
  totalCount.value = 0
  currentPage.value = 0
  rowPerPage.value = 0
}

const createCopyFormData = (id: string, name: string) => {
  return {
    systemAssessmentsId: id,
    systemAssessmentsName: `${name} (คัดลอก)`,
    isActive: false
  }
}

const Copy = async (systemAssessmentsId: string, systemAssessmentsName: string) => {
  const response = await Swal.ApproveCondition('ยืนยันการคัดลอกข้อมูล')
  if (response) {
    const formData = createCopyFormData(systemAssessmentsId, systemAssessmentsName)

    try {
      await callAxios.RequestPost('/UserManagement/CopySystemAssessments', formData)
      Swal.CopySuccess()
      GetList()
    } catch (error) {
      console.error('Error during the copy operation:', error)
    }
  }
}

onMounted(() => {
  GetList()
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <FiltersAPI v-model="filter" :fields="listFilter" @submit="GetList" />

    <VCard class="px-4">
      <VCardText>
        <div class="d-flex align-center flex-wrap gap-4">
          <BtnInsert
            class="mt-2"
            prepend-icon="mdi-plus-box-multiple"
            color="btn-add"
            rounded="xl"
            @click="openAddDialog"
          >
            เพิ่มรายการ
          </BtnInsert>
        </div>
      </VCardText>
      <AppDataTableAPI
        :columns="listFields"
        :value="ListItem"
        :total-records="totalCount"
        :header-no="false"
        @page="onPageChange"
      >
        <!-- กำหนดสีของ Chip ในคอลัมน์สถานะ -->
        <template #isActive="slotProps">
          <div class="text-center">
            <ChipStatus
              :status="slotProps.data.isActive"
              :label="slotProps.data.isActive === 'ใช้งาน' ? 'ใช้งาน' : ''"
            />
          </div>
        </template>
        <!-- ส่วน Icon การจัดการ -->
        <template #options="slotProps">
          <div class="text-center">
            <IconBtn @click="openview(slotProps.data.systemAssessmentsId)">
              <VIcon icon="mdi-eye-outline" />
              <VTooltip open-delay="500" location="top" activator="parent">ดูแบบฟอร์ม</VTooltip>
            </IconBtn>
            <IconBtn @click="openEditDialog(slotProps.data.systemAssessmentsId)">
              <VIcon icon="mdi-pencil-outline" />
              <VTooltip open-delay="500" location="top" activator="parent">
                แก้ไขชื่อแบบฟอร์ม/สถานะ
              </VTooltip>
            </IconBtn>
            <IconBtn @click="openEditForm(slotProps.data.systemAssessmentsId)">
              <VIcon icon="mdi-file-document-edit-outline" />
              <VTooltip open-delay="500" location="top" activator="parent">
                แก้ไขแบบประเมิน
              </VTooltip>
            </IconBtn>
            <IconBtn
              @click="
                Copy(slotProps.data.systemAssessmentsId, slotProps.data.systemAssessmentsName)
              "
            >
              <VIcon icon="mdi-content-copy" />
              <VTooltip open-delay="500" location="top" activator="parent">
                Copy แบบประเมิน
              </VTooltip>
            </IconBtn>
            <IconBtn @click="ApproveDelete(slotProps.data.systemAssessmentsId)">
              <VIcon icon="mdi-delete-outline" />
              <VTooltip open-delay="500" location="top" activator="parent">ลบข้อมูล</VTooltip>
            </IconBtn>
          </div>
        </template>
      </AppDataTableAPI>
    </VCard>

    <DialogEditSurvey
      v-model="isDialogVisible"
      :is-add="isAdd"
      :is-edit="isEdit"
      :edit-id="editId"
      :view-id="editId"
      @update="GetList"
    />
  </div>
</template>

<style scoped>
.no-select {
  user-select: none;
}
</style>
