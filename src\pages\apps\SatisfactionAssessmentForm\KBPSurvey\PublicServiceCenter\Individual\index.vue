<script setup>
import { filterMenu } from '@utils'

const inProcess = ref(true)

const breadcrumbItems = [
  {
    title: 'แบบประเมินความพึงพอใจ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'KBP Survey',
    disabled: false
  },
  {
    title: 'แบบประเมินความพึงพอใจการให้บริการ',
    disabled: false
  },
  {
    title: 'แบบประเมินความพึงพอใจการให้บริการ (รายบุคคล)',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const ListsMenu = ref([
  {
    title: 'สร้างแบบประเมินความพึงพอใจการให้บริการ (รายบุคคล)',
    toRaw: 'สร้างแบบประเมินความพึงพอใจการให้บริการ (รายบุคคล)',
    to: {
      name: 'apps-SatisfactionAssessmentForm-KBPSurvey-PublicServiceCenter-ListIndividualSurvey'
    },
    icon: 'mdi-note-plus',
    color: 'blue-700'
  },
  {
    title: 'บันทึกแบบประเมินความพึงพอใจการให้บริการ (รายบุคคล)',
    toRaw: 'บันทึกแบบประเมินความพึงพอใจการให้บริการ (รายบุคคล)',
    to: {
      name: 'apps-SatisfactionAssessmentForm-KBPSurvey-PublicServiceCenter-ListIndividualSurvey-Submit'
    },
    icon: 'mdi-note-plus',
    color: 'error-600'
  },
  {
    title: 'รายงานสรุปความพึงพอใจภาพรวม',
    toRaw: 'รายงานสรุปความพึงพอใจภาพรวม',
    to: {
      name: 'apps-SatisfactionAssessmentForm-KBPSurvey-Dashboard-Satisfaction'
    },
    icon: 'mdi-chart-box',
    color: 'blue-700'
  },
  {
    title: 'รายงานสรุปข้อคิดเห็น ข้อเสนอแนะ เพื่อปรับปรุงการให้บริการ',
    toRaw: 'รายงานสรุปข้อคิดเห็นข้อเสนอแนะเพื่อปรับปรุงการให้บริการ',
    to: {
      name: 'apps-Report-id',
      params: { id: 1 },
      query: { controller: 'RP_011_KBP_SummarySuggestion' }
    },
    icon: 'mdi-message-text',
    color: 'orange-200'
  },
  {
    title: 'รายงานสรุปคะแนนการใช้บริการ',
    toRaw: 'รายงานสรุปคะแนนการใช้บริการ',
    to: {
      name: 'apps-Report-id',
      params: { id: 1 },
      query: { controller: 'RP_012_KBP_SummaryScore' }
    },
    icon: 'mdi-message-text',
    color: 'error-600'
  },
  {
    title: 'รายงานสรุปการเรียกรับผลประโยชน์',
    toRaw: 'รายงานสรุปการเรียกรับผลประโยชน์',
    to: {
      name: 'apps-Report-id',
      params: { id: 1 },
      query: { controller: 'RP_013_KBP_SummaryReceiveBenefits' }
    },
    icon: 'mdi-message-text',
    color: 'orange-300'
  },
  {
    title: 'รายงานสรุปความพึงพอใจรายบุคคล',
    toRaw: 'รายงานสรุปความพึงพอใจรายบุคคล',
    to: {
      name: 'apps-SatisfactionAssessmentForm-KBPSurvey-Dashboard-IndividualSatisfaction'
    },
    icon: 'mdi-message-text',
    color: 'navy'
  }
])

onMounted(async () => {
  if (ListsMenu.value) {
    ListsMenu.value = await filterMenu(ListsMenu.value)
    inProcess.value = false
  } else {
    inProcess.value = false
  }
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <!-- {{ filter }} -->
    <VCard title="" class="mb-5">
      <VCardText>
        <BtnGoBack />
        <div v-if="inProcess" class="text-center">
          <VProgressCircular :size="60" color="primary" indeterminate />
        </div>
        <VRow v-else class="align-center">
          <VCol
            v-for="(item, index) in ListsMenu"
            :key="index"
            cols="12"
            md="6"
            class="align-center"
          >
            <RouterLink :to="item.to">
              <VAlert prominent variant="outlined" :color="item.color" :icon="item.icon">
                {{ item.title }}
              </VAlert>
            </RouterLink>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
  </div>
</template>
