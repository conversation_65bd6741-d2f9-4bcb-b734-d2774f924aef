<script setup>
import { useRoute, useRouter } from 'vue-router'
import { useTheme } from 'vuetify'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import { formatDateThai } from '@core/utils/formatters'

const route = useRoute()
const router = useRouter()
const callAxios = useAxios()
const Swal = useSwal()
const vuetifyTheme = useTheme()
const themeColors = vuetifyTheme.current.value.colors

const currentDate = new Date()

const year = currentDate.getFullYear()
const month = currentDate.getMonth() + 1 // เดือนเริ่มที่ 0
const day = currentDate.getDate()

const formattedDate = `${year}-${month}-${day}`

const breadcrumbItems = [
  {
    title: 'แบบประเมินความพึงพอใจ',
    disabled: false
  },
  {
    title: 'MOCSC Survey',
    disabled: false
  },
  {
    title: 'แบบประเมินความพึงพอใจการให้บริการศูนย์บริการประชาชน',
    disabled: false
  },
  {
    title: 'รายงานสรุปความพึงพอใจภาพรวม',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const itemsProvince = []
const itemsSector = []

const itemsFilter1 = [
  {
    id: 0,
    name: 'รวมทั้งประเทศ'
  },
  {
    id: 1,
    name: 'รายจังหวัด'
  },
  {
    id: 2,
    name: 'ภาค'
  }
]

const itemsFilter2 = [
  {
    id: 0,
    name: 'รวมทุกหน่วยงาน'
  },
  {
    id: 1,
    name: 'ฝ่ายบริหารงานทั่วไป (บท.)'
  },
  {
    id: 2,
    name: 'กลุ่มยุทธศาสตร์และแผนงาน (ยผ.)'
  },
  {
    id: 3,
    name: 'กลุ่มกำกับและพัฒนาเศรษฐกิจการค้า (กศ.)'
  },
  {
    id: 4,
    name: 'กลุ่มส่งเสริมการประกอบธุรกิจการค้าและการตลาด (สธ.)'
  },
  {
    id: 5,
    name: 'กลุ่มทะเบียนธุรกิจและอำนวยความสะดวกทางการค้า (ทอ.)'
  }
]

const columns = ref([
  {
    field: 'name',
    header: 'ประเด็น',
    sortable: false
  },
  {
    field: 'satis',
    header: 'ระดับความพึงพอใจ (ร้อยละ)',
    sortable: false,
    style: { textAlign: 'center' }
  },
  {
    field: 'criter',
    header: 'เกณฑ์ประเมิน',
    sortable: false,
    style: { textAlign: 'center' }
  }
])

const filters = ref({
  searchProvince: 0,
  searchProvinceById: 0,
  searchRegion: 0,
  searchGroup: 'รวมทุกหน่วยงาน',
  searchValue: 0,
  searchDate: [formattedDate, formattedDate]
})

const solidCardData = [
  {
    cardBg: '#0079FF',
    text: 'สรุปผลความพึงพอใจต่องานบริการของศูนย์บริการประชาชนกระทรวงพาณิชย์',
    textColor: 'white'
  },
  {
    cardBg: '',
    text: 'ร้อยละความพึงพอใจต่อการบริการโดยรวม',
    textColor: 'black'
  }
]

const solidCardDataPick = ref([
  {
    searchProvince: null,
    searchGroup: null,
    searchDate: null,
    searchCount: null
  },
  {
    searchProvince: null,
    searchGroup: null,
    searchCount: null,
    total: 0
  }
])

const dataDashboard = ref({})

const GetList = (
  searchProvince = 0,
  searchValue = 0,
  searchGroup = 'รวมทุกหน่วยงาน',
  date = null
) => {
  if (!date) date = [null, null]

  callAxios
    .RequestGet(
      `/EvaluationForm/GetSurveyDashboard?Type=${searchProvince === null ? 0 : searchProvince}&Value=${searchValue === null ? 0 : searchValue}&Service=${searchGroup}&StartDate=${date[0] ? date[0] : null}&EndDate=${date[1] ? date[1] : null}&EvaluationSystemId=2&EvaluationSystemType=7`
    )
    .then(response => {
      if (response.status == 200) {
        if (
          typeof response.data.response === 'object' &&
          Object.keys(response.data.response).length === 0
        ) {
          Swal.AddConditionFailText('ไม่พบข้อมูลของวันที่กำหนด')
        } else {
          dataDashboard.value = response.data.response
          solidCardDataPick.value[1].total = dataDashboard.value.datasRadarChart.total
        }
      }
    })
}

const SystemOrgStructureList = ref([])

const GetSystemOrgStructureList = () => {
  callAxios.RequestGet('/OtherMaster/GetProvinces').then(response => {
    if (response.status == 200) {
      SystemOrgStructureList.value = response.data.response
      SystemOrgStructureList.value.unshift({
        provinceId: 0,
        provinceName: 'เลือกจังหวัด'
      })
    }
  })
}

GetSystemOrgStructureList()

const SystemServiceLists = ref([])

const GetSystemServiceLists = () => {
  callAxios.RequestGet('/OtherMaster/SystemServiceLists').then(response => {
    if (response.status == 200) {
      SystemServiceLists.value = response.data.response
      SystemServiceLists.value.unshift({ id: 0, name: 'รวมทุกหน่วยงาน' })
    }
  })
}

GetSystemServiceLists()

const ZoneList = ref([])

const GetZoneList = () => {
  callAxios.RequestGet('/OtherMaster/ZoneList').then(response => {
    if (response.status == 200) {
      ZoneList.value = response.data.response
      ZoneList.value.unshift({
        id: 0,
        name: 'เลือกภาค'
      })
    }
  })
}

GetZoneList()

const submitFilter = () => {
  dataDashboard.value = {}
  GetList(
    filters.value.searchProvince,
    filters.value.searchValue,
    filters.value.searchGroup,
    filters.value.searchDate
  )
}

watch(filters.value, val => {
  solidCardDataPick.value[0].searchDate =
    val.searchDate !== null ? getNameFilterDate(filters.value.searchDate) : null
  solidCardDataPick.value[1].searchCount = val.searchCount !== null ? val.searchCount : null

  const dateValue = []

  dateValue[0] = val.searchDate !== null ? val.searchDate[0] : null
  dateValue[1] = val.searchDate !== null ? val.searchDate[1] : null
})

function getNameFilter1(id) {
  const foundItem = itemsFilter1.find(item => item.id === id)

  return foundItem ? foundItem.name : null
}
function getNameFilter2(id) {
  const foundItem = itemsFilter2.find(item => item.id === id)

  return foundItem ? foundItem.name : null
}
function getNameFilterDate(date) {
  console.log(date)
  if (date) return `ตั้งแต่วันที่ ${formatDateThai(date[0])} ถึง ${formatDateThai(date[1])}`
  else return ''
}

const selectProvince = val => {
  filters.value.searchValue = null
}

const searchProvinceById = val => {
  filters.value.searchValue = val
}

const searchRegion = val => {
  filters.value.searchValue = val
}

const GetDashboard = () => {
  const newTab = window.open(
    `/apps/satisfactionassessmentform/mocscsurvey/dashboard/satisfaction/print?date=${filters.value.searchDate}`,
    '_blank'
  )
}
</script>

<template>
  <section>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <VRow class="mb-2" align="center">
      <VCol cols="12" class="d-flex flex-wrap">
        <div class="demo-space-x">
          <VBtn prepend-icon="mdi-arrow-left-circle" variant="text" color="grey-900" @click="back">
            ย้อนกลับ
          </VBtn>
        </div>
      </VCol>
    </VRow>
    <VCard>
      <VCardText>
        <VRow class="mb-4 mx-2 mt-6">
          <VCol cols="12" md="4">
            <VCard>
              <VCardItem class="bg-success">
                <VCardTitle>เลือกช่วงวันที่</VCardTitle>
              </VCardItem>
              <VCardText class="mt-5">
                <DateTimePicker v-model="filters.searchDate" range :allowed-dates="[2567]" />
              </VCardText>
            </VCard>
          </VCol>
        </VRow>
        <VRow>
          <VCol cols="12">
            <div class="d-flex flex-wrap gap-4 justify-center">
              <VBtn prepend-icon="mdi-file-document" color="success-200" @click="GetDashboard">
                ออกรายงาน
              </VBtn>
              <VBtn prepend-icon="mdi-search" color="secondary" @click="submitFilter">ค้นหา</VBtn>
            </div>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
    <!-- <Filters :fields="listFilter" :modelValue="filters"/> -->

    <VRow v-if="dataDashboard.datasList">
      <VCol cols="12">
        <VCard>
          <VCardText>
            <HeaderDashboard
              class="mb-5"
              :solid-card-data="solidCardData[0]"
              :solid-card-data-pick="solidCardDataPick[0]"
            />
            <ListDashboard :datas="dataDashboard.datasList" />
            <AppDataTable
              v-model="dataDashboard.datasTable"
              :columns="columns"
              :header-no="false"
              :paginator="false"
            />
          </VCardText>
        </VCard>
      </VCol>
    </VRow>
    <VRow v-if="dataDashboard.datasBarChartGroup">
      <VCol cols="12">
        <VCard>
          <VCardText>
            <HeaderDashboard
              class="mb-5"
              :solid-card-data="solidCardData[0]"
              :solid-card-data-pick="solidCardDataPick[0]"
            />
            <VRow v-if="dataDashboard.datasHeader">
              <VCol cols="12">
                <VCard color="#D9EDF7">
                  <VCardText align="center">
                    <p class="clamp-text mb-0">
                      {{ dataDashboard.datasHeader.header }}
                    </p>
                    <h1 class="clamp-text mb-0">{{ dataDashboard.datasHeader.total }} ราย</h1>
                  </VCardText>
                </VCard>
              </VCol>
            </VRow>
            <VRow>
              <VCol cols="12" md="12" sm="12">
                <VAlert color="secondary">
                  <h3 class="text-white">
                    {{ dataDashboard.datasBarChartGroup.header }}
                  </h3>
                </VAlert>
              </VCol>
              <VCol cols="12" md="6" sm="12">
                <BarChart :datas-bar-chart="dataDashboard.datasBarChartGroup.byGroup" />
              </VCol>
              <VCol cols="12" md="6" sm="12">
                <BarChart :datas-bar-chart="dataDashboard.datasBarChartGroup.bySex" />
              </VCol>
            </VRow>
            <VRow>
              <VCol v-if="dataDashboard.datasBarChartGroup.byAge" cols="12" md="6" sm="12">
                <BarChart :datas-bar-chart="dataDashboard.datasBarChartGroup.byAge" />
              </VCol>
              <VCol v-if="dataDashboard.datasBarChartGroup.byEducation" cols="12" md="6" sm="12">
                <BarChart :datas-bar-chart="dataDashboard.datasBarChartGroup.byEducation" />
              </VCol>
            </VRow>
            <VRow>
              <VCol cols="12" md="12" sm="12">
                <BarChart :datas-bar-chart="dataDashboard.datasBarChartGroup.byJob" />
              </VCol>
            </VRow>

            <VRow v-if="dataDashboard.datasRadarChart">
              <VCol cols="12" md="12" sm="12">
                <VAlert color="secondary">
                  <h3 class="text-white">
                    {{ dataDashboard.datasBarChartGroup.header }}
                  </h3>
                </VAlert>
              </VCol>
              <VCol cols="12" md="12" sm="12">
                <HeaderDashboard
                  :solid-card-data="solidCardData[1]"
                  :solid-card-data-pick="solidCardDataPick[1]"
                />
              </VCol>
              <VCol cols="12" md="12" sm="12">
                <RadarChart :datas-radar-chart="dataDashboard.datasRadarChart.data" />
              </VCol>
            </VRow>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>
  </section>
</template>
