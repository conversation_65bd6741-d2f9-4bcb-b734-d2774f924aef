<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import { requiredValidator } from '@validators'

const baseIMGURL = localStorage.baseUrlImg

const route = useRoute()
const router = useRouter()
const callAxios = useAxios()
const Swal = useSwal()

const paramId = route.params.id

const refVForm = ref(null)

function goBack() {
  router.go(-1)
}

const breadcrumbItems = [
  {
    title: 'ขอสิทธิ์การใช้งานระบบ',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const form = ref({})
const formPersonal = ref({})

const GetListPersonal = () => {
  callAxios
    .RequestGet(`/SystemMaster/GetRequestAuthorizePersonalUpdate?RequestAuthorizeId=${paramId}`)
    .then(response => {
      if (response.status == 200) formPersonal.value = response.data.response
    })
}

GetListPersonal()

const ListAuthorizeType = ref([])

const GetListAuthorizeType = () => {
  callAxios.RequestGet('/SystemMaster/GetRequestAuthorizeType').then(response => {
    if (response.status == 200) ListAuthorizeType.value = response.data.response

    // ListAuthorizeType.value.items.unshift({
    //   id: 0,
    //   name: "กรุณาเลือก",
    // });
  })
}

GetListAuthorizeType()

const ListAuthorizeRoles = ref([])

const GetListAuthorizeRoles = () => {
  callAxios.RequestGet('/OtherMaster/GetDropdownListRoles').then(response => {
    if (response.status == 200) ListAuthorizeRoles.value = response.data.response

    // ListAuthorizeRoles.value.items.unshift({
    //   id: 0,
    //   name: "กรุณาเลือก",
    // });
  })
}

GetListAuthorizeRoles()

const StatusId = ref(null)

const AddForm = () => {
  callAxios
    .RequestPost(
      `/SystemMaster/AddRequestAuthorize?RequestAuthorizeId=${paramId}&RequestAuthorizeStatusId=${StatusId.value}`,
      form.value
    )
    .then(response => {
      if (response.status == 200) {
        // router.push({ path: "/apps/moccomplain/managechannels" });
        goBack()
      }
    })
}

const onFormSubmit = () => {
  refVForm.value?.validate().then(({ valid: isValid }: { valid: boolean }) => {
    if (isValid) AddForm()
  })
}

const GetListById = requestAuthorizeId => {
  callAxios
    .RequestGetById(
      '/SystemMaster/GetRequestAuthorizeById',
      `?RequestAuthorizeId=${requestAuthorizeId}`
    )

    .then(response => {
      if (response.status == 200) form.value = response.data.response
    })
}

GetListById(paramId)
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <VCard title="ส่วนที่ 1 กรอกข้อมูลส่วนบุคคล" class="mb-6 mt-5 pb-5">
      <VCardText>
        <VRow class="align-center">
          <VCol cols="12" md="2" class="text-lg-end">
            <label>ชื่อ - นามสกุล :</label>
          </VCol>
          <VCol cols="12" md="4">
            <!-- 👉 outlined variant -->
            <VTextField
              v-model="formPersonal.name"
              density="compact"
              readonly
              bg-color="grey-secondary"
            />
          </VCol>
          <VCol cols="12" md="2" class="text-lg-end">
            <label>หน่วยงาน :</label>
          </VCol>
          <VCol cols="12" md="4">
            <!-- 👉 outlined variant -->
            <VTextField
              v-model="formPersonal.orgStructureName"
              density="compact"
              readonly
              bg-color="grey-secondary"
            />
          </VCol>
          <VCol cols="12" md="2" class="text-lg-end">
            <label>เบอร์โทรศัพท์ :</label>
          </VCol>
          <VCol cols="12" md="4">
            <!-- 👉 outlined variant -->
            <VTextField
              v-model="formPersonal.telephone"
              density="compact"
              readonly
              bg-color="grey-secondary"
            />
          </VCol>
          <VCol cols="12" md="2" class="text-lg-end">
            <label>อีเมล :</label>
          </VCol>
          <VCol cols="12" md="4">
            <!-- 👉 outlined variant -->
            <VTextField
              v-model="formPersonal.email"
              density="compact"
              readonly
              bg-color="grey-secondary"
            />
          </VCol>
        </VRow>
      </VCardText>
    </VCard>

    <VForm ref="refVForm" @submit.prevent="onFormSubmit">
      <VCard title="ส่วนที่ 2 กรอกข้อมูลสิทธิ์ที่ต้องการ" class="mb-6 mt-5 pb-5">
        <VCardText>
          <VRow class="align-center">
            <VCol cols="12" md="6">
              <label>
                ประเภทการขอรับสิทธิ์ :
                <small class="text-error">*</small>
              </label>
              <VAutocomplete
                v-model="form.requestAuthorizeTypeId"
                item-title="name"
                item-value="requestAuthorizeTypeId"
                :items="ListAuthorizeType"
                :rules="[requiredValidator]"
                no-data-text="ไม่มีข้อมูล"
                readonly
                bg-color="grey-secondary"
              />
            </VCol>

            <VCol v-if="form.requestAuthorizeTypeId !== 3" cols="12" md="6">
              <label>
                สิทธิ์ที่ต้องการ :
                <small class="text-error">*</small>
              </label>
              <VAutocomplete
                v-model="form.roleId"
                item-title="roleName"
                item-value="roleId"
                :items="ListAuthorizeRoles"
                :rules="[requiredValidator]"
                no-data-text="ไม่มีข้อมูล"
                readonly
                bg-color="grey-secondary"
              />
            </VCol>

            <VCol cols="12" md="12">
              <label>รายละเอียด (ถ้ามี) :</label>
              <VTextarea
                v-model="form.detail"
                density="compact"
                readonly
                bg-color="grey-secondary"
              />
            </VCol>
          </VRow>
        </VCardText>
      </VCard>

      <VCard v-if="form.isAdmin" title="ส่วนที่ 3 หมายเหตุจากผู้ดูแลระบบ" class="mb-6 mt-5 pb-5">
        <VCardText>
          <VRow class="align-center">
            <VCol cols="12" md="12">
              <label>หมายเหตุ (ถ้ามี) :</label>
              <VTextarea
                v-model="form.comment"
                density="compact"
                readonly
                bg-color="grey-secondary"
              />
            </VCol>
          </VRow>
        </VCardText>
      </VCard>

      <VRow>
        <VCol cols="12" class="d-flex flex-wrap justify-end mt-5">
          <BtnGoBack />
        </VCol>
      </VRow>
    </VForm>
  </div>
</template>
