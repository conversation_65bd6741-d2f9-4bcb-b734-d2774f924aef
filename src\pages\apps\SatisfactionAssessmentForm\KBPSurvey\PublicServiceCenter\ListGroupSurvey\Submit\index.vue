<script setup>
import { useAxios } from '@/store/useAxios'

const callAxios = useAxios()
const items = ref([])

const breadcrumbItems = [
  {
    title: 'แบบประเมินความพึงพอใจ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'KBP Survey',
    disabled: false,
    to: ''
  },
  {
    title: 'แบบประเมินความพึงพอใจการให้บริการ',
    disabled: false,
    to: ''
  },
  {
    title: 'รายการแบบประเมินความพึงพอใจการให้บริการ (รายกลุ่ม)',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const isDialogDisable = ref(false)

const submitDialogFormEvaluation = event => {
  callAxios.RequestPost('/EvaluationForm/AddKBPSurveyGroup', event).then(response => {
    if (response.status == 200) {
      isDialogDisable.value = true
      GetKBPSurveyGroups()
    }
  })
}

const GetKBPSurveyGroups = () => {
  callAxios.RequestGet('/EvaluationForm/GetKBPSurveyGroups').then(response => {
    if (response.status == 200)
      setAttribute.value.items = response.data.response.filter(x => x.isActive === 'ใช้งาน')
  })
}

const setAttribute = ref({
  urlFilter: 'GetKBPSurveyGroups',
  toQR: 'apps-SatisfactionAssessmentForm-KBPSurvey-PublicServiceCenter-ListGroupSurvey-QRcodeSubmit-id',
  toQROfficer: true,
  toPrint:
    'apps-SatisfactionAssessmentForm-KBPSurvey-PublicServiceCenter-ListGroupSurvey-Example-id',
  toTransaction:
    'apps-SatisfactionAssessmentForm-KBPSurvey-PublicServiceCenter-ListGroupSurvey-Transaction-id',
  to: 'apps-SatisfactionAssessmentForm-KBPSurvey-PublicServiceCenter-ListGroupSurvey-id',
  items: []
})

onMounted(() => {
  GetKBPSurveyGroups()
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <BtnGoBack />
    <ListsTable
      v-bind="setAttribute"
      v-model:isDialogDisable="isDialogDisable"
      @submit="event => submitDialogFormEvaluation(event)"
      @update:modelValue="GetKBPSurveyGroups"
    />
  </div>
</template>
