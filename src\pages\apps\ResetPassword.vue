<script setup lang="ts">
import { VCol, VRow } from 'vuetify/lib/components/index.mjs'
import Footer from '@/layouts/components/Footer.vue'
import { useAppAbility } from '@/plugins/casl/useAppAbility'
import { useAuth } from '@/store/useAuth'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import bgLogin from '@images/pages/bgLogin.png'
import { confirmedValidator, passwordValidator, requiredValidator } from '@validators'

const callAxios = useAxios()
const callAuth = useAuth()
const AppAbility = useAppAbility()
const Swal = useSwal()
const router = useRouter()
const route = useRoute()
const refVForm = ref()

const tooltipTitle = 'เงื่อนไขในการกำหนดรหัสผ่าน'

const tooltipContent = `
  1. มีความยาว 8-12 ตัวอักษร<br />
  2. ตัวอักษรตัวแรกเป็นตัวพิมพ์ใหญ่ (A-Z) <br />
  3. ตัวอักษรถัดไปสามารถเป็น a-z อย่างน้อย 1 ตัวอักษร อักษรพิเศษ (#,?,!,@,$,%,^,&,*,-)<br />
  อย่างน้อย 1 ตัวอักษร และตัวเลข (0-9) อย่างน้อย 1 ตัวอักษร
`

// ดึงค่าจาก query string และแปลงเป็นตัวพิมพ์เล็ก
const Token = (route.query.Token as string) || ''
const Code = (route.query.Code as string) || ''
const Expire = (route.query.Expire as string) || ''

interface FormType {
  password: string
  confirmPassword: string
}

const form: Ref<FormType> = ref({
  password: '',
  confirmPassword: ''
})

const isPasswordVisible = ref(false)

const ChangePassword = async () => {
  try {
    const confirmResponse = await Swal.ApproveConfirm()

    if (confirmResponse) {
      const response = await callAxios.RequestPut('/Authenticate/ChangePassword', {
        token: Token,
        code: Code,
        expire: Expire,
        password: form.value.password // เพิ่มค่า password ลงใน payload
      })

      if (response.status === 200) {
        router.push({ path: '/apps/login' })
        console.log(response)
        Swal.ConditionSuccessText(response.data?.message || 'Password changed successfully.')
      } else {
        Swal.AddConditionFailText(response.data?.message || 'Request failed. Please try again.')
      }
    }
  } catch (error) {
    Swal.AddConditionFailText(
      error.response?.data?.message || 'Bad request. Please check your input and try again.'
    )
  }
}

const onSumbit = () => {
  refVForm.value?.validate().then(({ valid: isValid }) => {
    if (isValid) ChangePassword()
  })
}

const goBack = async () => {
  const confirmed = await Swal.ApproveCancel()
  if (!confirmed) return
  router.push({ path: '/apps/login' })
}
</script>

<template>
  <HeaderPublic />

  <div class="auth-wrapper d-flex">
    <VContainer>
      <VRow class="pt-10">
        <VCol cols="12" justify="center">
          <VCard class="auth-card login-card pa-2 pt-7 mb-5">
            <VCardText class="pt-2">
              <div class="text-center mb-4">
                <img src="@images/forgot.png" alt="Website logo" />
              </div>
              <h4 class="mb-5 text-center">ลืมรหัสผ่าน</h4>

              <!--
                <p class="mb-0">
                <span class="text-capitalize">{{ themeConfig.app.title }}</span>
                </p>
              -->
            </VCardText>
            <VCardText>
              <VForm ref="refVForm" @submit.prevent="onSumbit">
                <VRow justify="center" class="align-center">
                  <VCol cols="12" md="7" class="py-0 mb-4">
                    <label>
                      รหัสผ่าน:
                      <small class="text-error">*</small>
                    </label>
                    <VTextField
                      v-model="form.password"
                      density="compact"
                      :type="isPasswordVisible ? 'text' : 'password'"
                      :append-inner-icon="
                        isPasswordVisible ? 'mdi-eye-off-outline' : 'mdi-eye-outline'
                      "
                      :counter="20"
                      :maxlength="20"
                      :rules="[passwordValidator]"
                      @click:append-inner="isPasswordVisible = !isPasswordVisible"
                    />
                    <span v-if="!form.password" class="guidTooltip">
                      <small class="text-error">ดูคำแนะนำในการกำหนดรหัสผ่าน</small>
                      <TooltipFormDialog :title="tooltipTitle" :content="tooltipContent" />
                    </span>
                  </VCol>

                  <VCol cols="12" md="7" class="py-0">
                    <label>
                      ยืนยันรหัสผ่าน:
                      <small class="text-error">*</small>
                    </label>
                    <VTextField
                      v-model="form.confirmPassword"
                      density="compact"
                      :type="isPasswordVisible ? 'text' : 'password'"
                      :append-inner-icon="
                        isPasswordVisible ? 'mdi-eye-off-outline' : 'mdi-eye-outline'
                      "
                      :counter="20"
                      :maxlength="20"
                      :rules="[
                        requiredValidator,
                        () => confirmedValidator(form.confirmPassword, form.password)
                      ]"
                      @click:append-inner="isPasswordVisible = !isPasswordVisible"
                    />
                  </VCol>

                  <VCol cols="12" class="d-flex flex-wrap justify-center mt-3">
                    <div class="demo-space-x">
                      <VBtn color="blue-200" type="submit">ยืนยัน</VBtn>
                      <VBtn color="error-300" @click="goBack">ยกเลิก</VBtn>
                    </div>
                  </VCol>
                </VRow>
              </VForm>
            </VCardText>
          </VCard>
        </VCol>

        <VCol cols="12">
          <Footer />
        </VCol>
      </VRow>
    </VContainer>

    <VImg :src="bgLogin" role="presentation" class="d-none d-md-block auth-footer-mask" />
    <div class="auth-logo" />
    <GovWebsiteAccess />
  </div>
</template>

<style lang="css">
@import '@public/assets/css/style.css';
@import '@public/assets/css/skin/skin-2.css';
</style>

<style lang="css" scoped>
.v-container {
  position: relative;
  z-index: 1;
}

.auth-footer-mask {
  inset-block-end: 0% !important;
  /* เปลี่ยนค่าเมื่อหน้าจอเล็กลง */
}
.guidTooltip {
  margin-top: -20px;
  position: absolute;
}
</style>

<style lang="scss">
@use '@core/scss/template/pages/page-auth.scss';
</style>

<style lang="scss" scoped>
.layout-blank {
  .auth-wrapper {
    min-block-size: calc(var(--vh, 1vh) * 0);

    &::before {
      content: '';
      display: block;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 50%;
      background: linear-gradient(
        180deg,
        rgb(var(--v-theme-blue-100)) 0%,
        rgba(255, 255, 255, 0) 100%
      );
      /* Example background */
      z-index: 1;
      /* Make sure it's behind other content */
    }
  }

  .auth-card {
    z-index: 1 !important;
  }
}
</style>

<route lang="yaml">
meta:
  layout: blank
  action: read
  subject: Auth
  redirectIfLoggedIn: true
</route>
