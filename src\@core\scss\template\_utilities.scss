@use '@configured-variables' as variables;

.bg-card {
  background: rgb(var(--v-theme-surface)) !important;
}

.table-header-bg {
  th {
    background-color: rgb(var(--v-theme-grey-200));
  }
}

.per-page-select {
  margin-block: auto;

  .v-field__input {
    align-items: center;
    padding: 2px;
    font-size: 14px;
  }

  .v-field__append-inner {
    align-items: center;
    padding: 0;

    .v-icon {
      margin-inline-start: 0 !important;
    }
  }
}

.dragArea {
  list-style: none;
  padding: 0;
  min-height: 25px;
}
.children.dragArea {
  border: 2px solid rgb(var(--v-theme-grey-secondary));
  border-radius: 5px;
  padding: 10px;
}
li ul.dragArea {
  width: 98%;
  margin-left: 2%;
  font-weight: normal;
  margin-bottom: 5px;
}
/* Styling for list items */
.list-item {
  background-color: rgb(var(--v-theme-surface));
  border: 2px solid rgb(var(--v-theme-grey-secondary));
  padding: 10px;
  justify-content: space-between;
  align-items: center;
  border-radius: 10px;
  margin: 10px;
}

/* Styling for the content within list items */
.list-item-content {
  flex: 1;
}

/* Styling for the edit icon */
.edit-icon {
  margin-right: 10px;
  cursor: pointer;
}

/* Styling for the delete icon */
.delete-icon {
  cursor: pointer;
}

/* Styling for children within list items */
.list-item-child {
  background-color: rgb(var(--v-theme-surface));
  border: 2px solid rgb(var(--v-theme-grey-secondary));
  padding: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Styling for the content within child list items */
.list-item-child-content {
  flex: 1;
}
