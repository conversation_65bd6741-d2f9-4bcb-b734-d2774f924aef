<script setup>
const router = useRouter()
import DownloadForm from '@/@core/components/Public/DownloadForm.vue'

const imgLogo = ref('')

router.afterEach(() => {
  window.scrollTo(0, 0)
})
</script>

<template>
  <div>
    <HeaderPublic :logo="imgLogo" />

    <DownloadForm />

    <FooterPublic :logo="imgLogo" />
  </div>
</template>

<style lang="scss">
@use '@public/assets/css/style.css';
@use '@public/assets/css/skin/skin-2.css';
</style>

<route lang="yaml">
meta:
  layout: blank
  action: read
</route>
