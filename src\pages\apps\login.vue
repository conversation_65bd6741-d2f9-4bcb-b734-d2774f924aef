<script setup lang="ts">
import { VCol, VRow } from 'vuetify/lib/components/index.mjs'
import { useGenerateImageVariant } from '@/@core/composable/useGenerateImageVariant'
import Footer from '@/layouts/components/Footer.vue'
import { useAppAbility } from '@/plugins/casl/useAppAbility'
import { useAuth } from '@/store/useAuth'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import authV1LoginMaskDark from '@images/pages/auth-v1-login-mask-dark.png'
import authV1LoginMaskLight from '@images/pages/auth-v1-login-mask-light.png'
import bgLogin from '@images/pages/bgLogin.png'
import { emailValidator, requiredValidator } from '@validators'

const Swal = useSwal()
const callAxios = useAxios()
const callAuth = useAuth()
const AppAbility = useAppAbility()
const router = useRouter()
const refVForm = ref()

const rules = ref({
  required: requiredValidator,
  identifyemail: emailValidator
})

const isPasswordVisible = ref(false)

const auth = ref({
  // email: "<EMAIL>",
  // password: "P@ssw0rd",
})

const remember = ref(false)

const errors = ref({
  email: undefined,
  password: undefined
})

const LoadUserAbilities = () => {
  const userAbilities = JSON.stringify([{ action: 'manage', subject: 'all' }])

  localStorage.setItem('userAbilities', userAbilities)
}

const authV1ThemeLoginMask = useGenerateImageVariant(authV1LoginMaskLight, authV1LoginMaskDark)

const authLogin = async () => {
  try {
    const response = await callAuth.authenticationLogin(auth.value)

    if (response.status === 200) {
      const userAbilities = JSON.stringify([{ action: 'manage', subject: 'all' }])

      AppAbility.update(userAbilities)
      localStorage.setItem('userAbilities', userAbilities)
      localStorage.setItem('token', response.data.token)
      localStorage.setItem('username', response.data.fullName)
      localStorage.setItem('userId', response.data.userId)
      localStorage.setItem('roleName', response.data.role)
      sessionStorage.setItem('username', response.data.fullName)

      // localStorage.setItem("selectedTemplateColor", response.data.template);

      await router.push('/apps/home')
      window.location.reload() // รีเฟรชหน้าเว็บหลังจากเปลี่ยนเส้นทางเรียบร้อย
    } else if (response.status === 428) {
      if (auth.value.email) localStorage.setItem('rememberedEmail428', auth.value.email)

      // รอให้ผู้ใช้กด "ตกลง" ก่อน
      const confirmed = await Swal.ConditionWarningTextLogin(response.data.message)

      if (!confirmed) return // ถ้าผู้ใช้กด "ยกเลิก" หรือปิด Swal ให้ออกจากฟังก์ชัน

      if (response.data.message === 'อีเมลนี้ถูกระงับการใช้งานอยู่')
        await router.push({ path: '/apps/login' })
      else await router.push({ path: '/apps/registeragain' })
    }
  } catch (error) {
    console.error('เกิดข้อผิดพลาดขณะล็อกอิน:', error)
  }
}

const isRecaptchaVerified = ref(false)

// Callback เมื่อผู้ใช้ Verify สำเร็จ
const onVerify = (token: string) => {
  console.log('reCAPTCHA verified! Token:', token)
  isRecaptchaVerified.value = true // เปลี่ยนสถานะเมื่อ Verify สำเร็จ
}

const handleRememberMe = () => {
  if (remember.value) {
    // ถ้าติ๊กถูก ให้บันทึก email และ password
    localStorage.setItem('rememberedEmail', auth.value.email)
    localStorage.setItem('rememberedPassword', auth.value.password)
  } else {
    // ถ้าไม่ติ๊ก ให้ลบข้อมูล
    localStorage.removeItem('rememberedEmail')
    localStorage.removeItem('rememberedPassword')
  }
}

const onSumbit = () => {
  refVForm.value?.validate().then(({ valid: isValid }) => {
    if (isValid) {
      handleRememberMe() // จัดการการจดจำข้อมูล
      authLogin()
    }
  })
}

onMounted(() => {
  LoadUserAbilities()
  LoadUserAbilities()

  // โหลดข้อมูลอีเมลและรหัสผ่านที่เคยจำไว้ (ถ้ามี)
  const savedEmail = localStorage.getItem('rememberedEmail')
  const savedPassword = localStorage.getItem('rememberedPassword')

  if (savedEmail && savedPassword) {
    auth.value.email = savedEmail
    auth.value.password = savedPassword
    remember.value = true // ตั้งค่า checkbox เป็น true
  }
})
</script>

<template>
  <HeaderPublic />

  <div class="auth-wrapper d-flex">
    <VContainer>
      <VRow class="pt-10 justify-center">
        <VCol cols="12" sm="12" md="6" lg="5" xl="3" class="align-self-center">
          <VCard class="auth-card login-card pa-2 pt-7 rounded-shaped d-block">
            <VCardText class="pt-2">
              <h1 class="mb-5 text-center text-navy-100">เข้าสู่ระบบ</h1>
            </VCardText>
            <VCardText>
              <VForm ref="refVForm" @submit.prevent="onSumbit">
                <VRow>
                  <VCol cols="12">
                    <VTextField
                      v-model="auth.email"
                      label="ชื่อผู้ใช้งาน (อีเมล)"
                      :rules="[rules.identifyemail]"
                    />
                  </VCol>

                  <VCol cols="12">
                    <VTextField
                      v-model="auth.password"
                      label="รหัสผ่าน"
                      :type="isPasswordVisible ? 'text' : 'password'"
                      :error-messages="errors.password"
                      :append-inner-icon="
                        isPasswordVisible ? 'mdi-eye-off-outline' : 'mdi-eye-outline'
                      "
                      maxlength="20"
                      @click:append-inner="isPasswordVisible = !isPasswordVisible"
                    />

                    <!-- remember me checkbox -->
                    <div class="d-flex align-center justify-space-between flex-wrap mt-1 mb-4">
                      <VCheckbox v-model="remember" label="จดจำฉัน" @change="handleRememberMe" />

                      <RouterLink
                        :to="{ name: 'apps-forgotPassword' }"
                        class="px-0 py-0 text-decoration-underline change-color"
                      >
                        ลืมรหัสผ่าน?
                      </RouterLink>
                    </div>
                    <div class="text-center" role="presentation">
                      <Recaptcha @verify="onVerify" />
                    </div>
                    <!-- login button -->
                    <VBtn
                      color="navy-100 mt-4"
                      block
                      type="submit"
                      :disabled="!isRecaptchaVerified"
                    >
                      เข้าสู่ระบบ
                    </VBtn>
                    <div class="d-flex align-center text-navy-100 flex-wrap mt-2">
                      ยังไม่มีชื่อผู้ใช้งานใช่ไหม ? สามารถลงทะเบียนได้ คลิกที่นี่

                      <RouterLink
                        :to="{ name: 'apps-Register' }"
                        class="ms-2 px-0 py-0 text-decoration-underline change-color"
                      >
                        คลิกที่นี่
                      </RouterLink>
                    </div>
                  </VCol>
                </VRow>
              </VForm>
            </VCardText>
          </VCard>
        </VCol>

        <VCol cols="12">
          <Footer />
        </VCol>
      </VRow>
    </VContainer>

    <VImg :src="bgLogin" role="presentation" class="d-none d-md-block auth-footer-mask" />
    <div class="auth-logo" />
    <GovWebsiteAccess />
  </div>
</template>

<style lang="css">
@import '@public/assets/css/style.css';
@import '@public/assets/css/skin/skin-2.css';
</style>

<style lang="css" scoped>
.v-container {
  position: relative;
  z-index: 1;
}

.login-card {
  background: inherit;
}

.login-card.v-card--variant-elevated {
  box-shadow: inherit;
}

.auth-footer-mask {
  inset-block-end: 0% !important;

  /* เปลี่ยนค่าเมื่อหน้าจอเล็กลง */
}

.v-btn--variant-plain {
  block-size: inherit !important;
}

.change-color {
  color: #1640ac;
}

.v-application-theme2 .text-navy-100,
.v-application-theme2 .change-color,
.v-application-theme3 .text-navy-100,
.v-application-theme3 .change-color {
  color: #333 !important;
}

.v-application-theme2 .bg-navy-100 {
  background: #ffb049 !important;
  color: #333 !important;
}

.v-application-theme3 .bg-navy-100 {
  background: #d993ab !important;
  color: #333 !important;
}

.v-application-by .bg-navy-100 {
  background: #fecb00 !important;
  color: #000 !important;
}

.v-application-by img {
  filter: grayscale(100%) !important;
}
</style>

<style lang="scss">
@use '@core/scss/template/pages/page-auth.scss';
</style>

<style lang="scss" scoped>
.layout-blank {
  .auth-wrapper {
    min-block-size: calc(var(--vh, 1vh) * 0);

    &::before {
      position: absolute;

      /* Example background */
      z-index: 1;
      display: block;
      background: linear-gradient(
        180deg,
        rgb(var(--v-theme-blue-100)) 0%,
        rgba(255, 255, 255, 0%) 100%
      );
      block-size: 50%;
      content: '';
      inline-size: 100%;
      inset-block-start: 0;
      inset-inline-start: 0;

      /* Make sure it's behind other content */
    }
  }

  .auth-card {
    z-index: 1 !important;
  }
}
</style>

<route lang="yaml">
meta:
  layout: blank
  action: read
  subject: Auth
  redirectIfLoggedIn: true
</route>
