<script setup>
import { useAxios } from '@/store/useAxios'
import { requiredValidator } from '@validators'

const router = useRouter()
const callAxios = useAxios()
const refVForm = ref()

const form = ref({
  evaluationFormId: null,
  emailString: '',
  email: [],
  subject: '',
  body: ''
})

const tag = ref('')
const tags = ref([])

const itemsKPBFormDDL = ref([])

const breadcrumbItems = [
  {
    title: 'แบบประเมินความพึงพอใจ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'KBP Survey',
    disabled: false,
    to: ''
  },
  {
    title: 'แบบประเมินความพึงพอใจของผู้มีส่วนได้ส่วนเสีย',
    disabled: false,
    to: ''
  },
  {
    title: 'ส่งแบบประเมินความพึงพอใจของผู้มีส่วนได้ส่วนเสีย',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const onFormSubmit = () => {
  refVForm.value?.validate().then(({ valid: isValid, errors }) => {
    if (isValid) SendEvaluationKPBSEmail()
  })
}

function goBack() {
  router.go(-1)
}

const onFormReset = () => {
  form.value = {
    evaluationFormId: null,
    emailString: '',
    email: [],
    subject: '',
    body: ''
  }
  goBack()
}

const SendEvaluationKPBSEmail = () => {
  form.value.email = JSON.parse(JSON.stringify(form.value.emailString.split(',')))
  callAxios.RequestPost('/EvaluationForm/SendEvaluationKPBSEmail', form.value).then(response => {
    if (response.status == 200) onFormReset()
  })
}

const GetKPBFormDDL = () => {
  const endpoint = '/EvaluationForm/GetKPBFormDDL'

  callAxios.RequestGet(endpoint).then(response => {
    if (response.status == 200) itemsKPBFormDDL.value = response.data.response
  })
}

const GetKBPMailTemplate = value => {
  const endpoint = `/EvaluationForm/GetKBPMailTemplate?EvaluationFormId=${value}`

  callAxios.RequestGet(endpoint).then(response => {
    if (response.status == 200) {
      form.value.subject = response.data.response.subject
      form.value.body = response.data.response.body
    }
  })
}

onMounted(() => {
  GetKPBFormDDL()
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <VCard class="mb-5">
      <VCardText>
        <VForm ref="refVForm" @submit.prevent="onFormSubmit">
          <VRow>
            <VCol cols="12">
              <VRow class="mb-2" align="center">
                <VCol cols="12" md="3" class="text-lg-end">
                  <label>
                    เลือกแบบประเมิน :
                    <small class="text-error">*</small>
                  </label>
                </VCol>
                <VCol cols="12" md="9">
                  <VAutocomplete
                    v-model="form.evaluationFormId"
                    :items="itemsKPBFormDDL"
                    item-title="evaluationFormName"
                    item-value="evaluationFormId"
                    placeholder="เลือกแบบประเมิน"
                    :rules="[requiredValidator]"
                    @update:modelValue="event => GetKBPMailTemplate(event)"
                  />
                </VCol>
              </VRow>
            </VCol>
            <VCol cols="12">
              <VRow class="mb-2" align="center">
                <VCol cols="12" md="3" class="text-lg-end">
                  <label>
                    เรื่อง :
                    <small class="text-error">*</small>
                  </label>
                </VCol>
                <VCol cols="12" md="9">
                  <VTextField
                    v-model="form.subject"
                    :rules="[requiredValidator]"
                    placeholder="กรอกชื่อเรื่อง"
                  />
                </VCol>
              </VRow>
            </VCol>
            <VCol cols="12">
              <VRow class="mb-2" align="center">
                <VCol cols="12" md="3" class="text-lg-end">
                  <label>
                    ถึง :
                    <small class="text-error">*</small>
                  </label>
                </VCol>
                <VCol cols="12" md="9">
                  <VTextField
                    v-model="form.emailString"
                    :rules="[requiredValidator]"
                    placeholder="ส่งถึง"
                  />
                </VCol>
              </VRow>
            </VCol>
            <VCol cols="12">
              <VRow class="mb-2" align="center">
                <VCol cols="12" md="3" class="text-lg-end">
                  <label>รายละเอียด :</label>
                </VCol>
                <VCol cols="12" md="9">
                  <VTextarea v-model="form.body" placeholder="กรอกรายละเอียด" />
                </VCol>
              </VRow>
            </VCol>
          </VRow>
          <div class="d-flex flex-wrap justify-end mt-5">
            <div class="demo-space-x">
              <VBtn type="submit" color="blue-600">ส่ง</VBtn>
              <VBtn color="error-300" @click="onFormReset">ยกเลิก</VBtn>
            </div>
          </div>
        </VForm>
      </VCardText>
    </VCard>
  </div>
</template>
