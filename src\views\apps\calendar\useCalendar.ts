import type { CalendarApi, CalendarOptions, EventApi, EventSourceFunc } from '@fullcalendar/core'
import thLocale from '@fullcalendar/core/locales/th'
import dayGridPlugin from '@fullcalendar/daygrid'
import interactionPlugin from '@fullcalendar/interaction'
import listPlugin from '@fullcalendar/list'
import timeGridPlugin from '@fullcalendar/timegrid'
import type { Ref } from 'vue'
import type { Event, NewEvent } from './types'
import { useThemeConfig } from '@core/composable/useThemeConfig'
import { useCalendarStore } from '@/views/apps/calendar/useCalendarStore'

export const blankEvent = {
  title: '',
  start: '',
  end: '',
  allDay: false,
  url: '',
  extendedProps: {
    calendar: undefined,
    guests: [],
    location: '',
    description: '',
    attendees: []
  }
}

interface MyEvent {
  id: string
  title: string
  start: string
  end: string
}

export const useCalendar = (
  event: Ref<Event | NewEvent>,
  isDialogOpen: Ref<boolean>,
  isLeftSidebarOpen: Ref<boolean>
) => {
  // 👉 themeConfig
  const { isAppRtl } = useThemeConfig()

  // 👉 Store
  const store = useCalendarStore()

  // 👉 Calendar template ref
  const refCalendar = ref()

  // 👉 Calendar colors
  const calendarsColor = {
    Work: 'primary',
    Holiday: 'success'
  }

  // ℹ️ Extract event data from event API
  const extractEventDataFromEventApi = (eventApi: EventApi) => {
    // @ts-expect-error EventApi has extendProps type Dictionary (Record<string, any>) and we have fully typed extended props => Type conflict
    const {
      id,
      title,
      start,
      end,
      url,
      extendedProps: { calendar, guests, location, description, attendees },
      allDay
    }: Event = eventApi

    return {
      id,
      title,
      start,
      end,
      url,
      extendedProps: {
        calendar,
        guests,
        location,
        description,
        attendees
      },
      allDay
    }
  }

  // 👉 Fetch events
  const fetchEvents: EventSourceFunc = async (info, successCallback) => {
    try {
      const events = await store.fetchEvents()

      successCallback(
        events.map((e: Event) => ({
          ...e,
          start: new Date(e.start),
          end: new Date(e.end)
        }))
      )
    } catch (error) {
      console.error('Error fetching events:', error)
    }
  }

  // 👉 Calendar API
  const calendarApi = ref<null | CalendarApi>(null)

  // 👉 refetch events
  const refetchEvents = () => {
    if (calendarApi.value) calendarApi.value.refetchEvents()
  }

  watch(() => store.selectedCalendars, refetchEvents)

  // 👉 Calendar options
  const calendarOptions = {
    plugins: [dayGridPlugin, interactionPlugin, timeGridPlugin, listPlugin],
    initialView: 'dayGridMonth',
    firstDay: 0,
    headerToolbar: {
      start: 'drawerToggler prev,next title',
      center: '',
      end: 'dayGridMonth,timeGridWeek,timeGridDay,listMonth'
    },
    events: fetchEvents,
    locale: thLocale,
    forceEventDuration: true,
    editable: true,
    eventResizableFromStart: true,
    dragScroll: true,
    dayMaxEvents: 2,
    navLinks: true,
    displayEventTime: false,

    eventContent({ event }) {
      return { html: `<div>${event.title}</div>` }
    },

    eventClassNames({ event: calendarEvent }) {
      const colorName =
        calendarsColor[calendarEvent._def.extendedProps.calendar as keyof typeof calendarsColor]

      return [`bg-light-${colorName} text-${colorName}`]
    },

    eventClick({ event: clickedEvent, jsEvent }) {
      if (!clickedEvent) return

      event.value = extractEventDataFromEventApi(clickedEvent)
      isDialogOpen.value = true

      jsEvent.preventDefault()
    },

    // dateClick(info) {
    //   event.value = { ...event.value, start: String(new Date(info.date)) };
    //   isDialogOpen.value = true;
    // },

    customButtons: {
      drawerToggler: {
        text: '☰',
        click() {
          isLeftSidebarOpen.value = true
        }
      }
    }
  } as CalendarOptions

  // 👉 onMounted
  onMounted(() => {
    if (refCalendar.value) calendarApi.value = refCalendar.value.getApi()
  })

  watch(
    isAppRtl,
    val => {
      calendarApi.value?.setOption('direction', val ? 'rtl' : 'ltr')
    },
    { immediate: true }
  )

  return {
    refCalendar,
    calendarOptions,
    refetchEvents,
    fetchEvents
  }
}
