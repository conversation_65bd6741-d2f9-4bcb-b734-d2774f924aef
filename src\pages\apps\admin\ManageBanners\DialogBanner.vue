<script lang="ts" setup>
import { computed, reactive, ref } from 'vue'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import type { FormBanners } from '@interfaces/FormInterface'
import type { IGetSystemBannersByIdRes } from '@interfaces/UserManagementInterface'
import { booleanValidator, fileSize5MB, fileType, requiredValidator } from '@validators'

// Props and Emits
const props = defineProps({
  modelValue: Boolean,
  isAdd: Boolean,
  isEdit: Boolean,
  isView: Boolean,
  viewId: String,
  editId: String
})

const emit = defineEmits(['update:modelValue', 'update'])

const callAxios = useAxios()
const Swal = useSwal()
const refVForm: Ref<any> = ref(null)
const viewId = computed(() => props.viewId)
const editId = computed(() => props.editId)

const accountDataLocal = ref({
  avatarImg: ''
})
const form = reactive<FormBanners>({
  imageFiles: null as File | null,
  banner_id: null,
  banner_name: '',
  file_name: '',
  url: '',
  is_active: false,
  sorting: 0,
  create_date: new Date(),
  create_by: '',
  modilfy_date: null,
  modify_by: null,
  period_typeid: 1,
  start_date: null,
  end_date: null
})

const radioList = [
  {
    label: 'ทุกวัน/ทุกเวลา',
    value: 1
  },
  {
    label: 'กำหนดวันที่-เวลา',
    value: 2
  }
]

const createFormData = (sanitizedBannerName: string, sanitizedUrl?: string) => {
  const formData = new FormData()

  if (form.banner_id) formData.append('id', form.banner_id.toString())

  if (form.imageFiles instanceof File) formData.append('Files', form.imageFiles)

  // ใช้ sanitizedBannerName จากพารามิเตอร์
  if (sanitizedBannerName) formData.append('name', sanitizedBannerName)

  // Append isActive as string
  formData.append('isActive', String(form.is_active))

  // เพิ่ม sanitizedUrl หากมี
  if (sanitizedUrl) formData.append('url', sanitizedUrl)

  return formData
}

const formDefault = structuredClone(toRaw(form))

const resetForm = () => {
  Object.assign(form, structuredClone(formDefault))
}

const closeDialog = async () => {
  if (props.isView) {
    resetForm()
    isDialogVisible.value = false

    return
  }
  const response = await Swal.ApproveCancel()
  if (response) {
    resetForm()
    isDialogVisible.value = false
  }
}

const onFormSubmit = async () => {
  try {
    if (!refVForm.value) return
    const { valid } = await refVForm.value.validate()

    if (valid) {
      if (props.isAdd) await AddForm()
      else if (props.isEdit) await EditForm()
      else throw new Error('Invalid state')
    }
  } catch (error) {
    Swal.AddConditionFailText('ไม่สามารถดำเนินการได้: ไม่มีสถานะที่ถูกต้อง')
  }
}

const checkDuplicateData = async (error: any) => {
  if (error?.response?.status === 409) {
    const apiMessage = error.response.data?.message || 'ข้อมูลมีอยู่แล้วในระบบ'

    Swal.AddConditionFailText(apiMessage)
  } else {
    // กรณีอื่น ๆ
    Swal.AddConditionFailText('กรุณากรอกข้อมูลให้ถูกต้อง')
  }
}

const sanitizeContent = (content: string): string => {
  return content
    .replace(/\s+/g, ' ') // แทนที่ช่องว่างหลายช่องด้วยช่องว่างเดียว
    .trim() // ลบช่องว่างส่วนต้นและส่วนท้าย
}

const notifyUpdate = () => {
  localStorage.setItem('banner-updated', Date.now().toString()) // บันทึก timestamp ลง Local Storage
}

const AddForm = async () => {
  const confirmed = await Swal.ApproveConfirm()
  if (!confirmed) return

  try {
    const sanitizedBannerName = sanitizeContent(form.banner_name)
    const sanitizedUrl = sanitizeContent(form.url)

    const formData = createFormData(sanitizedBannerName, sanitizedUrl)

    const response = await callAxios.RequestUpload('/UserManagement/AddSystemBanner', formData)

    if (response.status === 200) {
      resetForm()
      isDialogVisible.value = false
      emit('update')
      notifyUpdate()
    } else {
      Swal.AddFail()
    }
  } catch (error: any) {
    await checkDuplicateData(error)
  }
}

const EditForm = async () => {
  const confirmed = await Swal.ApproveConfirm()
  if (!confirmed) return

  // Sanitize ข้อมูล
  const sanitizedBannerName = sanitizeContent(form.banner_name)
  const sanitizedUrl = sanitizeContent(form.url)

  const formData = createFormData(sanitizedBannerName, sanitizedUrl)

  try {
    const response = await callAxios.RequestPutUpload(
      `/system-banner/update/=${editId.value}`,
      formData
    )

    if (response.status === 200) {
      resetForm()
      isDialogVisible.value = false
      emit('update')
      notifyUpdate()
    } else {
      Swal.EditConditionFail()
    }
  } catch (error) {}
}

const ViewForm = async () => {
  try {
    const response = await callAxios.RequestGetById('/system-banner/get', `${viewId.value}`)

    if (response.status === 200) {
      resetForm()

      const bannerData: IGetSystemBannersByIdRes = response.data
      form.banner_id = bannerData.banner_id
      form.is_active = bannerData.is_active
      form.file_name = bannerData.file_name
      // form.pathName = bannerData.pathName
      form.banner_name = bannerData.banner_name
      form.url = bannerData.url
    }
  } catch (error) {
    console.error('View form error:', error)
    Swal.ViewFail()
  }
}

const handleFileSelected = (val: File) => {
  form.imageFiles = val
  accountDataLocal.value.avatarImg = URL.createObjectURL(val)
}

const isDialogVisible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value)
})

watch(
  () => isDialogVisible.value,
  async newVal => {
    if (newVal && props.isView) await ViewForm()
    else if (newVal && props.isEdit) await ViewForm()
  }
)

const setTitle = computed(() => {
  if (props.isView) return 'ดูข้อมูล'
  if (props.isEdit) return 'แก้ไขแบนเนอร์'

  return 'เพิ่มแบนเนอร์'
})

const listStatus = ref([
  { id: true, name: 'ใช้งาน' },
  { id: false, name: 'ไม่ใช้งาน' }
])


</script>

<template>
  <VDialog v-model="isDialogVisible" persistent class="v-dialog-lg" no-click-animation scrollable>
    <VCard :title="setTitle">
      <DialogCloseBtn variant="text" size="small" @click="closeDialog" />
      <VCardText>
        <VForm ref="refVForm" @submit.prevent="onFormSubmit">
          <VRow class="mb-4 mx-2 mt-3 justify-center">
            <VCol cols="6" md="6" sm="12">
              <label>
                ชื่อแบนเนอร์ :
                <small class="text-error">*</small>
              </label>
              <VTextField
                v-model="form.banner_name"
                density="comfortable"
                placeholder="ระบุชื่อแบนเนอร์"
                class="no-select"
                :rules="[requiredValidator]"
                :disabled="props.isView"
                :bg-color="isView ? 'grey-secondary' : undefined"
                :counter="100"
                :maxlength="100"
              />
            </VCol>
            <VCol cols="6" md="6" sm="12">
              <label>
                ช่วงเวลาเผยแพร่ :
                <small class="text-error">*</small>
              </label>
              <VRadioGroup
                v-model="form.period_typeid"
                :rules="[requiredValidator]"
                :inline="true"
                :readonly="props.isView"
                :bg-color="props.isView ? 'grey-secondary' : ''"
              >
                <VRadio
                  v-for="item in radioList"
                  :label="item.label"
                  :value="item.value"
                  :disabled="props.isView"
                  :bg-color="props.isView ? 'grey-secondary' : ''"
                />
              </VRadioGroup>
            </VCol>
             <VCol cols="6" md="6" sm="12">
              <label>
                แบนเนอร์ :
                <small class="text-error">*</small>
              </label>

                 <UploadImagePreviewOut
                        class="no-select mt-3"
                        :detail="
                          !props.isView
                            ? 'แนบไฟล์ขนาดไม่เกิน 5MB ไม่เกิน 1920px X 568px รองรับไฟล์ jpeg, jpg, gif, png (ไฟล์เดียว)'
                            : ''
                        "
                        :disabled="props.isView"
                        :edit-dialog="props.isEdit || props.isView"
                        :url-img="form.pathName"
                        :rules="[
                          (value: any) => fileSize5MB(value, props.isEdit),
                          fileType(['png', 'jpg', 'gif']),
                        ]"
                        @file-selected="handleFileSelected"
                      />
            </VCol>
   
            <VCol cols="6" md="6" sm="12">
              <label>
                วันที่ - เวลา (เริ่มต้น-สิ้นสุด) :
                <small class="text-error">*</small>
              </label>

              <DateTimePicker
                v-model="form.dateRange"
                format="DD/MM/YYYY HH:mm"
             datetime-range
                class="my-2"
                :rules="[requiredValidator]"
              />
            </VCol>
                      <VCol v-if="accountDataLocal.avatarImg" cols="12" class="pb-0">
        <VImg :height="200" :src="accountDataLocal.avatarImg" />
      </VCol>

            <VCol cols="12" md="12">
              <VRow class="mb-2" align="center">
                <VCol cols="12" md="12" class="py-0 mb-2">
                  <label>URL :</label>
                </VCol>
                <VCol cols="12" md="12" class="py-0">
                  <VTextField
                    v-model="form.url"
                    density="comfortable"
                    placeholder="ระบุ URL ตัวอย่าง www.google.com (ไม่จำเป็น)"
                    class="no-select dark-placeholder"
                    :disabled="props.isView"
                    :bg-color="isView ? 'grey-secondary' : undefined"
                    :counter="500"
                    :maxlength="500"
                  />
                </VCol>
              </VRow>
            </VCol>

            <VCol cols="12" md="12">
              <VRow class="mb-2" align="center">
                <VCol cols="12" md="12" class="py-0 mb-2">
                  <label>
                    สถานะ :
                    <small class="text-error">*</small>
                  </label>
                </VCol>
                <VCol cols="12" md="12" class="py-0">
                  <VAutocomplete
                    v-model="form.is_active"
                    density="comfortable"
                    placeholder="ระบุสถานะ"
                    item-title="name"
                    item-value="id"
                    class="no-select"
                    :rules="[booleanValidator]"
                    :items="listStatus"
                    :disabled="props.isView"
                    :bg-color="isView ? 'grey-secondary' : undefined"
                  />
                </VCol>
              </VRow>
            </VCol>
          </VRow>

          <div class="d-flex flex-wrap justify-center mt-5">
            <div class="btn-group">
              <template v-if="!props.isView">
                <VBtn
                  color="blue-600"
                  rounded="xl"
                  prepend-icon="mdi-content-save"
                  @click="onFormSubmit"
                >
                  บันทึก
                </VBtn>
                <VBtn
                  color="error-300"
                  rounded="xl"
                  prepend-icon="mdi-close-circle"
                  @click="closeDialog"
                >
                  ยกเลิก
                </VBtn>
              </template>

              <template v-else>
                <VBtn color="grey-800" rounded="xl" prepend-icon="mdi-close" @click="closeDialog">
                  ปิด
                </VBtn>
              </template>
            </div>
          </div>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style scoped>
.no-select {
  user-select: none;
}

.btn-group {
  display: flex;
  justify-content: center;
  gap: 16px;
}

::v-deep(.v-theme--dark .no-select .v-field--center-affix .v-label.v-field-label) {
  color: inherit !important;
}

::v-deep(.v-theme--dark .dark-placeholder input:disabled::placeholder) {
  color: inherit !important;
}

@media (max-width: 320px) {
  .btn-group {
    display: flex;
    justify-content: center;
    gap: 16px;
  }
}
</style>
