<script setup lang="ts">
import pictureIcon from '@/assets/images/mapmaker.png'
import L, { <PERSON><PERSON>, Mark<PERSON> } from 'leaflet'
import 'leaflet/dist/leaflet.css'
import { onBeforeUnmount, onMounted, ref, watch } from 'vue'

const map = ref<L.Map | null>(null)
const marker = ref<Marker | null>(null)

// Props for latitude, longitude, and default zoom
const props = defineProps({
  latitude: {
    type: Number,
    default: 13.736717
  },
  longitude: {
    type: Number,
    default: 100.523186
  },
  zoom: {
    type: Number,
    default: 10
  },
  height: {
    type: String,
    default: '600px'
  }
})

const center = ref({
  lat: props.latitude || 13.736717,
  lng: props.longitude || 100.523186
})

const initializeMap = () => {
  map.value = L.map('mapContainer', {
    center: [center.value.lat, center.value.lng],
    zoom: props.zoom
  })

  L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution:
      '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
  }).addTo(map.value)

  const customIcon = new Icon({
    iconUrl: pictureIcon,
    iconSize: [38, 38],
    iconAnchor: [19, 38]
  })

  marker.value = L.marker([center.value.lat, center.value.lng], {
    icon: customIcon,
    title: 'Custom Marker' // Add accessible name
  }).addTo(map.value)
}

watch(
  () => [props.latitude, props.longitude],
  ([newLat, newLng]) => {
    if (map.value) {
      map.value.setView([newLat, newLng], props.zoom)
    }
    if (marker.value) {
      marker.value.setLatLng([newLat, newLng])
    }
  }
)

onMounted(() => {
  initializeMap()
})

onBeforeUnmount(() => {
  if (map.value) {
    map.value.remove()
  }
})
</script>

<template>
  <div>
    <div
      id="mapContainer"
      :style="{ width: '100%', height }"
      role="application"
      aria-label="Map container"
    ></div>
  </div>
</template>

<style scoped>
#mapContainer {
  width: 100%;
}
</style>
