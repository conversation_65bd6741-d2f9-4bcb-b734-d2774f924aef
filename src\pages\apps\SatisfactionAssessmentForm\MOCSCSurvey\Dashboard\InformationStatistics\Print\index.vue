<script lang="ts" setup>
import { useAxios } from '@/store/useAxios'
import LogoMOC from '@images/logos/Logo_MOC.png'
import { formatDateTH } from '@utils'

const callAxios = useAxios()
const baseUrlImg = localStorage.baseURL
const sumTable1 = ref(0)
const sumTable2 = ref(0)
const sumTable3 = ref(0)
const sumTable4 = ref(0)
const sumTable5 = ref(0)
const province = ref(route.query.provice)
const isReset = ref(false)

const breadcrumbItems = [
  {
    title: 'MOC Survey',
    disabled: false
  },
  {
    title: 'ภาพรวมแบบสำรวจความต้องการของผู้รับบริการ',
    disabled: false
  },
  {
    title: 'รายงานสรุปตัวเลขสถิติข้อมูลทั่วไป',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const columns1 = ref([
  {
    field: 'name',
    header: 'เพศ',
    sortable: false
  },
  {
    field: 'number',
    header: 'จำนวนผู้ตอบ (คน)',
    sortable: false,
    style: { textAlign: 'center' }
  }
])

const columns2 = ref([
  {
    field: 'name',
    header: 'อายุ',
    sortable: false
  },
  {
    field: 'number',
    header: 'จำนวนผู้ตอบ (คน)',
    sortable: false,
    style: { textAlign: 'center' }
  }
])

const columns3 = ref([
  {
    field: 'name',
    header: 'ระดับการศึกษา',
    sortable: false
  },
  {
    field: 'number',
    header: 'จำนวนผู้ตอบ (คน)',
    sortable: false,
    style: { textAlign: 'center' }
  }
])

const columns4 = ref([
  {
    field: 'name',
    header: 'สถานภาพการทำงาน',
    sortable: false
  },
  {
    field: 'number',
    header: 'จำนวนผู้ตอบ (คน)',
    sortable: false,
    style: { textAlign: 'center' }
  }
])

const columns5 = ref([
  {
    field: 'name',
    header: 'งานบริการที่ขอรับบริการ',
    sortable: false
  },
  {
    field: 'number',
    header: 'จำนวนผู้ตอบ (คน)',
    sortable: false,
    style: { textAlign: 'center' }
  }
])

const dataDashboard = ref({})

const filter = ref({
  searchProvince: 0
})

const filterRaw = ref({
  searchProvince: 0
})

const GetList = (id = 0) => {
  callAxios
    .RequestGet(`/ReportGECC/GetMOCSurveyReportGeneral?OrgStructureId=${id}`)
    .then(response => {
      if (response.status == 200) {
        dataDashboard.value = response.data.response
        sumTable1.value = dataDashboard.value.donutChartI.summary
        sumTable2.value = dataDashboard.value.donutChartII.summary
        sumTable3.value = dataDashboard.value.donutChartIII.summary
        sumTable4.value = dataDashboard.value.donutChartIV.summary
        sumTable5.value = dataDashboard.value.donutChartV.summary
      }
    })
}

GetList()

const footer1 = ref([
  { footer: 'รวมทั้งหมด', style: { textAlign: 'right' } },
  { footer: sumTable1, style: { textAlign: 'center' } }
])

const footer2 = ref([
  { footer: 'รวมทั้งหมด', style: { textAlign: 'right' } },
  { footer: sumTable2, style: { textAlign: 'center' } }
])

const footer3 = ref([
  { footer: 'รวมทั้งหมด', style: { textAlign: 'right' } },
  { footer: sumTable3, style: { textAlign: 'center' } }
])

const footer4 = ref([
  { footer: 'รวมทั้งหมด', style: { textAlign: 'right' } },
  { footer: sumTable4, style: { textAlign: 'center' } }
])

const footer5 = ref([
  { footer: 'รวมทั้งหมด', style: { textAlign: 'right' } },
  { footer: sumTable5, style: { textAlign: 'center' } }
])

const listFilter = ref([
  {
    name: 'searchDate',
    type: 'dateRange',
    label: 'ช่วงวันที่'
  },
  {
    name: 'searchProvince',
    type: 'select',
    label: 'วัตถุประสงค์การแจ้งเรื่อง',
    title: 'orgStructureName',
    value: 'orgStructureId',
    items: []
  }
])

const GetProvinces = () => {
  callAxios.RequestGet('/ReportGECC/GetProvinces').then(response => {
    if (response.status == 200) {
      listFilter.value[1].items = response.data.response
      listFilter.value[1].items.unshift({ orgStructureId: 0, orgStructureName: 'ทั้งหมด' })
      console.log(listFilter.value)
    }
  })
}

GetProvinces()

const print = () => {
  window.print()
}
</script>

<template>
  <section>
    <VCardText>
      <VRow>
        <VCol cols="12">
          <div class="d-flex flex-wrap gap-4 justify-end">
            <VBtn class="hide-on-print" prepend-icon="mdi-printer" color="primary" @click="print">
              พิมพ์
            </VBtn>
          </div>
        </VCol>
      </VRow>
      <VRow>
        <VCol cols="12">
          <VCard class="mb-2">
            <VCardText>
              <div class="text-center my-2">
                <VImg height="100" :src="LogoMOC" />
              </div>
              <div class="text-center">
                <div class="d-flex align-center justify-center">
                  <h1 class="text-body-1">รายงานสรุปตัวเลขสถิติข้อมูลทั่วไป</h1>
                </div>
                <h3 v-if="listFilter[1].items.length > 0" class="text-body-1">
                  หน่วยงาน :
                  {{ listFilter[1].items.find(x => x.orgStructureId == province).orgStructureName }}
                </h3>
                <h3 v-if="queryData" class="text-body-1">
                  วันที่ {{ formatDateTH(queryData[0]) }} ถึง {{ formatDateTH(queryData[1]) }}
                </h3>
              </div>
            </VCardText>
          </VCard>
          <VCard
            v-if="dataDashboard.donutChartI && Object.keys(dataDashboard.donutChartI).length > 0"
            class="mb-5"
          >
            <VCardText>
              <VRow>
                <VCol cols="12" md="12" sm="12">
                  <VAlert color="info">
                    <h3 class="text-white">
                      {{ dataDashboard.donutChartI.header }}
                    </h3>
                  </VAlert>
                </VCol>
              </VRow>
              <VRow>
                <VCol cols="12" md="6" sm="12">
                  <PieChartsDonut :datas-bar-chart="dataDashboard.donutChartI.donut" />
                </VCol>
                <VCol cols="12" md="6" sm="12">
                  <AppDataTable
                    v-model="dataDashboard.donutChartI.datasTable"
                    :columns="columns1"
                    :header-no="false"
                    :paginator="false"
                    :footer="footer1"
                  />
                  <!-- <div class="mt-3"><p>หมายเหตุ* ค่าเฉลี่ยจะสามารถคำนวณได้เฉพาะ “ค่าตัวแปรในตัวเลือก” ที่เป็นตัวเลขเท่านั้น</p></div> -->
                </VCol>
              </VRow>
            </VCardText>
          </VCard>
          <VCard
            v-if="dataDashboard.donutChartII && Object.keys(dataDashboard.donutChartII).length > 0"
            class="mb-5"
          >
            <VCardText>
              <VRow>
                <VCol cols="12" md="12" sm="12">
                  <VAlert color="info">
                    <h3 class="text-white">
                      {{ dataDashboard.donutChartII.header }}
                    </h3>
                  </VAlert>
                </VCol>
              </VRow>
              <VRow>
                <VCol cols="12" md="6" sm="12">
                  <PieChartsDonut :datas-bar-chart="dataDashboard.donutChartII.donut" />
                </VCol>
                <VCol cols="12" md="6" sm="12">
                  <AppDataTable
                    v-model="dataDashboard.donutChartII.datasTable"
                    :columns="columns2"
                    :header-no="false"
                    :paginator="false"
                    :footer="footer2"
                  />
                  <!-- <div class="mt-3"><p>หมายเหตุ* ค่าเฉลี่ยจะสามารถคำนวณได้เฉพาะ “ค่าตัวแปรในตัวเลือก” ที่เป็นตัวเลขเท่านั้น</p></div> -->
                </VCol>
              </VRow>
            </VCardText>
          </VCard>

          <VCard
            v-if="
              dataDashboard.donutChartIII && Object.keys(dataDashboard.donutChartIII).length > 0
            "
            class="mb-5"
          >
            <VCardText>
              <VRow>
                <VCol cols="12" md="12" sm="12">
                  <VAlert color="info">
                    <h3 class="text-white">
                      {{ dataDashboard.donutChartIII.header }}
                    </h3>
                  </VAlert>
                </VCol>
              </VRow>
              <VRow>
                <VCol cols="12" md="6" sm="12">
                  <PieChartsDonut :datas-bar-chart="dataDashboard.donutChartIII.donut" />
                </VCol>
                <VCol cols="12" md="6" sm="12">
                  <AppDataTable
                    v-model="dataDashboard.donutChartIII.datasTable"
                    :columns="columns3"
                    :header-no="false"
                    :paginator="false"
                    :footer="footer3"
                  />
                  <!-- <div class="mt-3"><p>หมายเหตุ* ค่าเฉลี่ยจะสามารถคำนวณได้เฉพาะ “ค่าตัวแปรในตัวเลือก” ที่เป็นตัวเลขเท่านั้น</p></div> -->
                </VCol>
              </VRow>
            </VCardText>
          </VCard>

          <VCard
            v-if="dataDashboard.donutChartIV && Object.keys(dataDashboard.donutChartIV).length > 0"
          >
            <VCardText>
              <VRow>
                <VCol cols="12" md="12" sm="12">
                  <VAlert color="info">
                    <h3 class="text-white">
                      {{ dataDashboard.donutChartIV.header }}
                    </h3>
                  </VAlert>
                </VCol>
              </VRow>
              <VRow>
                <VCol cols="12" md="6" sm="12">
                  <PieChartsDonut :datas-bar-chart="dataDashboard.donutChartIV.donut" />
                </VCol>
                <VCol cols="12" md="6" sm="12">
                  <AppDataTable
                    v-model="dataDashboard.donutChartIV.datasTable"
                    :columns="columns4"
                    :header-no="false"
                    :paginator="false"
                    :footer="footer4"
                  />
                  <!-- <div class="mt-3"><p>หมายเหตุ* ค่าเฉลี่ยจะสามารถคำนวณได้เฉพาะ “ค่าตัวแปรในตัวเลือก” ที่เป็นตัวเลขเท่านั้น</p></div> -->
                </VCol>
              </VRow>
            </VCardText>
          </VCard>

          <VCard
            v-if="dataDashboard.donutChartV && Object.keys(dataDashboard.donutChartV).length > 0"
          >
            <VCardText>
              <VRow>
                <VCol cols="12" md="12" sm="12">
                  <VAlert color="info">
                    <h3 class="text-white">
                      {{ dataDashboard.donutChartV.header }}
                    </h3>
                  </VAlert>
                </VCol>
              </VRow>
              <VRow>
                <VCol cols="12" md="6" sm="12">
                  <PieChartsDonut :datas-bar-chart="dataDashboard.donutChartV.donut" />
                </VCol>
                <VCol cols="12" md="6" sm="12">
                  <AppDataTable
                    v-model="dataDashboard.donutChartV.datasTable"
                    :columns="columns5"
                    :header-no="false"
                    :paginator="false"
                    :footer="footer5"
                  />
                </VCol>
              </VRow>
            </VCardText>
          </VCard>
        </VCol>
      </VRow>
    </VCardText>
  </section>
</template>

<style>
@media print {
  .hide-on-print {
    display: none !important;
  }
}
</style>

<route lang="yaml">
meta:
  layout: blank
  action: read
</route>
