<script setup lang="ts">
interface breadcrumbItems {
  title: string
  disabled: boolean
  to: string
  active: boolean
  activeClass?: string
}

const breadcrumbItems: breadcrumbItems[] = [
  {
    title: 'แบบประเมินความพึงพอใจ',
    disabled: false,
    to: '/apps/home',
    active: false,
    activeClass: ''
  },
  {
    title: 'KBP Survey',
    disabled: false,
    to: '',
    active: false,
    activeClass: ''
  },
  {
    title: 'แบบประเมินความพึงพอใจของผู้มีส่วนได้ส่วนเสีย',
    disabled: false,
    to: '',
    active: true,
    activeClass: 'text-info'
  }
]
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <BtnGoBack />
    <EvaluationFormResult is-answer title="แบบประเมินความพึงพอใจของผู้มีส่วนได้ส่วนเสีย" />
  </div>
</template>
