<script setup lang="ts">
import { defineProps, ref } from 'vue'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import { booleanValidator, requiredValidator } from '@validators'

const props = defineProps({
  modelValue: <PERSON>ole<PERSON>,
  isAdd: <PERSON><PERSON><PERSON>,
  isEdit: <PERSON>olean,
  isView: <PERSON>olean,
  viewId: String,
  editId: String
})

const emit = defineEmits(['update:modelValue', 'update'])
const Swal = useSwal()
const callAxios = useAxios()
const refVForm: Ref<any> = ref(null)

const viewId = computed(() => props.viewId)
const editId = computed(() => props.editId)

const listStatus = ref([
  { id: true, name: 'ใช้งาน' },
  { id: false, name: 'ไม่ใช้งาน' }
])

const form = reactive({
  systemAssessmentsId: 0,
  systemAssessmentsName: '',
  isActive: false
})

const createFormData = () => {
  const formData = {
    systemAssessmentsName: form.systemAssessmentsName,
    isActive: form.isActive
  }

  if (form.systemAssessmentsId) formData.systemAssessmentsId = form.systemAssessmentsId

  return formData
}

const formDefault = structuredClone(toRaw(form))

const resetForm = () => {
  Object.assign(form, structuredClone(formDefault))
}

const onFormSubmit = async () => {
  try {
    if (!refVForm.value) return

    const { valid } = await refVForm.value.validate()

    if (valid) {
      if (props.isAdd) await AddForm()
      else if (props.isEdit) await EditForm()
      else throw new Error('Invalid state')
    }
  } catch (error) {
    Swal.AddConditionFailText('ไม่สามารถดำเนินการได้: ไม่มีสถานะที่ถูกต้อง')
  }
}

const AddForm = async () => {
  const confirmed = await Swal.ApproveConfirm()
  if (!confirmed) return

  const formData = createFormData()
  try {
    const response = await callAxios.RequestPost('/UserManagement/AddSystemAssessments', formData)

    if (response.status === 200) {
      resetForm()
      isDialogVisible.value = false
      emit('update')
    } else {
      Swal.AddFail()
    }
  } catch {
    Swal.AddConditionFailText('กรุณากรอกข้อมูลให้ถูกต้อง')
  }
}

const EditForm = async () => {
  const confirmed = await Swal.ApproveConfirm()
  if (!confirmed) return
  const formData = createFormData()
  try {
    const response = await callAxios.RequestPut(
      `/UserManagement/UpdateSystemAssessments?SystemAssessmentsId=${editId.value}`,
      formData
    )

    if (response.status === 200) {
      resetForm()
      isDialogVisible.value = false
      emit('update')
    } else {
      Swal.EditConditionFail()
    }
  } catch (error) {
    Swal.EditFail()
  }
}

const ViewForm = async () => {
  try {
    const response = await callAxios.RequestGetById(
      '/UserManagement/GetSystemAssessmentsById',
      `?SystemAssessmentsId=${viewId.value}`
    )

    if (response.status === 200) {
      const data = response.data.response

      form.systemAssessmentsId = data.systemAssessmentsId
      form.systemAssessmentsName = data.systemAssessmentsName
      form.isActive = data.isActive
    }
  } catch (error) {
    Swal.ViewFail()
  }
}

const closeDialog = async () => {
  const response = await Swal.ApproveCancel()
  if (response) {
    resetForm()
    isDialogVisible.value = false
  }
}

const setTitle = computed(() => {
  if (props.isEdit) return 'แก้ไขแบบสำรวจความคิดเห็น'

  return 'สร้างแบบสำรวจความคิดเห็น'
})

const isDialogVisible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value)
})

watch(
  () => isDialogVisible.value,
  async newVal => {
    if (newVal && props.isEdit) await ViewForm()
  }
)
</script>

<template>
  <div>
    <VDialog v-model="isDialogVisible" persistent class="v-dialog-lg" no-click-animation scrollable>
      <VCard :title="setTitle">
        <DialogCloseBtn variant="text" size="small" @click="closeDialog" />
        <VCardText>
          <VForm ref="refVForm" @submit.prevent="onFormSubmit">
            <VRow class="mb-4 mx-2 mt-3 justify-center">
              <VCol cols="12" md="10">
                <VRow>
                  <VCol cols="12" md="12">
                    <VRow class="mb-2" align="center">
                      <VCol cols="12" md="12" class="py-0 mb-2">
                        <label>
                          แบบสำรวจความคิดเห็น :
                          <small class="text-error">*</small>
                        </label>
                      </VCol>
                      <VCol cols="12" md="12" class="py-0">
                        <VTextField
                          v-model="form.systemAssessmentsName"
                          :rules="[requiredValidator]"
                          density="comfortable"
                          placeholder="ตัวอย่าง : แแบบสำรวจความคิดเห็นประจำปี 2567"
                          :counter="100"
                          :maxlength="100"
                        />
                      </VCol>
                    </VRow>
                  </VCol>

                  <VCol cols="12" md="12">
                    <VRow class="mb-2" align="center">
                      <VCol cols="12" md="12" class="py-0 mb-2">
                        <label>
                          สถานะ :
                          <small class="text-error">*</small>
                        </label>
                      </VCol>
                      <VCol cols="12" md="12" class="py-0">
                        <VAutocomplete
                          v-model="form.isActive"
                          :rules="[booleanValidator]"
                          :items="listStatus"
                          item-title="name"
                          item-value="id"
                          density="comfortable"
                          placeholder="ตัวอย่าง : ใช้งาน"
                          class="no-select"
                        />
                      </VCol>
                    </VRow>
                  </VCol>
                </VRow>
              </VCol>
            </VRow>

            <div class="d-flex flex-wrap justify-center mt-5">
              <div class="demo-space-x">
                <VBtn type="submit" color="blue-600" rounded="xl" prepend-icon="mdi-content-save">
                  บันทึก
                </VBtn>
                <VBtn
                  color="error-300"
                  rounded="xl"
                  prepend-icon="mdi-close-circle"
                  @click="closeDialog"
                >
                  ยกเลิก
                </VBtn>
              </div>
            </div>
          </VForm>
        </VCardText>
      </VCard>
    </VDialog>
  </div>
</template>
