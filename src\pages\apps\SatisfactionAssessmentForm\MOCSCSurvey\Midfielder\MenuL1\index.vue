<script setup>
const breadcrumbItems = [
  {
    title: 'แบบประเมินความพึงพอใจ'
  },
  {
    title: 'MOCSC Survey',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const ListsMenu = ref([
  {
    title: 'แบบประเมินความพึงพอใจการให้บริการศูนย์บริการประชาชน',
    to: {
      name: 'apps-SatisfactionAssessmentForm-MOCSCSurvey-Midfielder-MenuL2'
    },
    icon: 'mdi-list-box',
    color: 'orange-300'
  }
])

function onSearch(values) {
  alert(JSON.stringify(values, null, 2))
}
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <!-- {{ filter }} -->
    <VCard class="mb-5">
      <VCardText>
        <VRow class="align-center">
          <VCol
            v-for="(item, index) in ListsMenu"
            :key="index"
            cols="12"
            md="6"
            class="align-center"
          >
            <RouterLink :to="item.to">
              <VAlert prominent variant="outlined" :color="item.color" :icon="item.icon">
                {{ item.title }}
              </VAlert>
            </RouterLink>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
  </div>
</template>
