import type { VuetifyOptions } from 'vuetify'
import { resolveVuetifyTheme } from '@core/utils/vuetify'
import { themeConfig } from '@themeConfig'

export const staticPrimaryColor = '#666CFF'

const theme: VuetifyOptions['theme'] = {
  defaultTheme: resolveVuetifyTheme(),
  themes: {
    light: {
      dark: false,
      colors: {
        primary:
          localStorage.getItem(`${themeConfig.app.title}-lightThemePrimaryColor`) ||
          staticPrimaryColor,
        'on-primary': '#fff',
        secondary: '#6D788D',
        'secondary-100': '#BBBBBB',
        'neutral-50': '#F7F7EE',
        'on-secondary': '#fff',
        success: '#72E128',
        'on-success': '#fff',
        info: '#26C6F9',
        'on-info': '#fff',
        warning: '#FDB528',
        'on-warning': '#fff',
        warmSand: '#f5e5b8',
        error: '#FF4D49',
        background: '#ffffff',
        'on-background': '#000000', // ปรับสีทั้งหมดในระบบ
        'on-surface': '#000000', // ปรับสีทั้งหมดในระบบ
        'on-calendar': '#262B43',
        'on-active-dark': '#000000',
        'perfect-scrollbar-thumb': '#DBDADE',
        'snackbar-background': '#212121',
        'tooltip-background': '#262732',
        'alert-background': '#ffffff',
        'grey-50': '#FAFAFA',
        'grey-100': '#F4F5FA',
        'grey-200': '#F5F5F7',
        'grey-300': '#EAEAEA',
        'grey-400': '#BDBDBD',
        'grey-500': '#9E9E9E',
        'grey-600': '#757575',
        'grey-700': '#616161',
        'grey-800': '#474747',
        'grey-900': '#212121',
        'grey-secondary': '#d9d9d9',
        'btn-green': '#74DAC1',
        'btn-add': '#86dd71',
        'btn-pdf': '#C91A20',
        'btn-excel': '#007934',
        'btn-search': '#9aa2ff',
        'info-500': '#089EAD',
        'blue-100': '#bcdef3',
        'blue-200': '#1373F0',
        'blue-500': '#3F8DF4',
        'blue-600': '#1640ac',
        'blue-700': '#0094FF',
        'blue-800': '#4f9cf9',
        'success-200': '#29c105',
        'success-300': '#427A23',
        'success-400': '#2CAA0E',
        yellow: '#FFC95D',
        'yellow-50': '#FFC412',
        'error-200': '#E76F51',
        'error-300': '#B21F29',
        'error-400': '#FF69A8',
        'error-500': '#EA424F',
        'error-600': '#EA1E63',
        'error-700': '#FF0000',
        orange: '#ED7D31',
        'orange-100': '#FF6712',
        'orange-200': '#E7511E',
        'orange-300': '#FE960F',
        grey: '#A3A7A9',
        'success-50': '#9ED403',
        'yellow-100': '#fcfee7',
        'info-100': '#eafcfc',
        'info-200': '#A7F2F2',
        'success-100': '#dff9d8',
        white: '#ffffff',
        navy: '#142F43',
        'navy-100': '#1640ac',
        'navy-200': '#091a46',
        'navy-300': '#091a46',
        'brown-100': '#df9433',
        'brown-200': '#402a13',
        'purple-100': '#E6ECFF',
        'yellow-200': '#FFD500',
        'blue-400': '#3F51B5',
        'purple-200': '#d400ff',
        'blue-header': '#e6ecff',
        'sub-no': '#f6f6f6',
        'nav-active-orange': '#ffa736',
        'nav-active-blue': '#44adf9',
        'orange-bg-start': '#fe9c1a',
        'orange-bg-end': '#feb95f',
        'pink-bg-start': '#da628b',
        'pink-bg-end': '#ff84ad'
      },
      variables: {
        'code-color': '#d400ff',
        'border-color': '#4c4e64',
        'hover-opacity': 0.05,
        'overlay-scrim-background': '#4C4E64',
        'overlay-scrim-opacity': 0.5,

        // Shadows
        'shadow-key-umbra-opacity': 'rgba(var(--v-theme-on-surface), 0.08)',
        'shadow-key-penumbra-opacity': 'rgba(var(--v-theme-on-surface), 0.05)',
        'shadow-key-ambient-opacity': 'rgba(var(--v-theme-on-surface), 0.03)'
      }
    },
    dark: {
      dark: true,
      colors: {
        primary:
          localStorage.getItem(`${themeConfig.app.title}-darkThemePrimaryColor`) ||
          staticPrimaryColor,
        'on-primary': '#fff',
        secondary: '#30334E',
        'secondary-100': '#BBBBBB',
        'neutral-50': '#4B4B4B',
        'on-secondary': '#fff',
        success: '#72E128',
        'on-success': '#fff',
        info: '#26C6F9',
        'on-info': '#fff',
        warning: '#FDB528',
        'on-warning': '#fff',
        error: '#FF4D49',
        background: '#282A42',
        'on-background': '#eaeaff',
        surface: '#30334E',
        'on-surface': '#eaeaff',
        'on-calendar': '#262B43',
        'on-active-dark': '#000000',
        'perfect-scrollbar-thumb': '#4A5072',
        'snackbar-background': '#F5F5F5',
        'on-snackbar-background': '#30334E',
        'tooltip-background': '#464A65',
        'alert-background': '#282A42',
        'grey-50': '#2A2E42',
        'grey-100': '#41435c',
        'grey-200': '#3A3E5B',
        'grey-300': '#5E6692',
        'grey-400': '#7983BB',
        'grey-500': '#8692D0',
        'grey-600': '#AAB3DE',
        'grey-700': '#B6BEE3',
        'grey-800': '#CFD3EC',
        'grey-900': '#E7E9F6',
        'grey-secondary': '#333333',
        'btn-green': '#74DAC1',
        'btn-add': '#86dd71',
        'btn-pdf': '#C91A20',
        'btn-excel': '#007934',
        'btn-search': '#9aa2ff',
        'info-500': '#089EAD',
        'blue-100': '#bcdef3',
        'blue-200': '#1373F0',
        'blue-600': '#1640ac',
        'blue-700': '#0094FF',
        'blue-800': '#4f9cf9',
        'success-200': '#29c105',
        'success-300': '#427A23',
        'success-400': '#2CAA0E',
        yellow: '#FFC95D',
        'yellow-50': '#FFC412',
        'error-200': '#E76F51',
        'error-300': '#B21F29',
        'error-400': '#FF69A8',
        'error-500': '#EA424F',
        'error-600': '#EA1E63',
        'error-700': '#FF0000',
        orange: '#ED7D31',
        'orange-100': '#FF6712',
        'orange-200': '#E7511E',
        'orange-300': '#FE960F',
        grey: '#A3A7A9',
        'success-50': '#9ED403',
        'yellow-100': '#fcfee7',
        'info-100': '#eafcfc',
        'info-200': '#A7F2F2',
        'success-100': '#dff9d8',
        white: '#ffffff',
        navy: 'ffffff',
        'navy-100': '#1640ac',
        'navy-200': '#091a46',
        'navy-300': '#ffffff',
        'brown-100': '#df9433',
        'brown-200': '#402a13',
        'brown-appeals': '#A98700',
        'purple-100': '#2A2E42',
        'yellow-200': '#FFD500',
        'blue-400': '#3F51B5',
        'purple-200': '#d400ff',
        'purple-cancel': '#9966CC',
        'blue-header': '#e6ecff',
        'sub-no': '#636272',
        'nav-active-orange': '#ffa736',
        'nav-active-blue': '#44adf9',
        'orange-bg-start': '#fe9c1a',
        'orange-bg-end': '#feb95f',
        'pink-bg-start': '#da628b',
        'pink-bg-end': '#ff84ad'
      },
      variables: {
        'code-color': '#d400ff',
        'border-color': '#eaeaff',
        'hover-opacity': 0.05,
        'overlay-scrim-background': '#101121',
        'overlay-scrim-opacity': 0.6,

        // Shadows
        'shadow-key-umbra-opacity': 'rgba(20, 21, 33, 0.08)',
        'shadow-key-penumbra-opacity': 'rgba(20, 21, 33, 0.05)',
        'shadow-key-ambient-opacity': 'rgba(20, 21, 33, 0.03)'
      }
    }
  }
}

export default theme
