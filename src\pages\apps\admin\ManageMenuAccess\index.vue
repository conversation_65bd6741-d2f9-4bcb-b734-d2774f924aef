<script setup lang="ts">
import { useAuth } from '@/store/useAuth'
import { useAxios } from '@/store/useAxios'
import { useStatus } from '@/store/useStatus'
import { useSwal } from '@/store/useSwal'
import { onMounted, reactive, ref } from 'vue'

const callAxios = useAxios()
const callAuth = useAuth()
const callStatus = useStatus()
const Swal = useSwal()

// State and Reactive Data
const refVForm = ref(null)
const rolePermissions = ref([])
const selectedPermissions = ref([])

const currentPage = ref(1)
const rowPerPage = ref(20)
const totalRecords = ref(0)

// Breadcrumb Navigation
const breadcrumbItems = [
  { title: 'ผู้ดูแลระบบ', to: '/apps/home' },
  {
    title: 'จัดการสิทธิการเข้าถึงเมนู',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

// Filters
const filter = reactive({
  role_id: null,
  menu_id: null
})

const listFilters = reactive([
  {
    name: 'role_id',
    type: 'select',
    label: 'สิทธิ',
    title: 'role_name',
    value: 'role_id',
    required: true,
    items: []
  },
  {
    name: 'menu_id',
    type: 'select',
    label: 'เลือกเมนูหลัก',
    title: 'title',
    value: 'menu_id',
    required: true,
    items: []
  }
])

// Table Fields
const listFields = reactive([
  { field: 'title', header: 'ชื่อเมนู', sortable: true },
  { field: 'isRead', header: 'ดู', sortable: true },
  { field: 'isInsert', header: 'เพิ่ม', sortable: true },
  { field: 'isUpdate', header: 'แก้ไข', sortable: true },
  { field: 'isDelete', header: 'ลบ', sortable: true }
])

// Fetch Dropdown Data
const fetchDropdownItems = async () => {
  try {
    const response = await callAxios.RequestGet(`/public/system/roles?current_page=1&page_size=100`)

    if (response.status === 200) listFilters[0].items = [...response.data.data]

    const menuResponse = await callAxios.RequestGet('/system-menu/get-all-menu')

    if (menuResponse.status === 200) listFilters[1].items = [...menuResponse.data]
  } catch (error) {
    Swal.callCatch()
  }
}

// Fetch Menu Permissions for Role
const fetchRolePermissions = async () => {
  try {
    const response = await callAxios.RequestGet(
      `/role-permission/get-role-permission-menu/${filter.role_id}/${filter.menu_id}?current_page=1&page_size=100`
    )

    if (response.status === 200) {
      // ✅ ตั้ง rolePermissions.value ที่ได้จาก API ตรงนี้ก่อน
      rolePermissions.value = response.data.data

      // ✅ map แล้วใส่ permission เข้าไปแต่ละเมนู
      const menuList = rolePermissions.value.map(menu => ({
        ...menu,
        permission: {
          isRead: menu.is_read ?? false,
          isInsert: menu.is_insert ?? false,
          isUpdate: menu.is_update ?? false,
          isDelete: menu.is_delete ?? false
        }
      }))

      selectedPermissions.value = menuList
    }
  } catch (error) {
    Swal.callCatch()
  }
}

// Submit Form
const onFormSubmit = async () => {
  const confirmResponse = await Swal.ApproveConfirm()
  if (!confirmResponse) return

  const payload = selectedPermissions.value.map(item => ({
    role_permission_id: item.role_permission_id,
    is_read: item.permission.isRead,
    is_insert: item.permission.isInsert,
    is_update: item.permission.isUpdate,
    is_delete: item.permission.isDelete
  }))

  console.log('payload', payload)
  try {
    const response = await callAxios.RequestPatch(
      `/role-permission/update-role-permissions`,
      payload
    )

    if (response.status === 200) {
      Swal.EditSuccess()
      fetchRolePermissions()
    }
  } catch (error) {
    Swal.EditFail()
  }
}

// Reset Form
const resetForm = () => {
  selectedPermissions.value = []
}

// Handle Permission Selection
const togglePermission = menu => {
  const existingItem = selectedPermissions.value.find(item => item.menu_id === menu.menu_id)

  if (existingItem) {
    if (!menu.permission.isRead) {
      menu.permission.isInsert = false
      menu.permission.isUpdate = false
      menu.permission.isDelete = false
    }
    Object.assign(existingItem, menu)
  } else {
    if (!menu.permission.isRead) {
      menu.permission.isInsert = false
      menu.permission.isUpdate = false
      menu.permission.isDelete = false
    }
    selectedPermissions.value.push(menu)
  }
}

// Watch Filters and Trigger Fetch
const handleFilterSubmit = () => {
  if (filter.role_id && filter.menu_id) fetchRolePermissions()
  else Swal.AddConditionFailText('กรุณากรอกข้อมูลให้ถูกต้อง')
}

const onPageChange = (event: { page: number; rows: number }) => {
  currentPage.value = event.page + 1
  rowPerPage.value = event.rows
  handleFilterSubmit()
}

onMounted(() => {
  fetchDropdownItems()
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <FiltersAPI v-model="filter" :fields="listFilters" @submit="handleFilterSubmit" />

    <VForm ref="refVForm" @submit.prevent="onFormSubmit">
      <VCard>
        <AppDataTableAPI
          :columns="listFields"
          :value="selectedPermissions"
          :total-records="totalRecords"
          a
          :paginator="false"
          @page="onPageChange"
        >
          <template #isRead="{ data }">
            <div class="text-center">
              <VCheckbox v-model="data.permission.isRead" @change="togglePermission(data)" />
            </div>
          </template>
          <template #isInsert="{ data }">
            <div class="text-center">
              <VCheckbox
                v-model="data.permission.isInsert"
                :disabled="!data.permission.isRead"
                @change="togglePermission(data)"
              />
            </div>
          </template>
          <template #isUpdate="{ data }">
            <div class="text-center">
              <VCheckbox
                v-model="data.permission.isUpdate"
                :disabled="!data.permission.isRead"
                @change="togglePermission(data)"
              />
            </div>
          </template>
          <template #isDelete="{ data }">
            <div class="text-center">
              <VCheckbox
                v-model="data.permission.isDelete"
                :disabled="!data.permission.isRead"
                @change="togglePermission(data)"
              />
            </div>
          </template>
        </AppDataTableAPI>
      </VCard>

      <VRow>
        <VCol cols="12" class="d-flex justify-end mt-5">
          <VBtn type="submit" color="blue-600" rounded="xl" prepend-icon="mdi-content-save">
            บันทึก
          </VBtn>
        </VCol>
      </VRow>
    </VForm>
  </div>
</template>
