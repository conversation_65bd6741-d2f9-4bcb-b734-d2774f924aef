@use '@core/scss/base/mixins';

%nav {
  color: rgba(var(--v-theme-white), var(--v-high-emphasis-opacity));

  .nav-item-title {
    letter-spacing: 0.15px;
  }

  .nav-section-title {
    letter-spacing: 0.4px;
  }

  // สำหรับธีม 1 (สีขาว)
  .color-theme-1 & {
    color: rgba(var(--v-theme-white), var(--v-high-emphasis-opacity));
  }

  // สำหรับธีม 2 และ 3 (สีดำ)
  .color-theme-2 &,
  .color-theme-3 & {
    color: rgba(var(--v-theme-on-active-dark), var(--v-high-emphasis-opacity));
  }
}

%nav-link-active {
  background: rgb(var(--v-theme-nav-active-orange));
  color: rgb(var(--v-theme-on-background));

  @include mixins.elevation(3);
}

.color-theme-1 %nav-link-active {
  background: rgb(var(--v-theme-white));
  color: rgb(var(--v-theme-on-active-dark));

  & svg path {
    fill: rgba(var(--v-theme-on-active-dark), var(--v-high-emphasis-opacity));
  }
}

.color-theme-2 %nav-link-active,
.color-theme-3 %nav-link-active {
  background: rgb(var(--v-theme-white));
  color: rgb(var(--v-theme-on-active-dark));

  & svg path {
    fill: rgba(var(--v-theme-on-active-dark), var(--v-high-emphasis-opacity));
  }
}

%nav-link {
  a {
    color: inherit;
  }

  svg {
    path {
      fill: rgba(var(--v-theme-white), var(--v-high-emphasis-opacity));
    }
  }

  .color-theme-1 & svg path {
    fill: rgba(var(--v-theme-white), var(--v-high-emphasis-opacity));
  }

  .color-theme-2 & svg path,
  .color-theme-3 & svg path {
    fill: rgba(var(--v-theme-on-active-dark), var(--v-high-emphasis-opacity));
  }
}
