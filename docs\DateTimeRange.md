# DateTime Range Template for DateTimePicker

This document explains how to use the new datetime range functionality in the DateTimePicker component.

## Overview

The datetime range template allows users to select both a start and end date with time in a single picker interface. This is useful for scenarios like:
- Booking time slots
- Event scheduling
- Report date ranges with specific times
- Meeting duration selection

## Usage

### Basic DateTime Range

```vue
<template>
  <DateTimePicker
    v-model="dateTimeRange"
    datetime-range
    placeholder="เลือกช่วงวันที่และเวลา"
    label="DateTime Range"
  />
</template>

<script setup>
const dateTimeRange = ref(null)
</script>
```

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `datetime-range` | Boolean | false | Enables datetime range selection mode |
| `placeholder` | String | - | Placeholder text for the input |
| `label` | String | - | Label for the input field |
| `icon-style-flag` | Boolean | false | Enables icon styling |
| `disabled-dates` | Array | [] | Array of dates to disable |
| `min-date` | String | - | Minimum selectable date |
| `teleport` | Boolean | true | Whether to teleport the picker |
| `teleport-center` | Boolean | false | Whether to center the teleported picker |

### Data Format

The datetime range picker returns an array with two Date objects:

```javascript
// Example return value
[
  "2024-01-15T09:30:00", // Start datetime
  "2024-01-16T17:45:00"  // End datetime
]
```

### Display Format

The picker displays dates in Thai Buddhist calendar format with time:
- Single selection: `15 มกราคม 2567 09:30 -`
- Complete range: `15 มกราคม 2567 09:30 - 16 มกราคม 2567 17:45`

## Features

### 1. Thai Localization
- Uses Thai month names
- Displays years in Buddhist Era (BE) format (+543 years)
- Thai locale support

### 2. Time Selection
- 24-hour format
- Minute precision
- Time validation

### 3. Range Validation
- End time must be after start time
- Date range validation
- Disabled dates support

### 4. Styling Options
- Standard input styling
- Icon style with `icon-style-flag` prop
- Dark theme support
- Responsive design

## Examples

### With Icon Style
```vue
<DateTimePicker
  v-model="dateTimeRange"
  datetime-range
  icon-style-flag
  placeholder="เลือกช่วงวันที่และเวลา"
/>
```

### With Disabled Dates
```vue
<DateTimePicker
  v-model="dateTimeRange"
  datetime-range
  :disabled-dates="[new Date('2024-01-20'), new Date('2024-01-25')]"
  placeholder="เลือกช่วงวันที่และเวลา"
/>
```

### With Minimum Date
```vue
<DateTimePicker
  v-model="dateTimeRange"
  datetime-range
  :min-date="new Date().toISOString()"
  placeholder="เลือกช่วงวันที่และเวลา"
/>
```

## Events

| Event | Description | Payload |
|-------|-------------|---------|
| `update:modelValue` | Emitted when the datetime range changes | Array of two date strings |
| `click:clear` | Emitted when the clear button is clicked | Event object |

## Implementation Details

The datetime range template:
1. Uses the `@vuepic/vue-datepicker` component with `range` and `enable-time-picker` props
2. Formats dates using Thai Buddhist calendar
3. Handles time formatting with HH:MM format
4. Provides proper accessibility with ARIA labels
5. Supports both light and dark themes

## Browser Support

The datetime range picker supports all modern browsers that support:
- ES6+ JavaScript features
- CSS Grid and Flexbox
- HTML5 input types

## Troubleshooting

### Common Issues

1. **Date format issues**: Ensure your backend expects the ISO date format returned by the component
2. **Timezone handling**: The component uses UTC internally but displays in local timezone
3. **Validation errors**: Check that end date/time is after start date/time

### Performance Considerations

- The component is optimized for Vue 3's reactivity system
- Large disabled date arrays may impact performance
- Consider using date ranges instead of individual disabled dates for better performance
