// Write your overrides

@font-face {
  font-family: 'K2D';
  src:
    url('@/assets/fonts/K2D-Light.woff2') format('woff2'),
    url('@/assets/fonts/K2D-Light.woff') format('woff');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'K2D';
  src:
    url('@/assets/fonts/K2D-Medium.woff2') format('woff2'),
    url('@/assets/fonts/K2D-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'K2D';
  src:
    url('@/assets/fonts/K2D-Regular.woff2') format('woff2'),
    url('@/assets/fonts/K2D-Regular.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'K2D';
  src:
    url('@/assets/fonts/K2D-SemiBold.woff2') format('woff2'),
    url('@/assets/fonts/K2D-SemiBold.woff') format('woff');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

.v-file-input {
  .v-input__prepend {
    display: none;
  }
}
.swal2-container {
  z-index: 9999 !important;
}
.swal2-html-container {
  padding: 0.2em !important;
}

.fieldset-head {
  position: absolute;
  top: -2%;
  z-index: 1;
  background: white;
}

.fieldset-border {
  border: 2px solid;
}

.colored-toast.swal2-icon-success {
  background-color: #a5dc86 !important;
}

.colored-toast.swal2-icon-error {
  background-color: #f27474 !important;
}

.colored-toast.swal2-icon-warning {
  background-color: #f8bb86 !important;
}

.colored-toast.swal2-icon-info {
  background-color: #3fc3ee !important;
}

.colored-toast.swal2-icon-question {
  background-color: #87adbd !important;
}

.colored-toast .swal2-title {
  color: white;
}

.colored-toast .swal2-close {
  color: white;
}

.colored-toast .swal2-html-container {
  color: white;
}
.evaluation-form .v-selection-control--inline {
  flex: 1 0 auto;
  .v-label {
    width: 100%;
  }
}
