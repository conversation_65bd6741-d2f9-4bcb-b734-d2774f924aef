<script setup lang="ts">
import type { Settings } from 'v-slick-carousel'
import { VSlickCarousel } from 'v-slick-carousel'
import 'v-slick-carousel/style.css'

// import Tooltip from "@/@core/components/Public/Tooltip.vue";
const tooltipTitle = 'เงื่อนไขใหม่สำหรับรหัสผ่าน'

const tooltipContent = `
  1. รหัสผ่านต้องมีความยาว 8-16 ตัวอักษร<br />
  2. ต้องมีอักษรตัวพิมพ์ใหญ่ อย่างน้อย 1 ตัว<br />
  3. ต้องมีอักษรตัวพิมพ์เล็ก อย่างน้อย 1 ตัว<br />
  4. ต้องมีตัวเลข อย่างน้อย 1 ตัว และอักขระพิเศษ (#, ?, @)
`

const settings: Settings = {
  dots: true,
  focusOnSelect: true,
  infinite: true,
  speed: 500,
  groupsToScroll: 1,
  groupsToShow: 1,
  touchThreshold: 5
}
</script>

<template>
  <div class="carousel">
    <VSlickCarousel v-bind="settings">
      <div>Slide 1</div>
      <div>Slide 2</div>
      <div>Slide 3</div>
      <div>Slide 1</div>
      <div>Slide 2</div>
      <div>Slide 3</div>
      <div>Slide 1</div>
      <div>Slide 2</div>
      <div>Slide 3</div>
    </VSlickCarousel>

    <CkEditor />

    <VCol cols="12" md="12" class="py-0 mb-2">
      <label>
        ชื่อโลโก้ :
        <small class="text-error">*</small>
        <Tooltip :title="tooltipTitle" :content="tooltipContent" />
      </label>
    </VCol>
  </div>
</template>

<style scoped>
.carousel {
  padding: 28px;
}
</style>
