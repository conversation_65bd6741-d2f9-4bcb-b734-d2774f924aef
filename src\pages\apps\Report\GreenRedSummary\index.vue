<script setup lang="ts">
import { useRouter } from 'vue-router'
import FiltersAPI from '@/components/FiltersAPI.vue'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'

const router = useRouter()
const callAxios = useAxios()
const Swal = useSwal()

const breadcrumbItems = [
  {
    title: 'รายงาน',
    disable: false,
    to: '/apps/report/menu'
  },
  {
    title: 'ตารางสรุปผลการตรวจ (เขียว/แดง)',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const listFilter = reactive([
  {
    name: 'Year',
    type: 'dateYear',
    label: 'ปี',
    default: '',
    required: true,
    placeholder: 'เลือกปี'
  },
  {
    name: 'TransactionSatisfactionAudit',
    type: 'select',
    label: 'ประเภทการสรุปผล',
    required: true,
    default: '',
    title: 'name',
    value: 'id',
    items: [
      { name: 'ผลตรวจคัดกรอง', id: '1' },
      { name: 'ผลตรวจประเมินพื้นที่', id: '2' }
    ],
    placeholder: 'เลือกประเภทการสรุปผล'
  },
  {
    name: 'TransactionRegisterFormCode',
    type: 'text',
    label: 'เลขที่ใบสมัคร',
    default: '',
    placeholder: 'ตัวอย่าง:G670001'
  },
  {
    name: 'GovermentName',
    type: 'select',
    label: 'ชื่อส่วนราชการ (เจ้าภาพหลัก)',
    default: '',
    title: 'name',
    value: 'id',
    items: [{ name: 'ทั้งหมด', id: '' }],
    placeholder: 'เลือกชื่อส่วนราชการ (เจ้าภาพหลัก)'
  },
  {
    name: 'OrgStructureName',
    type: 'select',
    label: 'หน่วยงาน',
    default: '',
    title: 'name',
    value: 'id',
    items: [{ name: 'ทั้งหมด', id: '' }],
    placeholder: 'เลือกหน่วยงาน'
  },
  {
    name: 'Province',
    type: 'select',
    label: 'จังหวัด',
    default: '',
    title: 'name',
    value: 'id',
    items: [{ name: 'ทั้งหมด', id: '' }],
    placeholder: 'เลือกจังหวัด'
  },
  {
    name: 'Subcommittee',
    type: 'select',
    label: 'คณะอนุกรรมการ',
    default: '',
    title: 'name',
    value: 'id',
    items: [{ name: 'ทั้งหมด', id: '' }],
    placeholder: 'เลือกคณะอนุกรรมการ'
  },
  {
    name: 'Result',
    type: 'select',
    label: 'ผลการตรวจ',
    default: '',
    title: 'name',
    value: 'id',
    items: [
      { name: 'ทั้งหมด', id: '0' },
      { name: 'ผ่าน', id: '1' },
      { name: 'ไม่ผ่าน', id: '2' }
    ],
    placeholder: 'เลือกผลการตรวจ'
  },
  {
    name: 'ScoreStart',
    type: 'textNumber',
    label: 'ช่วงคะแนน เริ่มต้น',
    default: '',
    placeholder: 'เลือกช่วงคะแนน เริ่มต้น ตัวอย่าง:10',
    rules: [v => /^[0-9]*$/.test(v)]
  },
  {
    name: 'ScoreEnd',
    type: 'textNumber',
    label: 'ช่วงคะแนน สิ้นสุด',
    default: '',
    placeholder: 'เลือกช่วงคะแนน สิ้นสุด ตัวอย่าง:30',
    rules: [v => /^[0-9]*$/.test(v)]
  }
])

const filter = reactive({
  Year: '',
  TransactionSatisfactionAudit: null,
  TransactionRegisterFormCode: '',
  GovermentName: '',
  OrgStructureName: '',
  Province: '',
  Subcommittee: '',
  Result: '0',
  ScoreStart: '',
  ScoreEnd: ''
})

const form = reactive({
  departmentChildLists: '',
  StructName: '',
  dateyear: ''
})

const urlReport = ref('')

const GetList = async () => {
  try {
    if (!filter.Year) {
      Swal.validateText('กรุณาเลือกปี')

      return
    }
    if (filter.TransactionSatisfactionAudit == null) {
      Swal.validateText('กรุณาเลือกประเภทการสรุปผล')

      return
    }

    const params = new URLSearchParams()

    if (filter.Year) params.append('Year', filter.Year)

    if (filter.TransactionSatisfactionAudit) {
      params.append('Type', filter.TransactionSatisfactionAudit)
    }
    if (filter.TransactionRegisterFormCode) {
      params.append('TransactionRegisterFormCode', filter.TransactionRegisterFormCode)
    }

    // ชื่อส่วนราชการ(เจ้าภาพหลัก)
    if (filter.GovermentName) params.append('OrgStructureId', filter.GovermentName)

    // หน่วยงาน
    if (filter.OrgStructureName) params.append('TransactionGovermentId', filter.OrgStructureName)

    if (filter.Province) params.append('ProvinceId', filter.Province)

    if (filter.Subcommittee) params.append('SubcommitteeName', filter.Subcommittee)

    if (filter.Result != '0') params.append('Result', filter.Result)

    if (filter.ScoreStart) params.append('ScoreStart', filter.ScoreStart)

    if (filter.ScoreEnd) params.append('ScoreEnd', filter.ScoreEnd)

    const response = await callAxios.RequestGet(
      `/Report/ReadReportAuditResultGECC?${params.toString()}`
    )

    if (response.status === 200) urlReport.value = response.data.response.reportLink
    else Swal.AddConditionFailText('เกิดข้อผิดพลาดในการดึงข้อมูลรายงาน')
  } catch (error) {
    console.error('Error fetching report:', error)
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการดึงข้อมูลรายงาน')
  }
}

const getDropdownItems = async () => {
  try {
    // Fetch data for ชื่อส่วนราชการ (เจ้าภาพหลัก)
    let response = await callAxios.RequestGet('/OtherMaster/DepartmentParrentLists?flag=true')
    if (response.status === 200) {
      const DepartmentParrent = listFilter.find(filter => filter.name === 'GovermentName')

      if (DepartmentParrent) {
        DepartmentParrent.items = [{ name: 'ทั้งหมด', id: '' }, ...response.data.response]
      }
    }

    // Fetch data for Province
    response = await callAxios.RequestGet('/OtherMaster/ProvinceLists')
    if (response.status === 200) {
      const Province = listFilter.find(filter => filter.name === 'Province')
      if (Province) {
        Province.items = [{ name: 'ทั้งหมด', id: '' }, ...response.data.response]
      }
    }
  } catch (error) {
    console.error('Error fetching dropdown items:', error)
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการโหลดข้อมูล')
  }
}

// Fetch กรม/หน่วยงาน
const fetchDepartmentChildLists = async (departmentParrentId: string) => {
  if (!departmentParrentId) return

  try {
    const response = await callAxios.RequestGetById(
      '/OtherMaster/DepartmentChildLists',
      `?OrgGroupId=${departmentParrentId}&Id=${filter.GovermentName}&flag=true`
    )

    if (response.status === 200) {
      const DepartmentChildLists = listFilter.find(filter => filter.name === 'OrgStructureName')

      if (DepartmentChildLists) {
        DepartmentChildLists.items = [{ name: 'ทั้งหมด', id: '' }, ...response.data.response]
      }
    }
  } catch (error) {
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการโหลดข้อมูลอำเภอ')
  }
}

const fetchSubcommitteeLists = async (ProvinceId: string) => {
  if (!ProvinceId) return

  try {
    const response = await callAxios.RequestGetById(
      '/OtherMaster/SubcommitteeProviceLists',
      `?Province=${ProvinceId}`
    )

    if (response.status === 200) {
      const Submmittee = listFilter.find(filter => filter.name === 'Subcommittee')

      if (Submmittee) {
        Submmittee.items = [{ name: 'ทั้งหมด', id: '' }, ...response.data.response]
      }
    }
  } catch (error) {
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการโหลดข้อมูลอนุกรรมการ')
  }
}

watch(
  () => filter.GovermentName,
  async newDepartmentParrent => {
    filter.OrgStructureName = ''
    if (newDepartmentParrent !== '') {
      fetchDepartmentChildLists(newDepartmentParrent)
    } else {
      const DepartmentChildLists = listFilter.find(filter => filter.name === 'OrgStructureName')

      if (DepartmentChildLists) DepartmentChildLists.items = [{ name: 'ทั้งหมด', id: '' }]
    }
  }
)

watch(
  () => filter.Province,
  async newProvince => {
    filter.Subcommittee = ''
    if (newProvince !== '') {
      fetchSubcommitteeLists(newProvince)
    } else {
      const Submmittee = listFilter.find(filter => filter.name === 'Subcommittee')

      if (Submmittee) Submmittee.items = [{ name: 'ทั้งหมด', id: '' }]
    }
  }
)

onMounted(() => {
  getDropdownItems()
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <FiltersAPI v-model="filter" :fields="listFilter" @submit="GetList" />

    <VCard v-if="urlReport">
      <iframe
        v-if="urlReport"
        :src="urlReport"
        width="100%"
        height="800"
        frameborder="0"
        allowfullscreen
      />
    </VCard>
    <VRow>
      <VCol cols="12" class="d-flex justify-end mt-5 me-4">
        <BtnGoBack />
      </VCol>
    </VRow>
  </div>
</template>
