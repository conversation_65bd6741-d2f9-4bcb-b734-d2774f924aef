<script setup lang="ts">
import DialogEditTemplate from './DialogEditTemplate.vue'
import DialogViewTemplate from './DialogViewTemplate.vue'
import IconEdit from '@/@core/components/button/IconEdit.vue'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import type { IGetSystemTemplateRes } from '@interfaces/UserManagementInterface'

const callAxios = useAxios()
const Swal = useSwal()
const currentPage = ref(1)
const rowPerPage = ref(20)
const totalRecords = ref(0)
const isDialogVisible = ref(false)
const isEdit = ref(false)
const isView = ref(false)
const editId = ref()
const ListItem = ref<IGetSystemTemplateRes[]>([])

const openEditDialog = (id: string) => {
  isEdit.value = true
  isView.value = false
  isDialogVisible.value = true
  editId.value = id
}

const openViewDialog = (id: string) => {
  isView.value = true
  isEdit.value = false
  isDialogVisible.value = true
  editId.value = id
}

const breadcrumbItems = [
  {
    title: 'ผู้ดูแลระบบ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'หน้าจัดการ Template',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const listFields = ref([
  {
    field: 'templateName',
    header: 'ชื่อ Template',
    sortable: false
  },
  {
    field: 'isDefault',
    header: 'ค่าเริ่มต้น',
    sortable: false
  },
  {
    field: 'isActive',
    header: 'สถานะ',
    sortable: false
  },
  {
    field: 'options',
    header: 'การจัดการ',
    sortable: false,
    style: { textAlign: 'center' }
  }
])

const GetList = async () => {
  try {
    const queryParams = new URLSearchParams({
      Page: currentPage.value.toString(),
      PageSize: rowPerPage.value.toString()
    })

    const response = await callAxios.RequestGet(
      `/UserManagement/GetSystemTemplate?${queryParams.toString()}`
    )

    if (response.status === 200) {
      totalRecords.value = response.data.count
      ListItem.value = response.data.response as IGetSystemTemplateRes[]
    } else {
      Swal.AddConditionFailText('ไม่สามารถโหลดข้อมูลได้')
    }
  } catch (error) {
    Swal.callCatch()
  }
}

const handleDialogUpdate = () => {
  GetList()
}

const checkRowHighlight = (rowData: any) => {
  if (rowData && typeof rowData.no === 'string') {
    const noNumber = parseInt(rowData.no, 10)

    return noNumber % 2 === 0 ? 'bg-sub-no' : ''
  }

  return ''
}

onMounted(() => {
  GetList()
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <VCard class="px-4">
      <AppDataTableAPI
        :columns="listFields"
        :value="ListItem"
        :header-no="true"
        :paginator="false"
        :row-class="checkRowHighlight"
      >
        <!-- กำหนดสีของ Chip ในคอลัมน์สถานะ -->
        <template #isActive="slotProps">
          <ChipStatus
            :status="slotProps.data.isActive"
            :label="slotProps.data.isActive === 'ใช้งาน' ? 'ใช้งาน' : ''"
          />
        </template>

        <!-- ส่วน Icon การจัดการ -->
        <template #options="slotProps">
          <div class="text-center">
            <IconBtn @click="openViewDialog(slotProps.data.templateId)">
              <VIcon icon="mdi-monitor-eye" />
              <VTooltip open-delay="500" location="top" activator="parent">พรีวิว</VTooltip>
            </IconBtn>
            <IconEdit @click="openEditDialog(slotProps.data.templateId)" />
          </div>
        </template>
      </AppDataTableAPI>
    </VCard>

    <DialogEditTemplate
      v-model="isDialogVisible"
      :is-edit="isEdit"
      :is-view="isView"
      :view-id="editId"
      :edit-id="editId"
      @update="handleDialogUpdate"
    />

    <DialogViewTemplate
      v-model="isDialogVisible"
      :is-edit="isEdit"
      :is-view="isView"
      :view-id="editId"
      :edit-id="editId"
      @update="handleDialogUpdate"
    />
  </div>
</template>

<style scoped>
.no-select {
  user-select: none;
}
</style>
