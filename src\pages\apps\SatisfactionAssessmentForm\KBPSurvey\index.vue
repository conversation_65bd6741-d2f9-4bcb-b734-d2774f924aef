<script lang="ts" setup>
import { filterMenu } from '@utils'

const inProcess = ref(true)

const breadcrumbItems = [
  {
    title: 'แบบประเมินความพึงพอใจ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'KBP Survey',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const ListsMenu = ref([
  {
    title: 'แบบประเมินความพึงพอใจการให้บริการ (ส่วนภูมิภาค)',
    toRaw: 'แบบประเมินความพึงพอใจการให้บริการ (ส่วนภูมิภาค)',
    to: {
      name: 'apps-SatisfactionAssessmentForm-KBPSurvey-GroupOrIndividualSurvey'
    },
    icon: 'mdi-list-box',
    color: 'blue-700'
  },
  {
    title: 'แบบประเมินความพึงพอใจของผู้มีส่วนได้ส่วนเสีย',
    toRaw: 'แบบประเมินความพึงพอใจของผู้มีส่วนได้ส่วนเสีย',
    to: {
      name: 'apps-SatisfactionAssessmentForm-KBPSurvey-MOC-KBPS-8'
    },
    icon: 'mdi-book-open',
    color: 'error-400'
  }
])

onMounted(async () => {
  if (ListsMenu.value) {
    ListsMenu.value = await filterMenu(ListsMenu.value)
    inProcess.value = false
  } else {
    inProcess.value = false
  }
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <VCard class="mb-5">
      <VCardText>
        <VRow class="align-center">
          <VCol
            v-for="(item, index) in ListsMenu"
            :key="index"
            cols="12"
            md="6"
            class="align-center"
          >
            <RouterLink :to="item.to">
              <VAlert prominent variant="outlined" :color="item.color" :icon="item.icon">
                {{ item.title }}
              </VAlert>
            </RouterLink>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
  </div>
</template>
