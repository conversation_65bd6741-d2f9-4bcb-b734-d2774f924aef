import { IDistrict, IDistrictFilter, IGroupUser, ILevelUser, IOrganizations, IProvince, IProvinceFilter, IRoles, IRolesFilter, ISubDistrict, ITitles } from '@/@core/interfaces/AllStatusMaster'
import { useAxios } from '@/store/useAxios'
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useStatus = defineStore('useStatus', () => {
  const callAxios = useAxios()
  const listGroupUser = ref<IGroupUser[]>([
    { id: 1, name: 'ผู้ใช้งานทั่วไป' },
    { id: 2, name: 'ผู้ใช้งานส่วนกลาง' },
    { id: 3, name: 'ผู้ใช้งานภายนอก' },
    { id: 4, name: 'ผู้บริหาร' },
    { id: 5, name: 'ผู้ดูแลระบบ' }
  ])
  const listLevelUser = ref<ILevelUser[]>([
    { id: 1, name: 'อำเภอ' },
    { id: 2, name: 'จังหวัด' },
    { id: 3, name: 'ส่วนกลาง' }
  ])
  const listSubDistrict = ref<ISubDistrict[]>([])
  const listDistrict = ref<IDistrict[]>([])
  const listProvince = ref<IProvince[]>([])
  const listTitles = ref<ITitles[]>([])
  const listRoles = ref<IRoles[]>([])
  const listOrganizations = ref<IOrganizations[]>([])
  const user_id = ref<number | null>(null)
  const manageusremode = ref<string>('')

  const listDistrictFilter = ref<IDistrictFilter[]>([])
  const listProvinceFilter = ref<IProvinceFilter[]>([])
  const listRolesFilter = ref<IRolesFilter[]>([])
  const MapGroupUserStatus = (id: number) => {
    return listGroupUser.value.find(x => x.id === id)?.name
  }
  const MapLevelUserStatus = (id: number) => {
    return listLevelUser.value.find(x => x.id === id)?.name
  }
  const getDistrict = async (provinceId: number) => {
    const response = await callAxios.RequestGet(`/public/system/provinces/${provinceId}/districts?current_page=1&page_size=100`)
    if (response.status === 200) {
      listDistrict.value = response.data.data as IDistrict[]
      listDistrictFilter.value = listDistrict.value.map(({
        district_id,
        district_name
      }) => (
        {
          district_id,
          district_name
        }
      ))
      listDistrictFilter.value.unshift({ district_id: null, district_name: 'ทั้งหมด' })
    }
  }
  const getSubDistrict = async (districtId: number, provinceId: number) => {
    const response = await callAxios.RequestGet(`/public/system/provinces/${provinceId}/districts/${districtId}/sub-districts?current_page=1&page_size=100`)
    if (response.status === 200) {
      listSubDistrict.value = response.data.data as ISubDistrict[]
    }
  }
  const getProvince = async () => {
    const response = await callAxios.RequestGet(`/public/system/provinces?current_page=1&page_size=100`)
    if (response.status === 200) {
      listProvince.value = response.data.data as IProvince[]
      listProvinceFilter.value = listProvince.value.map(({
        province_id,
        province_name
      }) => (
        {
          province_id,
          province_name
        }
      ))
      listProvinceFilter.value.unshift({ province_id: null, province_name: 'ทั้งหมด' })
    }
  }

  const getTitles = async () => {
    const response = await callAxios.RequestGet(`/public/system/titles?current_page=1&page_size=100`)
    if (response.status === 200) {
      listTitles.value = response.data.data as ITitles[]
    }
  }
  const getRoles = async () => {
    const response = await callAxios.RequestGet(`/public/system/roles?current_page=1&page_size=100`)
    if (response.status === 200) {
      listRoles.value = response.data.data as IRoles[]
      listRolesFilter.value = listRoles.value.map(({
        role_id,
        role_name
      }) => (
        {
          role_id,
          role_name
        }
      ))
      listRolesFilter.value.unshift({ role_id: null, role_name: 'ทั้งหมด' })
    }
  }

  const getOrganizations = async () => {
    const response = await callAxios.RequestGet(`/public/system/organizations?current_page=1&page_size=100`)
    if (response.status === 200) {
      listOrganizations.value = response.data.data as IOrganizations[]
    }
  }
  const getRoleUser = (id: number) => {
    return listRoles.value.find(x => x.role_id === id)
  }
  const setUsetId = (id: number | null, mode: string) => {
    user_id.value = id
    manageusremode.value = mode
  }
  return {
    listGroupUser,
    listLevelUser,
    MapGroupUserStatus,
    MapLevelUserStatus,
    getDistrict,
    getSubDistrict,
    getProvince,
    listSubDistrict,
    listDistrict,
    listProvince,
    listTitles,
    listRoles,
    listOrganizations,
    getTitles,
    getRoles,
    getOrganizations,
    getRoleUser,
    user_id,
    manageusremode,
    setUsetId,
    listDistrictFilter,
    listProvinceFilter,
    listRolesFilter
  }
}, {
  persist: {
    paths: ['listRoles', 'manageusremode', 'user_id', 'listRolesFilter']
  }
})
