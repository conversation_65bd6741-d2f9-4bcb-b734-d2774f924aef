<script setup>
import { ref } from 'vue'
import backgroundImg from '@public/assets/images/background/bg1.png'
import { RouterLink } from 'vue-router'
import { useAxios } from '@/store/useAxios'
const callAxios = useAxios()

const Card = ref([
  { icon: 'flaticon-office', title: 'กระทรวง', color: ' bg-yellow' },
  {
    icon: 'flaticon-website',
    title: 'ส่วนราชการไม่สังกัดสำนักงานนายกรัฐมนตรี กระทรวง หรือทบวง',
    color: 'bg-red'
  },
  { icon: 'flaticon-pie-charts', title: 'องค์กร/องค์กรอิสระ', color: 'bg-green' },
  { icon: 'flaticon-pie-charts', title: 'องค์กรระหว่างประเทศ', color: 'bg-green' }
])

const GetSystemLink = () => {
  callAxios.RequestGet('/OtherMaster/GetSystemLink').then(response => {
    if (response.status == 200) {
      Card.value = response.data.response
    }
  })
}

const baseUrlImg = localStorage.baseUrlImg

const addActive = ref(1)

onMounted(() => {
  GetSystemLink()
})
</script>

<template>
  <section :style="`background-image: url(${backgroundImg});`">
    <div class="container">
      <div class="row">
        <div class="col-lg-3 col-md-6" v-for="(item, ind) in Card" @mouseenter="addActive = ind">
          <div
            :class="`icon-bx-wraper style-1 box-hover text-center m-b30 ${addActive === ind ? 'active' : ''}`"
          >
            <div :class="`icon-bx-md radius bg-green shadow-green`">
              <a :href="item.url" target="_blank" class="icon-cell">
                <img :src="baseUrlImg + item.pathName" alt="" style="z-index: 0" />
              </a>
            </div>
            <div class="icon-content">
              <h5 class="dlab-title">{{ item.linkName }}</h5>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
