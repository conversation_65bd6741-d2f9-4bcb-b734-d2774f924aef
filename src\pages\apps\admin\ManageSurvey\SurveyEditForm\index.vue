<script setup lang="ts">
import DialogEditSurveyForm from './DialogEditSurveyForm.vue'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'

const callAxios = useAxios()
const Swal = useSwal()
const route = useRoute()
const router = useRouter()
const id = ref(route.query.id)

// Pagination and List Management
const inProcess = ref(false)

// Dialog Controls
const isDialogVisible = ref(false)
const isView = ref(false)
const isEdit = ref(false)
const isAdd = ref(false)
const refVForm = ref<any>(null)
const editId = ref('')

// Form Data
const form = reactive({
  govermentId: '',
  systemAssessmentsId: '',
  description: '',
  name: '',
  isActive: false,
  isRequired: false
})

// Breadcrumb Items
const breadcrumbItems = [
  { title: 'ผู้ดูแลระบบ', disabled: false, to: '/apps/home' },
  {
    title: 'จัดการแบบประเมินความพึงพอใจ',
    disabled: false,
    to: '/apps/admin/ManageSurvey'
  },
  {
    title: 'แก้ไขฟอร์มแบบประเมินความพึงพอใจ',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

// Table Fields
const listFields = ref([
  {
    field: 'assessmentsName',
    header: 'รายละเอียด',
    sortable: true,
    style: { textAlign: 'start' }
  },
  {
    field: 'isActive',
    header: 'สถานะ',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'options',
    header: 'การจัดการ',
    sortable: false,
    style: { textAlign: 'center' }
  }
])

const assessmentsData = ref([])

// Fetch List Data
// const GetList = async () => {
//   try {
//     inProcess.value = true;
//     const response = await callAxios.RequestGet(
//       `/UserManagement/GetAssessments?SystemAssessmentsId=${id.value}`
//     );

//     if (response.status === 200) {
//       const responseData = response.data.response;
//       form.systemAssessmentsId = responseData.systemAssessmentsId;
//       form.description = responseData.detail;
//       assessmentsData.value = responseData.assessments;
//     } else {
//       Swal.isNotFound();
//     }
//   } catch (error) {
//     Swal.callCatch();
//   } finally {
//     inProcess.value = false;
//   }
// };

const GetList = async () => {
  try {
    inProcess.value = true

    const response = await callAxios.RequestGet(
      `/UserManagement/GetAssessments?SystemAssessmentsId=${id.value}`
    )

    if (response.status === 200) {
      const responseData = response.data.response

      form.systemAssessmentsId = responseData.systemAssessmentsId
      form.description = responseData.detail
      assessmentsData.value = responseData.assessments

      if (!responseData || !responseData.assessments || responseData.assessments.length === 0) {
        // ข้อมูลเป็นค่าว่าง -> เรียก POST API
        const defaultPayload = {
          detail:
            'ตัวอย่าง :ศูนย์บริการประชาชน สำนักงานปลัดสำนักนายกรัฐมนตรี ในฐานะฝ่ายเลขานุการคณะกรรมการอำนวยการศูนย์ราชการสะดวก ขอความร่วมมือผู้รับบริการทุกท่านทำแบบประเมินความพึงพอใจ ทั้งนี้ เพื่อเป็นการรักษามาตรฐานการให้บริการประชาชนของหน่วยงานรัฐ ในฐานะศูนย์ราชการสะดวก และนำข้อมูลที่ได้มาปรับปรุงพัฒนาการให้บริการของศูนย์ราชการสะดวกต่อไป',
          systemAssessmentsId: id.value,
          assessments: [
            {
              assessmentsName: 'ข้อมูลผู้รับบริการ',
              isRequest: true,
              subModels: [
                {
                  assessmentsName: 'เพศ',
                  isRequest: true,
                  isActive: true,
                  subModels: [
                    { assessmentsName: 'ชาย', isRequest: true, isActive: true },
                    {
                      assessmentsName: 'หญิง',
                      isRequest: true,
                      isActive: true
                    }
                  ]
                },
                {
                  assessmentsName: 'อายุ',
                  isRequest: true,
                  isActive: true,
                  subModels: [
                    {
                      assessmentsName: 'ต่ำกว่า 30 ปี',
                      isRequest: true,
                      isActive: true
                    },
                    {
                      assessmentsName: '30 - 50 ปี',
                      isRequest: true,
                      isActive: true
                    },
                    {
                      assessmentsName: '51 - 60 ปี',
                      isRequest: true,
                      isActive: true
                    },
                    {
                      assessmentsName: 'มากกว่า 60 ปี',
                      isRequest: true,
                      isActive: true
                    }
                  ]
                },
                {
                  assessmentsName: 'ระดับการศึกษาสูงสุด',
                  isRequest: true,
                  isActive: true,
                  subModels: [
                    {
                      assessmentsName: 'ต่ำกว่าหรือเท่ากับมัธยมศึกษาตอนปลาย',
                      isRequest: true,
                      isActive: true
                    },
                    {
                      assessmentsName: 'ปริญญาตรี',
                      isRequest: true,
                      isActive: true
                    },
                    {
                      assessmentsName: 'ปริญญาโท',
                      isRequest: true,
                      isActive: true
                    },
                    {
                      assessmentsName: 'สูงกว่าปริญญาโท',
                      isRequest: true,
                      isActive: true
                    }
                  ]
                },
                {
                  assessmentsName: 'ความถี่ในการรับบริการต่อเดือน',
                  isRequest: true,
                  isActive: true,
                  subModels: [
                    {
                      assessmentsName: '1 - 2 ครั้ง',
                      isRequest: true,
                      isActive: true
                    },
                    {
                      assessmentsName: '3 - 4 ครั้ง',
                      isRequest: true,
                      isActive: true
                    },
                    {
                      assessmentsName: '4 ครั้งขึ้นไป',
                      isRequest: true,
                      isActive: true
                    }
                  ]
                },
                {
                  assessmentsName: 'เป็นผู้ที่ต้องการความช่วยเหลือเป็นพิเศษเนื่องจาก',
                  isRequest: true,
                  isActive: true,
                  subModels: [
                    {
                      assessmentsName: 'สตรีมีครรภ์',
                      isRequest: true,
                      isActive: true
                    },
                    {
                      assessmentsName: 'ผู้สูงอายุ',
                      isRequest: true,
                      isActive: true
                    },
                    {
                      assessmentsName: 'พระภิกษุสงฆ์/สามเณร',
                      isRequest: true,
                      isActive: true
                    }
                  ]
                }
              ]
            },
            {
              assessmentsName: 'ระดับความพึงพอใจต่อการให้บริการ',
              isRequest: true,
              subModels: [
                {
                  assessmentsName: 'จุดให้บริการ ณ สำนักงาน/ที่ตั้งหน่วยงาน',
                  isRequest: true,
                  isActive: true,
                  subModels: [
                    {
                      assessmentsName: 'วันและเวลาเปิดให้บริการ',
                      isRequest: true,
                      isActive: true
                    },
                    {
                      assessmentsName: 'ความสะอาดของสถานที่ให้บริการ',
                      isRequest: true,
                      isActive: true
                    },
                    {
                      assessmentsName: 'การจัดสิ่งอำนวยความสะดวก',
                      isRequest: true,
                      isActive: true
                    },
                    {
                      assessmentsName: 'การจัดลำดับขั้นตอนการให้บริการ',
                      isRequest: true,
                      isActive: true
                    }
                  ]
                },
                {
                  assessmentsName: 'ช่องทางระบบออนไลน์ของหน่วยงาน',
                  isRequest: true,
                  isActive: true,
                  subModels: [
                    {
                      assessmentsName: 'การใช้งานสะดวก รวดเร็ว เข้าถึงบริการได้ง่าย',
                      isRequest: true,
                      isActive: true
                    },
                    {
                      assessmentsName: 'ครอบคลุมงานบริการของหน่วยงาน',
                      isRequest: true,
                      isActive: true
                    },
                    {
                      assessmentsName: 'การตอบกลับ/การแจ้งผลการดำเนินการให้ทราบ',
                      isRequest: true,
                      isActive: true
                    }
                  ]
                },
                {
                  assessmentsName: 'ช่องทางออนไลน์ที่ท่านใช้บริการมากที่สุดและมีประสิทธิภาพสูงสุด',
                  isRequest: true,
                  isActive: true,
                  subModels: [
                    {
                      assessmentsName: 'เว็บไซต์',
                      isRequest: true,
                      isActive: true
                    },
                    {
                      assessmentsName: 'Line Official',
                      isRequest: true,
                      isActive: true
                    },
                    {
                      assessmentsName: 'Application ของหน่วยงาน',
                      isRequest: true,
                      isActive: true
                    }
                  ]
                }
              ]
            },
            {
              assessmentsName: 'ภาพรวมการให้บริการ',
              isRequest: true,
              subModels: [
                {
                  assessmentsName: 'โปรดให้คะแนนการให้บริการของหน่วยงานของรัฐ ตั้งแต่ 1 - 5 คะแนน',
                  isRequest: false,
                  isActive: true,
                  subModels: []
                }
              ]
            }
          ]
        }

        const postResponse = await callAxios.RequestPostNoAlert(
          '/UserManagement/AddAssessments',
          defaultPayload
        )

        if (postResponse.status === 200)
          GetList() // เรียกซ้ำเพื่อโหลดข้อมูลใหม่
        else Swal.isNotFound()
      } else {
        // กำหนดค่าถ้าข้อมูลไม่ว่าง
        form.systemAssessmentsId = responseData.systemAssessmentsId
        form.description = responseData.detail
        assessmentsData.value = responseData.assessments
      }
    } else {
      Swal.isNotFound()
    }
  } catch (error) {
    Swal.callCatch()
  } finally {
    inProcess.value = false
  }
}

const UpdateForm = async () => {
  const confirmed = await Swal.ApproveConfirm()
  if (!confirmed) return

  try {
    const payload = {
      detail: form.description,
      systemAssessmentsId: form.systemAssessmentsId,
      assessments: assessmentsData.value.map((assessment, index) => ({
        // no: assessment.no || index + 1,
        assessmentsId: assessment.assessmentsId || null,
        assessmentsName: assessment.assessmentsName || '',
        isRequest: assessment.isRequest || false,
        subModels: assessment.subModels.map((subModel, subIndex) => ({
          // no: subModel.no || `${index + 1}.${subIndex + 1}`,
          assessmentsId: subModel.assessmentsId || null,
          assessmentsName: subModel.assessmentsName || '',
          isRequest: subModel.isRequest || false,
          isActive: subModel.isActive,
          subModels: subModel.subModels.map((nestedSubModel, nestedIndex) => ({
            // no:
            //   nestedSubModel.no ||
            //   `${nestedIndex + 1}`,
            assessmentsId: nestedSubModel.assessmentsId || null,
            assessmentsName: nestedSubModel.assessmentsName || '',
            isRequest: nestedSubModel.isRequest || false,
            isActive: nestedSubModel.isActive
          }))
        }))
      }))
    }

    const response = await callAxios.RequestPut(
      `/UserManagement/UpdateAssessments?SystemAssessmentsId=${form.systemAssessmentsId}`,
      payload
    )

    if (response.status === 200) {
      Swal.AddSuccess()
      GetList()
      router.go(-1)
    } else {
      Swal.AddFail()
    }
  } catch (error) {
    Swal.callCatch()
  }
}

const handleDialogUpdate = ({ type, data }) => {
  if (type === 'add') {
    const parentIndex = assessmentsData.value.findIndex(a =>
      a.subModels.some(sm => sm.assessmentsId === data.parentAssessmentsId)
    )

    if (parentIndex !== -1) {
      const subIndex = assessmentsData.value[parentIndex].subModels.findIndex(
        sm => sm.assessmentsId === data.parentAssessmentsId
      )

      if (subIndex !== -1) {
        assessmentsData.value[parentIndex].subModels[subIndex].subModels.push({
          ...data,
          assessmentsId: data.assessmentsId || `temp-${Date.now()}` // Assign temp ID if missing
        })
        console.log('Data Added:', data)
      }
    }
  } else if (type === 'edit') {
    let found = false

    // Update parent level
    const parentIndex = assessmentsData.value.findIndex(
      a =>
        a.assessmentsId === data.assessmentsId || // Match by ID
        a.assessmentsName === data.parentAssessmentsId // Match by Name
    )

    if (parentIndex !== -1) {
      assessmentsData.value[parentIndex] = {
        ...assessmentsData.value[parentIndex],
        assessmentsName: data.assessmentsName,
        isActive: data.isActive
      }
      console.log('Updated Parent Model:', assessmentsData.value[parentIndex])
      found = true
    }

    // Update subModels level
    if (!found) {
      assessmentsData.value.forEach(parent => {
        const subIndex = parent.subModels.findIndex(
          sub =>
            sub.assessmentsId === data.assessmentsId || // Match by ID
            sub.assessmentsName === data.parentAssessmentsId // Match by Name
        )

        if (subIndex !== -1) {
          parent.subModels[subIndex] = {
            ...parent.subModels[subIndex],
            assessmentsName: data.assessmentsName, // Ensure name is updated
            isActive: data.isActive // Ensure status is updated
          }
          console.log('Updated SubModel:', parent.subModels[subIndex])
          found = true
        }

        // Update nested subModels level
        if (!found) {
          parent.subModels.forEach(sub => {
            const nestedIndex = sub.subModels.findIndex(
              nsm =>
                nsm.assessmentsId === data.assessmentsId || // Match by ID
                nsm.assessmentsName === data.parentAssessmentsId // Match by Name
            )

            if (nestedIndex !== -1) {
              sub.subModels[nestedIndex] = {
                ...sub.subModels[nestedIndex],
                assessmentsName: data.assessmentsName, // Ensure name is updated
                isActive: data.isActive // Ensure status is updated
              }
              console.log('Updated Nested SubModel:', sub.subModels[nestedIndex])
              found = true
            }
          })
        }
      })
    }

    // Debugging log if no match found
    if (!found) console.warn('No matching submodel found for update:', data)
  }
}

// const handleDialogUpdate = ({ type, data }) => {
//   if (type === "add") {
//     const parentIndex = assessmentsData.value.findIndex((a) =>
//       a.subModels.some((sm) => sm.assessmentsId === data.parentAssessmentsId)
//     );

//     if (parentIndex !== -1) {
//       const subIndex = assessmentsData.value[parentIndex].subModels.findIndex(
//         (sm) => sm.assessmentsId === data.parentAssessmentsId
//       );

//       if (subIndex !== -1) {
//         assessmentsData.value[parentIndex].subModels[subIndex].subModels.push(
//           data
//         );
//       }
//     }
//   } else if (type === "edit") {
//     let found = false;

//     // Loop through each parent model
//     assessmentsData.value.forEach((parent) => {
//       // Find the submodel to edit
//       const subIndex = parent.subModels.findIndex(
//         (sm) =>
//           sm.assessmentsId === data.assessmentsId || // Match by ID
//           sm.assessmentsName === data.assessmentsName // Match by name
//       );

//       // Update if found
//       if (subIndex !== -1) {
//         parent.subModels[subIndex] = { ...parent.subModels[subIndex], ...data };
//         found = true;
//       }

//       // Check nested submodels if not found at the current level
//       if (!found) {
//         parent.subModels.forEach((sub) => {
//           const nestedIndex = sub.subModels.findIndex(
//             (nsm) =>
//               nsm.assessmentsId === data.assessmentsId || // Match by ID
//               nsm.assessmentsName === data.assessmentsName // Match by name
//           );

//           if (nestedIndex !== -1) {
//             sub.subModels[nestedIndex] = {
//               ...sub.subModels[nestedIndex],
//               ...data,
//             };
//             found = true;
//           }
//         });
//       }
//     });
//   }
// };

// Form Submission
const onFormSubmit = async () => {
  if (!refVForm.value) return
  const { valid } = await refVForm.value.validate()
  if (valid) await UpdateForm()
}

const formDefault = structuredClone(toRaw(form))

const resetForm = () => {
  Object.assign(form, structuredClone(formDefault))
}

const goBack = async () => {
  const response = await Swal.ApproveCancel()
  if (response) resetForm()

  router.go(-1)
}

// Delete Item
const handleDelete = async (assessmentsId: string) => {
  const confirmed = await Swal.ApproveConfirm()
  if (!confirmed) return
  try {
    const parentIndex = assessmentsData.value.findIndex(
      parent => parent.assessmentsId === assessmentsId
    )

    if (parentIndex !== -1) {
      assessmentsData.value.splice(parentIndex, 1)
      console.log('Deleted Parent Item:', assessmentsId)

      return
    }

    // ลบรายการในระดับ SubModel
    assessmentsData.value.forEach(parent => {
      const subIndex = parent.subModels.findIndex(sub => sub.assessmentsId === assessmentsId)

      if (subIndex !== -1) {
        parent.subModels.splice(subIndex, 1)
        console.log('Deleted SubModel Item:', assessmentsId)

        return
      }

      // ลบรายการในระดับ Nested SubModel
      parent.subModels.forEach(sub => {
        const nestedIndex = sub.subModels.findIndex(
          nestedSub => nestedSub.assessmentsId === assessmentsId
        )

        if (nestedIndex !== -1) {
          sub.subModels.splice(nestedIndex, 1)
          console.log('Deleted Nested SubModel Item:', assessmentsId)
        }
      })
    })
  } catch (error) {
    console.error(error)
  }
}

const openAddDialog = (parentAssessmentsId: string) => {
  isAdd.value = true
  isEdit.value = false
  isView.value = false
  editId.value = parentAssessmentsId
  isDialogVisible.value = true
}

const openEditDialog = (identifier: { assessmentsId?: string; assessmentsName: string }) => {
  isEdit.value = true
  isView.value = false
  isAdd.value = false

  // if (identifier.assessmentsId) {
  //   editId.value = identifier.assessmentsId;
  // } else {
  //   editId.value = identifier.assessmentsName;
  // }
  if (identifier.assessmentsName) editId.value = identifier.assessmentsName

  isDialogVisible.value = true
}

// const openEditDialog = (assessmentsName: string) => {
//   isEdit.value = true;
//   isView.value = false;
//   isAdd.value = false;
//   isDialogVisible.value = true;
//   editId.value = assessmentsName;
// };

// Mount Fetch Data
onMounted(() => {
  GetList()
})
</script>

<template>
  <div>
    <!-- Breadcrumb -->
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <!-- Form -->
    <VForm ref="refVForm" @submit.prevent="onFormSubmit">
      <!-- Main Card -->
      <VCard class="px-10">
        <VCardText>
          <div class="d-flex align-center justify-center">
            <h2 class="font-size-large">
              แบบสำรวจความคิดเห็นของผู้รับบริการจากหน่วยงานของรัฐที่ได้รับการรับรองมาตรฐานการให้บริการของศูนย์ราชการสะดวก
            </h2>
          </div>
        </VCardText>

        <!-- Textarea Section -->
        <VRow>
          <VCol cols="12" md="12">
            <span>คำอธิบายแบบประเมิน :</span>
          </VCol>
        </VRow>
        <VRow class="mt-0 justify-center">
          <VCol cols="12" md="12">
            <VTextarea
              v-model="form.description"
              :counter="1000"
              :maxlength="1000"
              placeholder="กรอกคําอธิบาย"
              variant="outlined"
            />
          </VCol>
        </VRow>

        <!-- Form Fields -->

        <div v-for="(assessment, index) in assessmentsData" :key="assessment.assessmentsId">
          <!-- ตอนที่ -->
          <VRow class="mt-5">
            <VCol cols="12" md="1" class="d-flex justify-end align-center">
              <div>
                <label>
                  ตอนที่ :
                  <small class="text-error">*</small>
                </label>
              </div>
            </VCol>

            <VCol cols="12" md="1">
              <VTextField v-model="assessment.no" readonly />
            </VCol>
            <VCol cols="12" md="7">
              <VTextField
                v-model="assessment.assessmentsName"
                :counter="100"
                :maxlength="100"
                outlined
              />
            </VCol>
            <VCol cols="12" md="3" />
          </VRow>

          <!-- Question Section -->
          <VCard class="mb-5" color="warmSand">
            <template v-if="assessment.no === 3">
              <AppDataTableAPI
                :columns="listFields"
                :value="assessment.subModels"
                :header-no="true"
                :paginator="false"
              >
                <template #no="slotProps">
                  {{ slotProps.index + 1 }}
                </template>
                <template #isActive="slotProps">
                  <div class="text-center">
                    <ChipStatus
                      :status="slotProps.data.isActive === true ? 'ใช้งาน' : 'ไม่ใช้งาน'"
                      :label="slotProps.data.isActive === true ? 'ใช้งาน' : ''"
                    />
                  </div>
                </template>
                <template #options="slotProps">
                  <div class="text-center">
                    <!-- <h4>{{ slotProps.data.assessmentsId }}</h4> -->
                    <IconBtn
                      @click="
                        openEditDialog({
                          assessmentsId: slotProps.data.assessmentsId,
                          assessmentsName: slotProps.data.assessmentsName
                        })
                      "
                    >
                      <VIcon icon="mdi-pencil-outline" />
                      <VTooltip open-delay="500" location="top" activator="parent">
                        แก้ไขชื่อแบบฟอร์ม/สถานะ
                      </VTooltip>
                    </IconBtn>
                  </div>
                </template>
              </AppDataTableAPI>
            </template>
            <template v-else>
              <div
                v-for="(subModel, subIndex) in assessment.subModels"
                :key="subModel.assessmentsId"
              >
                <div
                  v-if="
                    subModel.assessmentsName ===
                    'ช่องทางออนไลน์ที่ท่านใช้บริการมากที่สุดและมีประสิทธิภาพสูงสุด'
                  "
                >
                  <VRow class="my-5 mx-5">
                    <VCol cols="12" md="2" class="d-flex justify-end align-center">
                      <label>
                        คำถามเลือกตอบ
                        <small class="text-error">*</small>
                      </label>
                    </VCol>
                    <VCol cols="12" md="6">
                      <VTextField v-model="subModel.assessmentsName" readonly />
                    </VCol>
                    <VCol cols="12" md="4">
                      <VCheckbox v-model="subModel.isRequest" label="จำเป็นต้องกรอก" />
                    </VCol>
                  </VRow>
                </div>

                <div v-else>
                  <VRow class="my-5 mx-5">
                    <VCol cols="2" class="d-flex justify-end align-center">
                      <label>
                        คำถามที่
                        <small class="text-error">*</small>
                      </label>
                    </VCol>
                    <VCol cols="2">
                      <VTextField v-model="subModel.no" readonly />
                    </VCol>
                    <VCol cols="6">
                      <VTextField
                        v-model="subModel.assessmentsName"
                        :counter="100"
                        :maxlength="100"
                      />
                    </VCol>
                    <VCol cols="2">
                      <VCheckbox v-model="subModel.isRequest" label="จำเป็นต้องกรอก" />
                    </VCol>
                  </VRow>
                </div>

                <VRow class="my-5 mx-5">
                  <VCol cols="12" md="12">
                    <VCol cols="12" md="12">
                      <VBtn
                        class="mt-2"
                        prepend-icon="mdi-plus-box-multiple"
                        color="btn-add"
                        rounded="xl"
                        @click="openAddDialog(subModel.assessmentsId)"
                      >
                        เพิ่มรายการ
                      </VBtn>
                    </VCol>

                    <!-- Data Table -->
                    <AppDataTableAPI
                      :columns="listFields"
                      :value="subModel.subModels"
                      :header-no="true"
                      :paginator="false"
                    >
                      <template #isActive="slotProps">
                        <ChipStatus
                          :status="slotProps.data.isActive === true ? 'ใช้งาน' : 'ไม่ใช้งาน'"
                          :label="slotProps.data.isActive === true ? 'ใช้งาน' : ''"
                        />
                      </template>
                      <template #options="slotProps">
                        <div class="text-center">
                          <!-- <h4>{{ slotProps.data.assessmentsId }}</h4> -->
                          <IconBtn
                            @click="
                              openEditDialog({
                                assessmentsId: slotProps.data.assessmentsId,
                                assessmentsName: slotProps.data.assessmentsName
                              })
                            "
                          >
                            <VIcon icon="mdi-pencil-outline" />
                            <VTooltip open-delay="500" location="top" activator="parent">
                              แก้ไขชื่อแบบฟอร์ม/สถานะ
                            </VTooltip>
                          </IconBtn>

                          <IconBtn @click="handleDelete(slotProps.data.assessmentsId)">
                            <VIcon icon="mdi-delete-outline" />
                            <VTooltip open-delay="500" location="top" activator="parent">
                              ลบข้อมูล
                            </VTooltip>
                          </IconBtn>
                        </div>
                      </template>
                    </AppDataTableAPI>
                  </VCol>
                </VRow>
              </div>
            </template>
          </VCard>
        </div>
      </VCard>
      <div class="d-flex justify-end mt-5">
        <div class="demo-space-x">
          <VBtn type="submit" color="blue-600" rounded="xl" prepend-icon="mdi-content-save">
            บันทึก
          </VBtn>
          <VBtn color="error-300" rounded="xl" prepend-icon="mdi-close-circle" @click="goBack">
            ยกเลิก
          </VBtn>
        </div>
      </div>
    </VForm>
    <DialogEditSurveyForm
      v-model="isDialogVisible"
      :edit-id="editId"
      :is-add="isAdd"
      :is-edit="isEdit"
      :is-view="isView"
      :view-id="editId"
      :assessments-data="assessmentsData"
      @update="handleDialogUpdate"
    />
  </div>
</template>

<style scoped>
.no-select {
  user-select: none;
}

.text-right {
  text-align: end;
}
</style>
