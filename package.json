{"name": "materialize-vuejs-admin-template", "version": "1.1.1", "private": true, "scripts": {"dev": "vite", "build": "vite build", "build:prod": "vite build --mode production", "build:dev": "vite build --mode development", "preview": "vite preview --port 5050", "typecheck": "vue-tsc --noEmit", "lint": "eslint . -c .eslintrc.js --fix --rulesdir eslint-internal-rules/ --ext .ts,.js,.vue,.tsx,.jsx", "build:icons": "tsc -b src/@iconify && node src/@iconify/build-icons.js", "postinstall": "npm run build:icons"}, "dependencies": {"@casl/ability": "6.5.0", "@casl/vue": "2.2.1", "@ckeditor/ckeditor5-vue": "^7.0.0", "@floating-ui/dom": "1.2.8", "@vitejs/plugin-vue": "^5.0.5", "@vitejs/plugin-vue-jsx": "^4.0.0", "@vuepic/vue-datepicker": "^11.0.2", "@vueuse/core": "10.1.2", "@vueuse/math": "10.1.2", "apexcharts-clevision": "3.28.5", "axios": "^1.7.2", "axios-mock-adapter": "1.21.4", "chart.js": "4.3.0", "ckeditor5": "43.2.0", "file-saver": "^2.0.5", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.1", "jszip": "^3.10.1", "jwt-decode": "3.1.2", "leaflet": "^1.9.4", "pinia": "2.1.3", "powerbi-client-vue-js": "^1.0.3", "primevue": "^3.36.0", "prismjs": "1.29.0", "punycode": "^2.3.1", "sass": "1.62.1", "sweetalert2": "^11.12.1", "swiper": "^11.0.5", "unplugin-vue-define-options": "^3.0.0-beta.14", "v-slick-carousel": "^0.4.0", "vue": "^3.4.21", "vue-chartjs": "^5.3.1", "vue-flatpickr-component": "11.0.3", "vue-i18n": "9.2.2", "vue-prism-component": "2.0.0", "vue-qrcode": "^2.2.0", "vue-recaptcha-v3": "^2.0.1", "vue-router": "4.4.0", "vue3-apexcharts": "^1.5.3", "vue3-google-map": "^0.21.0", "vue3-perfect-scrollbar": "1.6.1", "vuedraggable": "^4.1.0", "vuetify": "^3.8.10", "webfontloader": "1.6.28", "xlsx": "^0.18.5"}, "devDependencies": {"@antfu/eslint-config-vue": "0.39.1", "@fullcalendar/core": "6.1.7", "@fullcalendar/daygrid": "6.1.7", "@fullcalendar/interaction": "6.1.7", "@fullcalendar/list": "6.1.7", "@fullcalendar/timegrid": "6.1.7", "@fullcalendar/vue3": "6.1.7", "@iconify-json/mdi": "1.1.52", "@iconify/tools": "2.2.6", "@iconify/vue": "4.1.1", "@intlify/unplugin-vue-i18n": "0.10.0", "@types/node": "20.2.3", "@types/webfontloader": "1.6.35", "@typescript-eslint/eslint-plugin": "5.59.6", "@typescript-eslint/parser": "5.59.6", "eslint": "8.41.0", "eslint-config-airbnb-base": "15.0.0", "eslint-import-resolver-typescript": "3.5.5", "eslint-plugin-import": "2.27.5", "eslint-plugin-promise": "6.1.1", "eslint-plugin-regex": "1.10.0", "eslint-plugin-sonarjs": "0.19.0", "eslint-plugin-unicorn": "47.0.0", "eslint-plugin-vue": "9.13.0", "postcss-html": "1.5.0", "stylelint": "14.15.0", "stylelint-config-idiomatic-order": "9.0.0", "stylelint-config-standard-scss": "6.1.0", "stylelint-use-logical-spec": "4.1.0", "type-fest": "3.11.0", "typescript": "5.0.4", "unplugin-auto-import": "0.16.1", "unplugin-vue-components": "0.24.1", "vite": "^5.3.2", "vite-plugin-pages": "^0.32.3", "vite-plugin-vue-layouts": "^0.11.0", "vite-plugin-vuetify": "^2.0.3", "vue-shepherd": "3.0.0", "vue-tsc": "1.6.5"}, "packageManager": "yarn@1.22.18", "resolutions": {"postcss": "8", "primevue": "^3.36.0", "vue": "3.3.4", "vite": "^5.3.2", "@vitejs/plugin-vue": "^5.0.0", "@vitejs/plugin-vue-jsx": "^4.0.0", "vue3-perfect-scrollbar": "^1.6.1"}}