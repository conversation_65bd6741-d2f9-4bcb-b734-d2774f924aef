<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import { VBreadcrumbs, VBtn, VCardTitle } from 'vuetify/lib/components/index.mjs'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'

const route = useRoute()
const router = useRouter()
const callAxios = useAxios()
const Swal = useSwal()

const govermentId = ref(route.query.id)
const startDate = ref(route.query.startDate)
const endDate = ref(route.query.endDate)

const breadcrumbItems = [
  {
    title: 'รายงาน',
    disable: false,
    to: '/apps/report/menu'
  },
  {
    title: 'รายงานสรุปผลการประเมินความพึงพอใจ',
    disabled: false,
    to: '/apps/report/SatisfactionEvaluationSummary'
  },
  {
    title: 'รายงานผลการประเมินความพึงพอใจ',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const tooltipTitle = 'ค่าระดับความพึงพอใจ'

const tooltipContent = `
- 4.51 - 5.00 มากที่สุด <br />
- 3.51 - 4.50 มาก <br />
- 2.51 - 3.50 ปานกลาง <br />
- 1.51 - 2.50 น้อย <br />
- 0.00 - 1.50 น้อยที่สุด
`

const assessmentData = reactive({
  title: '',
  govermentName: '',
  provinceName: '',
  startDate: '',
  endDate: '',
  firstState: [],
  secondState: [],
  summaryState: [],
  thirdState: []
})

const listFieldStation = [
  {
    field: 'title',
    header: 'หัวข้อ',
    sortable: false,
    style: { textAlign: 'left' }
  },
  {
    field: 'score',
    header: 'ระดับความพึงพอใจ',
    sortable: false,
    style: { textAlign: 'center' }
  }
]

const listFieldApplicationOnline = [
  {
    field: 'heading',
    header: 'หัวข้อ',
    sortable: false,
    style: { textAlign: 'left' }
  },
  {
    field: 'statusSatisfaction',
    header: 'ระดับความพึงพอใจ',
    sortable: false,
    style: { textAlign: 'center' }
  }
]

const listFieldPeopleOnline = [
  {
    field: 'title',
    header: 'หัวข้อ',
    sortable: false,
    style: { textAlign: 'left' }
  },
  {
    field: 'score',
    header: 'จำนวน (คน)',
    sortable: false,
    style: { textAlign: 'center' }
  }
]

const listFieldSuggestions = [
  {
    field: 'title',
    header: 'หัวข้อ',
    sortable: false,
    style: { textAlign: 'left' }
  }
]

// value
const ListItemSuggestions = ref([
  {
    heading: 'เจ้าหน้าที่พูดจาเพราะมากค่ะ'
  },
  {
    heading: 'ห้องน้ำสะอาดมากครับ'
  },
  {
    heading: 'เจ้าหน้าที่เก็บเอกสารของลูกค้าได้ดีเยี่ยม'
  },
  {
    heading: 'เจ้าหน้าที่ดูแลดีมากๆค่ะ'
  }
])

const Print = () => {
  const routeData = router.resolve({
    name: 'apps-Report-SatisfactionEvaluationSummary-ViewReport',
    query: {
      id: govermentId.value,
      startDate: startDate.value,
      endDate: endDate.value
    }
  })

  window.open(routeData.href, '_blank')
}

const GetList = async () => {
  try {
    const response = await callAxios.RequestGet(
      `Report/ReadReportSatisfaction?Goverment=${govermentId.value}&StartDate=${startDate.value}&EndDate=${endDate.value}`
    )

    if (response.status === 200) {
      const {
        title,
        govermentName,
        provinceName,
        firstState,
        startDate,
        endDate,
        secondState,
        summaryState,
        thirdState
      } = response.data.response || {}

      assessmentData.title = title
      assessmentData.govermentName = govermentName ?? ''
      assessmentData.provinceName = provinceName ?? ''
      assessmentData.startDate = startDate ?? ''
      assessmentData.endDate = endDate ?? ''
      assessmentData.firstState = firstState ?? []
      assessmentData.secondState = secondState ?? []
      assessmentData.summaryState = summaryState ?? []
      assessmentData.thirdState = thirdState ?? []
    }
  } catch (error) {
    Swal.callCatch()
  }
}

onMounted(async () => {
  await GetList()
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <VCard>
      <div class="d-flex">
        <VCardTitle class="mt-2">
          รายงานผลการประเมินความพึงพอใจของผู้รับบริการจากหน่วยงานของรัฐ
          <br />
          ที่ได้รับการรับรองมาตรฐานการให้บริการของศูนย์ราชการสะดวกผ่านระบบ Gsurvey
          สำนักงานปลัดสำนักนายกรัฐมนตร
        </VCardTitle>
        <VRow>
          <VCol class="d-flex justify-end mt-4 me-4">
            <VBtn rounded="xl" prepend-icon="mdi-printer" color="blue-600" @click="Print">
              พิมพ์
            </VBtn>
          </VCol>
        </VRow>
      </div>

      <VCardText>
        ช่วงวันที่ : {{ assessmentData.startDate }} ถึงวันที่ : {{ assessmentData.endDate }}
        <br />
        ชื่อศูนย์ราชการสะดวก : {{ assessmentData.govermentName }}
      </VCardText>
    </VCard>
    <VCard class="mt-4">
      <VCardTitle class="text-primary">
        {{ assessmentData.firstState.title }}
      </VCardTitle>
      <div class="d-flex align-center gap-3">
        <ul class="report-list">
          <li>
            {{ `จำนวนผู้ประเมิน : ${assessmentData.firstState.total} คน ผลการประเมิน :` }}
          </li>
        </ul>
        <VBtn rounded="xl" color="background">
          <img src="@images/emojiSmile.png" class="me-2" />
          พึงพอใจ : {{ assessmentData.firstState.good }}
        </VBtn>
        <VBtn rounded="xl" color="background">
          <img src="@images/emojiCry.png" class="me-2" />
          ไม่พึงพอใจ : {{ assessmentData.firstState.bad }}
        </VBtn>
      </div>
      <div class="d-flex align-center gap-3">
        <ul class="report-list">
          <li>สรุปผลการประเมิน :</li>
        </ul>
        <template v-if="assessmentData.firstState.good >= assessmentData.firstState.bad">
          <VBtn rounded="xl" color="background">
            <img src="@images/emojiSmile.png" class="me-2" />
            พึงพอใจ
          </VBtn>
        </template>
        <template v-else>
          <VBtn rounded="xl" color="background">
            <img src="@images/emojiCry.png" class="me-2" />
            ไม่พึงพอใจ
          </VBtn>
        </template>
      </div>

      <VCardTitle class="text-primary">
        {{ assessmentData.secondState.title }}
      </VCardTitle>
      <div v-for="(mainSub, mainIndex) in assessmentData.secondState.total" :key="mainIndex">
        <div class="d-flex align-center gap-3">
          <ul class="report-list">
            <div class="d-flex align-center gap-2">
              <img src="@images/playSatic.png" class="icon" />
              <span>{{ mainSub.title }}</span>
            </div>
            <div class="d-flex align-center ms-5">
              <span>จำนวนผู้ประเมินทั้งหมด : 100 คน</span>
            </div>
          </ul>
        </div>
        <div v-for="(subItem, subIndex) in mainSub.sub" :key="subIndex">
          <ul class="report-list">
            <div class="d-flex align-center ms-5">
              <span>{{ subItem.title }} :</span>
            </div>
          </ul>
          <template v-if="mainIndex != 0">
            <VCard class="mt-4 ms-4 me-4 mb-4">
              <AppDataTableAPI
                :columns="
                  subItem.title === 'ช่องทางออนไลน์ที่ท่านใช้บริการมากที่สุดและมีประสิทธิภาพสูงสุด'
                    ? listFieldPeopleOnline
                    : listFieldStation
                "
                :value="subItem.sub"
                :paginator="false"
                :scrollable="true"
              />
            </VCard>
            <template
              v-if="
                subItem.title === 'ช่องทางออนไลน์ที่ท่านใช้บริการมากที่สุดและมีประสิทธิภาพสูงสุด'
              "
            >
              <ul class="report-list">
                <ul class="nested-list">
                  <li>ช่องทางอื่น ๆ -ไม่มี-</li>
                </ul>
              </ul>
            </template>
          </template>
          <template v-else>
            <ul class="report-list">
              <ul class="nested-list">
                <li
                  v-for="(subSubItem, subSubIndex) in subItem.sub"
                  :key="subSubIndex"
                  class="ms-5"
                >
                  {{ subSubItem.title }}: {{ subSubItem.score ?? 'N/A' }} คน
                </li>
              </ul>
            </ul>
          </template>
        </div>
      </div>

      <VCard class="mt-6 ms-6 me-6 mb-6 card-container">
        <VRow>
          <VCol cols="12" class="text-center">
            <div class="d-flex align-center justify-center">
              <VCardTitle>{{ assessmentData.summaryState.title }}</VCardTitle>
              <TooltipFormDialog :title="tooltipTitle" :content="tooltipContent" />
            </div>
            <VCardTitle>{{ assessmentData.summaryState.score }}</VCardTitle>
            <VCardTitle>ผล : {{ assessmentData.summaryState.result }}</VCardTitle>
          </VCol>
        </VRow>
      </VCard>

      <VCardTitle class="text-primary mt-4">
        {{ assessmentData.thirdState.title }}
      </VCardTitle>
      <h4 class="report-list">
        {{ assessmentData.thirdState.total }}
      </h4>
      <div v-for="(mainSub, mainIndex) in assessmentData.thirdState.total" :key="mainIndex">
        <VCard class="mt-4 ms-4 me-4 mb-4">
          <AppDataTableAPI
            :columns="listFieldSuggestions"
            :value="mainSub"
            :paginator="true"
            :scrollable="false"
          />
        </VCard>
      </div>
    </VCard>

    <div class="d-flex align-center justify-end flex-wrap gap-4 mt-4 me-4">
      <BtnGoBack />
    </div>
  </div>
</template>

<style scoped>
.card-container {
  position: relative;
  overflow: visible;
  border-radius: 35px;
}

.title {
  font-size: 1rem; /* ปรับขนาดตัวอักษร */
  font-weight: bold; /* เน้นข้อความให้ชัด */
  line-height: 1.25rem; /* จัดข้อความให้ตรงกลางในแนวเดียวกับไอคอน */
  margin: 0; /* ลบ margin ที่อาจจะมาจาก li */
}

.icon {
  width: 0.625rem; /* ขนาดของไอคอน */
  height: 0.625rem; /* ความสูงของไอคอน */
  object-fit: contain; /* รักษาอัตราส่วนของรูป */
}

.report-list {
  margin-left: 0; /* ไม่ต้องการระยะจาก margin เริ่มต้น */
  font-size: 1rem; /* ขนาดตัวอักษร */
  line-height: 1.6; /* ระยะห่างระหว่างบรรทัด */
  list-style: none; /* ซ่อนจุด bullet */
  padding-left: 3.125rem; /* ยกเลิกระยะ padding ด้านซ้าย */
}

.report-list .title {
  font-weight: bold; /* เน้นข้อความหัวข้อ */
  color: #000000; /* สีฟ้าสำหรับหัวข้อ */
}

.nested-list {
  list-style: none; /* ซ่อนจุด bullet */
  margin-left: 1.25rem; /* เพิ่มระยะห่างจากหัวข้อ */
  padding-left: 0; /* ยกเลิก padding */
}

.nested-list li {
  margin-bottom: 0.3125rem; /* เว้นช่องว่างระหว่างรายการ */
}
</style>
