<script setup>
const breadcrumbItems = [
  {
    title: 'ตั้งค่า',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'จัดการรายชื่อเจ้าหน้าที่',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const filter = ref({
  search1: null,
  search2: null,
  searchQuery: null
})

const listFilter = reactive([
  {
    name: 'searchQuery',
    type: 'text',
    label: 'ค้นหา'
  }
])

const listFields = reactive([
  {
    field: 'listFields1',
    header: 'วันที่บันทึก'
  },
  {
    field: 'listFields2',
    header: 'จังหวัด',
    sortable: true
  },
  {
    field: 'listFields3',
    header: 'หน่วยงาน',
    sortable: true
  },
  {
    field: 'listFields4',
    header: 'เจ้าหน้าที่',
    sortable: true
  },
  {
    field: 'options',
    header: 'การจัดการ'
  }
])

const listItems = ref([
  {
    listFields1: '18/07/2566',
    listFields2: 'ฉะเชิงเทรา',
    listFields3: 'ศูนย์บริการประชาชนกระทรวงพาณิชย์จังหวัดฉะเชิงเทรา',
    listFields4: '15',
    contractorId: 0
  },
  {
    listFields1: '18/07/2566',
    listFields2: 'สมุทรปราการ',
    listFields3: 'ศูนย์บริการประชาชนกระทรวงพาณิชย์จังหวัดสมุทรปราการ',
    listFields4: '10',
    contractorId: 0
  }
])

const listFieldsIn = reactive([
  {
    field: 'listFields1',
    header: 'ชื่อ - นามสกุล'
  },

  {
    field: 'options',
    header: 'การจัดการ'
  }
])

const nameStaff = ref(null)

const addName = value => {
  listItemsIn.value.push({ listFields1: value })
}

const deleteName = value => {
  // console.log(value)
  listItemsIn.value.splice(value, 1)
}

const listItemsIn = ref([])

const isDialogVisible01 = ref(false)

const onFormReset = () => {
  isDialogVisible01.value = false

  form.value = structuredClone(toRaw(formRaw.value))
}

function onSearch(values) {
  alert(JSON.stringify(values, null, 2))
}
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <!-- {{ filter }} -->

    <!-- <Filters :fields="listFilter" :modelValue="filter" /> -->
    <!--
      <VCard title="แสดงภาพตัวอย่างโลโก้ที่ใช้งานปัจจุบัน" class="mb-5">
      <VCardText> </VCardText>
      </VCard>
    -->

    <VCard>
      <VCardText>
        <div class="d-flex align-center flex-wrap gap-4">
          <VBtn prepend-icon="mdi-plus" color="success-200" @click="isDialogVisible01 = true">
            เพิ่ม
          </VBtn>
        </div>
      </VCardText>
      <AppDataTable :filters="filter.searchQuery" :columns="listFields" :value="listItems">
        <template #options="slotProps">
          <div class="text-center">
            <IconBtn>
              <VIcon icon="mdi-pencil-outline" />
              <VTooltip open-delay="500" location="top" activator="parent">แก้ไข</VTooltip>
            </IconBtn>
            <IconBtn @click="">
              <VIcon icon="mdi-delete-outline" />
              <VTooltip open-delay="500" location="top" activator="parent">ลบข้อมูล</VTooltip>
            </IconBtn>
          </div>
        </template>
      </AppDataTable>
    </VCard>

    <VDialog
      v-model="isDialogVisible01"
      persistent
      class="v-dialog-lg"
      no-click-animation
      scrollable
    >
      <VCard :title="Edit ? 'แก้ไขข้อมูล' : 'เพิ่มรายชื่อเจ้าหน้าที่'">
        <!-- List -->
        <DialogCloseBtn variant="text" size="small" @click="onFormReset" />

        <VCardText>
          <VForm ref="refVForm" @submit.prevent="onFormSubmit">
            <VRow class="mb-2 mt-6">
              <VCol cols="12" md="3" class="text-lg-end">
                <label>
                  จังหวัด :
                  <small class="text-error">*</small>
                </label>
              </VCol>
              <VCol cols="12" md="9">
                <!-- 👉 outlined variant -->
                <VAutocomplete />
              </VCol>
            </VRow>

            <VRow class="mb-2" align="center">
              <VCol cols="12" md="3" class="text-lg-end">
                <label>
                  หน่วยงาน :
                  <small class="text-error">*</small>
                </label>
              </VCol>
              <VCol cols="12" md="9">
                <!-- 👉 outlined variant -->
                <VAutocomplete />
              </VCol>
            </VRow>

            <VRow class="mb-2" align="center">
              <VCol cols="12" md="3" class="text-lg-end">
                <label>
                  รายชื่อเจ้าหน้าที่ :
                  <small class="text-error">*</small>
                </label>
              </VCol>
              <VCol cols="12" md="9">
                <VRow class="mb-2" align="center">
                  <VCol cols="6" class="align-center px-2">
                    <VTextField v-model="nameStaff" />
                  </VCol>

                  <VCol cols="6" class="align-center px-2 mt-1">
                    <VBtn
                      class="mx-2"
                      color="success"
                      icon="mdi-plus"
                      @click="addName(nameStaff)"
                    />
                  </VCol>
                </VRow>

                <AppDataTable
                  :filters="filter.searchQuery"
                  :columns="listFieldsIn"
                  :value="listItemsIn"
                  :paginator="false"
                >
                  <template #listFields1="slotProps" />

                  <template #options="slotProps">
                    <div class="text-center">
                      <IconBtn @click="deleteName(slotProps.index)">
                        <VIcon icon="mdi-delete-outline" />
                        <VTooltip open-delay="500" location="top" activator="parent">
                          ลบข้อมูล
                        </VTooltip>
                      </IconBtn>
                    </div>
                  </template>
                </AppDataTable>
              </VCol>
            </VRow>

            <!-- 👉 ปุ่มต่างๆ -->
            <div class="d-flex flex-wrap justify-center mt-5">
              <div class="demo-space-x">
                <VBtn type="submit" color="blue-600">บันทึก</VBtn>
                <VBtn color="error-300" @click="onFormReset">ยกเลิก</VBtn>
              </div>
            </div>
          </VForm>
        </VCardText>
      </VCard>
    </VDialog>
  </div>
</template>
