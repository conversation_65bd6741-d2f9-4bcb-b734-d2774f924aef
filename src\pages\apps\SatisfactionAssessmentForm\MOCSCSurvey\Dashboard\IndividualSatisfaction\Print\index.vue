<script lang="ts" setup>
import { useRoute } from 'vue-router'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import LogoMOC from '@images/logos/Logo_MOC.png'
import { formatDateTH } from '@utils'

const route = useRoute()
const callAxios = useAxios()
const Swal = useSwal()
const personalInfo: Ref<any> = ref<any>({})

const queryName: Ref<string> = ref<string>(route.query.name)
interface BreadcrumbItem {
  title: string
  disabled: boolean
  to?: string
  active?: boolean
  activeClass?: string
}

const breadcrumbItems: Ref<BreadcrumbItem[]> = ref([
  {
    title: 'Dashboard',
    disabled: false
  },
  {
    title: 'สรุปความพึงพอใจรายบุคคล',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
])

interface Column {
  field: string
  header: string
  sortable: boolean
  style?: { textAlign: string }
}

const columns: Ref<Column[]> = ref<Column[]>([
  {
    field: 'name',
    header: 'รายการ',
    sortable: false
  },
  {
    field: 'number',
    header: 'จำนวน',
    sortable: false,
    style: { textAlign: 'center' }
  },
  {
    field: 'percent',
    header: 'เปอร์เซ็น',
    sortable: false,
    style: { textAlign: 'center' }
  }
])

interface dataDashboard {
  datasTable: any[]
  datas: {
    header: string
    byPercent: Object
  }
  summary: string
  summaryPercent: string
}

const dataDashboard: Ref<dataDashboard> = ref<dataDashboard>({
  datasTable: [],
  datas: {
    header: '',
    byPercent: {}
  },
  summary: '0',
  summaryPercent: '0'
})

interface recommendServiceData {
  answerName: string
  createdDateTime: Date
}

const recommendServiceData: Ref<recommendServiceData[]> = ref<recommendServiceData[]>([])

const GetReportSurveyCommentByPersonal = (id = 0) => {
  if (id !== null) {
    callAxios
      .RequestGet(
        `/ReportGECC/GetReportSurveyCommentByPersonal?PersonalInfoId=${id}&StartDate=${searchDate.value[0]}&EndDate=${searchDate.value[1]}&EvaluationSystemId=2`
      )
      .then(response => {
        if (response.status == 200) {
          if (
            typeof response.data.response === 'object' &&
            Object.keys(response.data.response).length === 0
          ) {
            // Swal.AddConditionFailText("ไม่พบข้อมูลของวันที่กำหนด");
          } else {
            recommendServiceData.value = response.data.response
            isLoad.value = false
          }
        }
      })
  } else {
    Swal.isNotFound()
  }
}

const GetReportSurveyByPersonal = (id = 0) => {
  if (id !== null) {
    callAxios
      .RequestGet(
        `/ReportGECC/GetReportSurveyByPersonal?PersonalInfoId=${id}&StartDate=${
          searchDate.value[0] ? searchDate.value[0] : null
        }&EndDate=${searchDate.value[1] ? searchDate.value[1] : null}&EvaluationSystemId=2`
      )
      .then(response => {
        if (response.status == 200 && Object.keys(response.data.response).length > 0) {
          if (
            typeof response.data.response === 'object' &&
            Object.keys(response.data.response).length === 0
          ) {
            Swal.AddConditionFailText('ไม่พบข้อมูลของวันที่กำหนด')
          } else {
            dataDashboard.value = response.data.response
            footer.value[0].columns[1].footer = dataDashboard.value.summary
            footer.value[0].columns[2].footer = dataDashboard.value.summaryPercent
            isLoad.value = false
          }
        }
      })
  } else {
    Swal.isNotFound()
  }
}

interface FooterColumn {
  footer: string
  style: { textAlign: string }
}

interface FooterItem {
  columns: FooterColumn[]
}

const footer: Ref<FooterItem[]> = ref<FooterItem[]>([
  {
    columns: [
      {
        footer: 'จำนวนรวม',
        style: { textAlign: 'center' }
      },
      {
        footer: '0',
        style: { textAlign: 'center' }
      },
      {
        footer: '0',
        style: { textAlign: 'center' }
      }
    ]
  }
])

const columns2: Ref<Column[]> = ref<Column[]>([
  {
    field: 'answerName',
    header: 'ข้อเสนอแนะในการให้บริการ',
    sortable: false
  },
  {
    field: 'createdDateTime',
    header: 'วันที่ตอบแบบสอบถาม',
    sortable: false,
    style: { textAlign: 'center' }
  }
])

const searchDate: Ref<any> = ref<any>(route.query.date.split(','))
const isLoad: Ref<boolean> = ref<boolean>(true)

interface listSectionDropDown {
  serviceId: number
  serviceName: string
}

const listSectionDropDown: Ref<listSectionDropDown[]> = ref<listSectionDropDown[]>([])

const searchGroup: Ref<number> = ref<number>(0)

const GetSectionDropDown = (id = 0) => {
  callAxios.RequestGet(`/ReportGECC/GetSectionDropDown?OrgStructureId=${id}`).then(response => {
    if (response.status == 200) listSectionDropDown.value = response.data.response
    listSectionDropDown.value.splice(0, 0, {
      serviceId: 0,
      serviceName: 'ทั้งหมด'
    })
  })
}

const listPersonalByOrgStructureIdServiceId: Ref<any[]> = ref<any[]>([])
const searchName: Ref<number | null> = ref<number | null>(null)

const GetPersonalByOrgStructureIdServiceId = (org = 0, division = 0) => {
  callAxios
    .RequestGet(
      `/ReportGECC/GetPersonalByOrgStructureIdServiceIdMocscSurvey?OrgStructureId=${org}&DivisionId=${division}`
    )
    .then(response => {
      if (response.status == 200)
        listPersonalByOrgStructureIdServiceId.value = response.data.response
    })
}

const print = () => {
  window.print()
}

const OrgStructureGroupById = id => {
  callAxios.RequestGet(`/OtherMaster/OrgStructureGroupById?PersonalInfoId=${id}`).then(response => {
    if (response.status == 200) personalInfo.value = response.data.response
  })
}

const setAttribute: Object = computed(() => ({
  dataDashboard: dataDashboard.value,
  columns: columns.value,
  columns2: columns2.value,
  footer: footer.value,
  recommendServiceData: recommendServiceData.value
}))

onMounted(() => {
  GetSectionDropDown()
  GetPersonalByOrgStructureIdServiceId()
  GetReportSurveyCommentByPersonal(queryName.value)
  GetReportSurveyByPersonal(queryName.value)
  OrgStructureGroupById(queryName.value)
})
</script>

<template>
  <section>
    <VCardText>
      <VRow>
        <VCol cols="12">
          <div class="d-flex flex-wrap gap-4 justify-end">
            <VBtn class="hide-on-print" prepend-icon="mdi-printer" color="primary" @click="print">
              พิมพ์
            </VBtn>
          </div>
        </VCol>
      </VRow>
      <VRow>
        <VCol
          v-if="
            dataDashboard.datas?.header ||
            dataDashboard.datasTable?.length > 0 ||
            dataDashboard.datasTable2?.length > 0
          "
          cols="12"
        >
          <VCard class="mb-2">
            <VCardText>
              <div class="text-center my-2">
                <VImg height="100" :src="LogoMOC" />
              </div>
              <div class="text-center">
                <div class="d-flex align-center justify-center">
                  <h1 class="text-body-1">รายงานความพึงพอใจรายบุคคล</h1>
                </div>
                <h2 v-if="personalInfo.orgStructureName" class="text-body-1">
                  <!-- สำนักงานพาณิชย์จังหวัด :  -->
                  {{ personalInfo.orgStructureName }}
                </h2>
                <h3
                  v-if="listSectionDropDown.length > 0 && personalInfo.serviceId"
                  class="text-body-1"
                >
                  กลุ่มงาน :
                  {{
                    listSectionDropDown.find(x => x.serviceId == personalInfo.serviceId).serviceName
                  }}
                </h3>
                <h3 v-if="dataDashboard.datas?.header" class="text-body-1">
                  {{ dataDashboard.datas?.header }}
                </h3>
                <h3 v-if="queryData" class="text-body-1">
                  วันที่ {{ formatDateTH(queryData[0]) }} ถึง
                  {{ formatDateTH(queryData[1]) }}
                </h3>
              </div>
            </VCardText>
          </VCard>
          <IndividualSatisfaction v-if="!isLoad" v-bind="setAttribute" />
        </VCol>
      </VRow>
    </VCardText>
  </section>
</template>

<style>
@media print {
  .hide-on-print {
    display: none !important;
  }
}
</style>

<route lang="yaml">
meta:
  layout: blank
  action: read
</route>
