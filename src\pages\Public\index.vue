<script setup lang="ts">
const router = useRouter()
// import GovWebsiteAccess from "@/@core/components/Public/GovWebsiteAccess.vue";
import { useAuth } from '@/store/useAuth'
import { useAxios } from '@/store/useAxios'

const callAxios = useAxios()
const Auth = useAuth()
const baseUrlImg = localStorage.baseUrlImg
const imgLogo = ref('')
const banners = ref<Array<{ pathName: string }>>([])

defineEmits(['update-banners', 'update-menubanners'])

const GetBanner = () => {
  callAxios.RequestGet('/OtherMaster/GetBanners').then(response => {
    if (response.status === 200) {
      if (response.data.response[0]?.pathName) {
        Auth.banner = baseUrlImg + response.data.response[0].pathName
      }
      banners.value = response.data.response
    }
  })
}

const GetMenuBanner = () => {
  callAxios.RequestGet('/OtherMaster/GetLandingPage').then(response => {
    if (response.status === 200) {
      Auth.menuBanner = [
        ...response.data.response.map((menu: any) => {
          let api = ''

          if (menu.contentId) {
            api = `/api/OtherMaster/GetContentLandingPageById?ContentId=${menu.contentId}`
          } else if (menu.url) {
            api = `/api/OtherMaster/GetLandingPageById?systemManualId=${menu.systemManualId}&Page=1&PageSize=20`
          }

          return {
            text: menu.webpageName,
            image: baseUrlImg + menu.pathName,
            url: menu.url,
            systemManualId: menu.systemManualId,
            contentId: menu.contentId,
            api
          }
        })
      ] // ใช้ Spread Operator เพื่อ Reactive
      // console.log("Auth.menuBanner updated:", Auth.menuBanner);
    }
  })
}

// ฟัง Event เมื่อมีการเปลี่ยนแปลงใน Local Storage
const handleStorageChange = (event: StorageEvent) => {
  if (event.key === 'banner-updated') {
    GetBanner() // อัปเดตข้อมูลแบนเนอร์
  } else if (event.key === 'menu-banner-updated') {
    GetMenuBanner() // อัปเดตข้อมูลเมนูใต้แบนเนอร์
  }
}

onMounted(() => {
  GetBanner()
  GetMenuBanner()
  const url = location.hostname

  //ถ้าเข้า *************
  if (url == '*************') {
    alert('แจ้งเตือน')
  }

  window.addEventListener('storage', handleStorageChange) // เพิ่ม Listener
})

onUnmounted(() => {
  window.removeEventListener('storage', handleStorageChange) // ลบ Listener เมื่อ Component ถูกทำลาย
})

router.afterEach(() => {
  window.scrollTo(0, 0)
})
</script>

<template>
  <div>
    <HeaderPublic :logo="imgLogo" />
    <SliderBanner :banners="banners" />
    <SliderNews />

    <CookieConsent />
    <main id="main-content">
      <SliderMenu />
      <DashboardTab />
    </main>
    <FooterPublic :logo="imgLogo" />
    <GovWebsiteAccess />
  </div>
</template>

<style lang="scss">
@use '@public/assets/css/style.css';
@use '@public/assets/css/skin/skin-2.css';
</style>

<style scoped>
#main-content {
  position: relative;
  padding-block: 50px;
  padding-inline: 0;
}

@media (min-width: 992px) {
  #main-content {
    padding-block: 100px;
    padding-inline: 0;
  }
}

#main-content::before {
  position: absolute;

  /* Example background */
  z-index: 1;
  display: block;
  background: linear-gradient(
    180deg,
    rgb(var(--v-theme-blue-100)) 0%,
    rgba(255, 255, 255, 0%) 100%
  );
  block-size: 50%;
  content: '';
  inline-size: 100%;
  inset-block-start: 0;
  inset-inline-start: 0;

  /* Make sure it's behind other content */
}

.v-application-by #main-content::before {
  filter: grayscale(100%);
}
</style>

<route lang="yaml">
meta:
  layout: blank
  action: read
</route>
