<script setup>
import { useRoute, useRouter } from 'vue-router'
import Filters from '@core/components/Filters.vue'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import { booleanValidator, chipColorStatus, requiredValidator } from '@validators'

const editDialog = ref(false)

const route = useRoute()
const router = useRouter()
const callAxios = useAxios()
const Swal = useSwal()
const queryId = ref(0)
const ProvinceId = ref(route.query.provinceId ? route.query.provinceId : 0)
const oldProvinceId = ref(0)
const oldProvinceIdForm = ref(0)

const OrgStructureId = ref(route.query.orgStructureId ? route.query.orgStructureId : 0)

const ServiceTypeId = ref(0)

const breadcrumbItems = [
  {
    title: 'ผู้ดูแลระบบ',
    disabled: false,
    to: '/pagemain'
  },
  {
    title: 'จัดการงานบริการ',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const filter = ref({
  searchProvince: 0,
  searchQuery: null,
  searchGroup: 0,
  searchType: 0
})

const listType = ref([
  { serviceTypeId: 0, serviceTypeName: 'ทั้งหมด' },
  { serviceTypeId: 1, serviceTypeName: 'กลุ่มงาน' },
  { serviceTypeId: 2, serviceTypeName: 'งานบริการ' }
])

const listFilter = ref([
  {
    name: 'searchProvince',
    type: 'select',
    label: 'จังหวัด',
    title: 'provinceName',
    value: 'provinceId',
    items: []
  },
  {
    name: 'searchQuery',
    type: 'text',
    label: 'ค้นหา'
  },
  {
    name: 'searchGroup',
    type: 'select',
    label: 'ชื่อหน่วยงาน',
    title: 'orgStructureName',
    value: 'orgStructureId',
    items: []
  },
  {
    name: 'searchType',
    type: 'select',
    label: 'ประเภท',
    title: 'serviceTypeName',
    value: 'serviceTypeId',
    items: listType.value
  }
])

const listFields = ref([
  {
    field: 'provinceName',
    header: 'จังหวัด',
    sortable: true
  },
  {
    field: 'orgStructureName',
    header: 'ชื่อหน่วยงาน',
    sortable: true
  },
  {
    field: 'serviceTypeName',
    header: 'ประเภท',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'serviceGroupName',
    header: 'ประเภทงานบริการ',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'serviceName',
    header: 'ชื่อ',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'isActive',
    header: 'สถานะการใช้งาน',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'options',
    header: 'การจัดการ',
    style: { textAlign: 'center' }
  }
])

const listStatus = ref([
  { id: true, name: 'ใช้งาน' },
  { id: false, name: 'ไม่ใช้งาน' }
])

const JsonForm = ref([
  {
    title: 'จังหวัด',
    rules: [requiredValidator],
    required: true,
    typeForm: 'select',
    modelValue: 'provinceId',
    statusUsed: true,
    items: [],
    itemTitle: 'provinceName',
    itemValue: 'provinceId',
    defaultValue: true
  },
  {
    title: 'ชื่อหน่วยงาน',
    rules: [requiredValidator],
    required: true,
    typeForm: 'select',
    modelValue: 'orgStructureId',
    statusUsed: true,
    items: [],
    itemTitle: 'orgStructureName',
    itemValue: 'orgStructureId',
    defaultValue: true
  },

  {
    title: 'ประเภท',
    rules: [requiredValidator],
    required: true,
    typeForm: 'radio',
    modelValue: 'serviceTypeId',
    statusUsed: true,
    items: [],
    choices: [
      {
        label: 'กลุ่มงาน',
        value: 1
      },
      {
        label: 'งานบริการ',
        value: 2
      }
    ],

    defaultValue: true
  },

  {
    title: 'ประเภทงานบริการ',
    rules: [requiredValidator],
    required: true,
    typeForm: 'radio',
    modelValue: 'serviceGroupId',
    statusUsed: true,
    items: [],
    condition: {
      name: 'serviceTypeId',
      value: 2
    },
    choices: [
      {
        label: 'งานบริการหลัก',
        value: 1
      },
      {
        label: 'งานบริการรอง',
        value: 2
      }
    ],

    defaultValue: true
  },
  {
    title: 'ชื่อ',
    rules: [requiredValidator],
    required: true,
    typeForm: 'text',
    modelValue: 'serviceName',
    statusUsed: true
  },
  {
    title: 'สถานะ',
    rules: [booleanValidator],
    required: true,
    typeForm: 'select',
    modelValue: 'isActive',
    statusUsed: true,
    items: listStatus,
    itemTitle: 'name',
    itemValue: 'id',
    defaultValue: true
  }
])

const isDialogVisible = ref(false)

const form = ref({
  logoName: '',
  pathName: '',
  isActive: true,
  logoId: 0
})

const formRaw = ref({
  logoName: '',
  pathName: '',
  isActive: true,
  logoId: 0
})

const GetList = () => {
  callAxios
    .RequestGet(
      `/SystemMaster/GetSystemServiceByProvinceIdAndOrgId?ProvinceId=${ProvinceId.value}&OrgStructureId=${OrgStructureId.value}&ServiceTypeId=${ServiceTypeId.value}`
    )
    .then(response => {
      if (response.status === 200) ListItem.value = response.data.response
    })
}

const ListItem = ref(GetList())

const GetProvincesDropDown = () => {
  callAxios.RequestGet('/SystemMaster/GetProvincesDropDown').then(response => {
    if (response.status == 200) {
      JsonForm.value[0].items = JSON.parse(JSON.stringify(response.data.response))
      listFilter.value[0].items = JSON.parse(JSON.stringify(response.data.response))
      listFilter.value[0].items.unshift({
        provinceId: 0,
        provinceName: 'ทั้งหมด'
      })
    }
  })
}

GetProvincesDropDown()

const GetOrgStructureDropDown = id => {
  callAxios.RequestGet(`/SystemMaster/GetOrgStructureDropDown?ProvinceId=${id}`).then(response => {
    if (response.status == 200) listFilter.value[2].items = response.data.response
    listFilter.value[2].items.unshift({
      orgStructureId: 0,
      orgStructureName: 'ทั้งหมด'
    })
  })
}

const GetOrgStructureDropDownForm = id => {
  callAxios.RequestGet(`/SystemMaster/GetOrgStructureDropDown?ProvinceId=${id}`).then(response => {
    if (response.status == 200) JsonForm.value[1].items = response.data.response
  })
}

const itemOrgStructureDropDown = ref(GetOrgStructureDropDown(0))

watch(filter.value, (val, old) => {
  if (val.searchProvince !== oldProvinceId.value) {
    oldProvinceId.value = val.searchProvince
    filter.value.searchGroup = 0
  }
  ProvinceId.value = val.searchProvince ? val.searchProvince : 0
  OrgStructureId.value = val.searchGroup ? val.searchGroup : 0
  ServiceTypeId.value = val.searchType ? val.searchType : 0

  GetList()
  GetOrgStructureDropDown(val.searchProvince)
})

watch(form.value, val => {
  if (val.provinceId !== oldProvinceIdForm.value) {
    oldProvinceIdForm.value = val.provinceId
    form.value.orgStructureId = 0
  }
  if (val.provinceId > 0) GetOrgStructureDropDownForm(val.provinceId)
  else GetOrgStructureDropDownForm(0)
})

const GetListById = id => {
  callAxios
    .RequestGetById('/SystemMaster/GetSystemServiceById', `?ServiceId=${id}`)
    .then(response => {
      if (response.status == 200) GetOrgStructureDropDownForm(response.data.response.provinceId)
      form.value = response.data.response
      isDialogVisible.value = true
      editDialog.value = true
      queryId.value = id
    })
}

const AddForm = item => {
  if (editDialog.value === false) {
    callAxios.RequestPost('/SystemMaster/AddSystemService', form.value).then(response => {
      if (response.status == 200) {
        isDialogVisible.value = false
        form.value = structuredClone(toRaw(formRaw.value))
        GetList()
      }
    })
  } else {
    callAxios
      .RequestPut(`/SystemMaster/UpdateSystemService?ServiceId=${queryId.value}`, form.value)
      .then(response => {
        if (response.status == 200) {
          isDialogVisible.value = false
          form.value = structuredClone(toRaw(formRaw.value))
          GetList()
        }
      })
  }
}

const onFormSubmit = data => {
  AddForm(data)
}

const onFormReset = () => {
  isDialogVisible.value = false
  form.value = structuredClone(toRaw(formRaw.value))
}

const ApproveDelete = id => {
  const endpoint = `/SystemMaster/DeleteSystemService?ServiceId=${id}`

  Swal.ApproveDelete(endpoint).then(response => {
    if (response) GetList()
  })
}

const back = () => {
  router.push({ path: '/apps/admin/manageservicework' })
}
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <Filters :fields="listFilter" :model-value="filter" />
    <VCard>
      <VCardText>
        <div class="d-flex align-center flex-wrap gap-4">
          <BtnInsert prepend-icon="mdi-plus" color="success-200" @click="isDialogVisible = true">
            เพิ่มงานบริการ
          </BtnInsert>
        </div>
      </VCardText>
      <AppDataTable :filters="filter.searchQuery" :columns="listFields" :value="ListItem">
        <template #isActive="slotProps">
          <div class="text-center">
            <VChip
              variant="elevated"
              :color="chipColorStatus(slotProps.data.isActive, 'เปิดใช้งาน')"
            >
              {{ slotProps.data.isActive }}
            </VChip>
          </div>
        </template>
        <template #options="slotProps">
          <div class="text-center">
            <IconEdit @click="GetListById(slotProps.data.serviceId)" />
            <IconDelete @click="ApproveDelete(slotProps.data.serviceId)" />
          </div>
        </template>
      </AppDataTable>
    </VCard>
    <div class="d-flex flex-wrap justify-end mt-5">
      <div class="demo-space-x">
        <VBtn prepend-icon="mdi-arrow-u-left-top" color="grey-300" @click="back">ย้อนกลับ</VBtn>
      </div>
    </div>
    <AppVDialog
      :is-dialog-visible="isDialogVisible"
      :json-form="JsonForm"
      :edit-dialog="editDialog"
      :form="form"
      size="sm"
      @onDialogDisable="onFormReset"
      @onFormSubmit="onFormSubmit"
    />
  </div>
</template>
