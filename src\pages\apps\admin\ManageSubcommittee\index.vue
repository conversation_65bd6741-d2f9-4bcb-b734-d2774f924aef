<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import Filters from '@core/components/Filters.vue'
import { requiredValidator } from '@validators'

const router = useRouter()
const callAxios = useAxios()
const Swal = useSwal()

const searchQuery: Ref<string | number> = ref('')
const isEdit: Ref<boolean> = ref(false)
const refVForm: Ref<any> = ref(null)
const fileUpload: Ref<any> = ref([])

const editId: Ref<number> = ref(0)

interface FormType {
  geccStandardAuditComitteeId: number
  groupName: string
  isActive: boolean
  fiscalYear: number
  geccStandardAuditComitteeDetailId: number
  geccStandardAuditComitteeRelateId: number
  provinceId: object
  member: string
}

const form: Ref<FormType> = ref({
  fiscalYear: '',
  groupName: '',
  member: [],
  nameStaff: [],
  provinceId: [],
  isActive: '',
  presidentName: ''
})

const formRaw: Ref<FormType> = ref({
  fiscalYear: '',
  groupName: '',
  member: [],
  provinceId: null,
  isActive: ''
})

const listStatus = ref([
  { id: true, name: 'ใช้งาน' },
  { id: false, name: 'ไม่ใช้งาน' }
])

// ทำสี
const statusActive = ref({
  ใช้งาน: 'success',
  ไม่ใช้งาน: 'error'
})

const breadcrumbItems = [
  {
    title: 'ผู้ดูแลระบบ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'จัดการคณะอนุกรรมการ',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const filter = ref({
  searchQuery: null
})

const listFilter = reactive([
  {
    name: 'searchQuery',
    type: 'text',
    label: 'ค้นหา'
  }
])

const listFields = ref([
  {
    field: 'recordDate',
    header: 'วันที่บันทึก',
    sortable: true
  },
  {
    field: 'fiscalYear',
    header: 'ปีงบประมาณ',
    sortable: true
  },
  {
    field: 'groupName',
    header: 'คณะที่',
    sortable: true
  },
  {
    field: 'presidentName',
    header: 'รายชื่อประธาน',
    sortable: true
  },
  {
    field: 'auditComittee',
    header: 'รายชื่ออนุกรรมการ',
    sortable: true
  },
  {
    field: 'provinces',
    header: 'พื้นที่รับผิดชอบ',
    sortable: true
  },

  {
    field: 'options',
    header: 'การจัดการ'
  }
])

const ListItem = ref([])
const ListItemStore = ref([])

const isDialogVisible = ref(false)

const GetList = () => {
  callAxios.RequestGet('/SystemMaster/GetGECCStandardAuditComitteeList').then(response => {
    if (response.status == 200) {
      ListItem.value = response.data.response
      ListItemStore.value = response.data.response
    }
  })
}

onMounted(async () => {
  GetList()
})

const GetListById = id => {
  // form.value = ListItem.value[id];
  // editId.value = id;
  callAxios

    // .RequestGetById(
    //   `/SystemSetting/GetCertificateLogoByID?linkId=${id}`
    // )
    .RequestGetById(
      '/SystemMaster/GetGECCStandardAuditComitteeById',
      `?GECCStandardAuditComitteeId=${id}`
    )
    .then(response => {
      if (response.status == 200) {
        form.value = response.data.response
        editId.value = form.value.geccStandardAuditComitteeId
        listItemsIn.value = form.value.auditComittee
      }
    })
}

const AddForm = () => {
  Swal.ApproveConfirm().then(response => {
    if (response) {
      listItemsIn.value.map(member => {
        form.value.member.push({
          firstName: member.listName,
          position: 0
        })
      })
      form.value.member.push({
        firstName: form.value.presidentName,
        position: 1
      })
      callAxios
        .RequestPost('/SystemMaster/AddGECCStandardAuditComittee', form.value)
        .then(response => {
          if (response.status == 200) {
            isDialogVisible.value = false
            form.value = {}
            nameStaff.value = ''
            listItemsIn.value = []

            // onFormReset();
            GetList()
          }
        })
    }
  })
}

const EditForm = () => {
  Swal.ApproveConfirm().then(response => {
    if (response) {
      // ListItem.value.splice(id, 1, form.value);
      // onFormReset();
      // form.value.createdDateTime = "1900-01-01";
      // form.value.modifiedDateTime = "1900-01-01";
      listItemsIn.value.map(member => {
        form.value.auditComittee.push({
          listName: {
            firstName: member.listName,
            position: 0
          }
        })
      })
      form.value.auditComittee.push({
        listName: {
          firstName: form.value.presidentName,
          position: 1
        }
      })
      form.value.auditComittee = form.value.auditComittee.filter(
        item => typeof item.listName !== 'string'
      )
      console.log(form.value)

      callAxios
        .RequestPut(
          `/SystemMaster/UpdateGECCStandardAuditComittee?GECCStandardAuditComitteeId=${editId.value}`,
          form.value
        )
        .then(response => {
          if (response.status == 200) {
            isDialogVisible.value = false
            GetList()
          }
        })
    }
  })
}

const onFormSubmit = () => {
  refVForm.value?.validate().then(({ valid: isValid }: { valid: boolean }) => {
    if (isValid) {
      if (isEdit.value === false) AddForm()
      else EditForm(editId.value)
    }
  })
}

const onFormReset = () => {
  Swal.ApproveConfirm().then(response => {
    if (response) {
      isDialogVisible.value = false
      form.value = structuredClone(toRaw(formRaw.value))
      nameStaff.value = ''
      listItemsIn.value = []
    }
  })
}

const ApproveDelete = id => {
  const endpoint = `/SystemMaster/DeleteGECCStandardAuditComittee?GECCStandardAuditComitteeId=${id}`

  // console.log(listFields.value);

  // ListItem.value.splice(id, 1);

  Swal.ApproveDelete(endpoint).then(response => {
    if (response) GetList()
  })
}

const listComittee = ref([
  {
    field: 'listName',
    header: 'ชื่อ - นามสกุล'
  },

  {
    field: 'options',
    header: 'การจัดการ'
  }
])

const nameStaff = ref(null)

const addName = value => {
  if (value === null) Swal.AddConditionFailText('กรุณากรอกรายชื่ออนุกรรมการ')
  else listItemsIn.value.push({ listName: value })
}

const deleteName = value => {
  // console.log(value)
  listItemsIn.value.splice(value, 1)
}

const ListProvincesDropDown = ref()

const GetProvincesDropDown = () => {
  callAxios.RequestGet('/SystemMaster/GetProvincesDropDown').then(response => {
    if (response.status == 200) ListProvincesDropDown.value = response.data.response
  })
}

GetProvincesDropDown()

const listItemsIn = ref([])
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <Filters :fields="listFilter" :model-value="filter" />

    <VCard>
      <VCardText>
        <div class="d-flex align-center flex-wrap gap-4">
          <BtnInsert prepend-icon="mdi-plus" @click="((isDialogVisible = true), (isEdit = false))">
            เพิ่ม
          </BtnInsert>
        </div>
      </VCardText>
      <AppDataTable :filters="filter.searchQuery" :columns="listFields" :value="ListItem">
        <template #auditComittee="slotProps">
          <ol>
            <li v-for="item in slotProps.data.auditComittee">
              {{ item }}
            </li>
          </ol>
        </template>
        <template #provinces="slotProps">
          <ol>
            <li v-for="item in slotProps.data.provinces">
              {{ item }}
            </li>
          </ol>
        </template>
        <template #options="slotProps">
          <div class="text-center">
            <IconEdit
              @click="
                ((isDialogVisible = true),
                (isEdit = true),
                GetListById(slotProps.data.geccStandardAuditComitteeId))
              "
            />

            <IconDelete @click="ApproveDelete(slotProps.data.geccStandardAuditComitteeId)" />
          </div>
        </template>
      </AppDataTable>
    </VCard>

    <VDialog v-model="isDialogVisible" persistent class="v-dialog-lg" no-click-animation scrollable>
      <VCard :title="isEdit ? 'แก้ไขข้อมูล' : 'เพิ่มข้อมูล'">
        <DialogCloseBtn variant="text" size="small" @click="onFormReset" />

        <VCardText>
          <VForm ref="refVForm" @submit.prevent="onFormSubmit">
            <VRow class="mb-4 mx-2 mt-6">
              <VCol cols="12" md="1" />
              <VCol cols="12" md="10">
                <VRow>
                  <VCol cols="12" md="12" class="text-lg-end">
                    <VRow class="mb-2" align="center">
                      <VCol cols="12" md="4" class="text-lg-end">
                        <label>
                          ปีงบประมาณ :
                          <small class="text-error">*</small>
                        </label>
                      </VCol>
                      <VCol cols="12" md="8">
                        <DateTimePicker
                          v-model="form.fiscalYear"
                          :rules="[requiredValidator]"
                          teleport-center
                          year-picker
                        />
                      </VCol>
                    </VRow>
                  </VCol>
                  <VCol cols="12" md="12" class="text-lg-end">
                    <VRow class="mb-2" align="center">
                      <VCol cols="12" md="4" class="text-lg-end">
                        <label>
                          คณะที่ :
                          <small class="text-error">*</small>
                        </label>
                      </VCol>
                      <VCol cols="12" md="8">
                        <VTextField v-model="form.groupName" :rules="[requiredValidator]" />
                      </VCol>
                    </VRow>
                  </VCol>
                  <VCol cols="12" md="12" class="text-lg-end">
                    <VRow class="mb-2" align="center">
                      <VCol cols="12" md="4" class="text-lg-end">
                        <label>
                          ชื่อประธาน :
                          <small class="text-error">*</small>
                        </label>
                      </VCol>
                      <VCol cols="12" md="8">
                        <VTextField v-model="form.presidentName" :rules="[requiredValidator]" />
                      </VCol>
                    </VRow>
                  </VCol>
                  <VCol cols="12" md="12" class="text-lg-end">
                    <VRow class="mb-2" align="center">
                      <VCol cols="12" md="4" class="text-lg-end">
                        <label>
                          รายชื่ออนุกรรมการ :
                          <small class="text-error">*</small>
                        </label>
                      </VCol>
                      <VCol cols="12" md="8">
                        <VRow class="mb-2" align="center">
                          <VCol cols="9" class="align-center px-2">
                            <VTextField v-if="isEdit" v-model="nameStaff" />

                            <VTextField v-else v-model="nameStaff" :rules="[requiredValidator]" />
                          </VCol>

                          <VCol cols="3" class="align-center px-2 mt-1">
                            <VBtn
                              class="mx-2"
                              color="success"
                              icon="mdi-plus"
                              @click="addName(nameStaff)"
                            />
                          </VCol>
                        </VRow>

                        <AppDataTable
                          :filters="filter.searchQuery"
                          :columns="listComittee"
                          :value="listItemsIn"
                          :paginator="false"
                        >
                          <template #listName="slotProps" />

                          <template #options="slotProps">
                            <div class="text-center">
                              <IconBtn @click="deleteName(slotProps.index)">
                                <VIcon icon="mdi-delete-outline" />
                              </IconBtn>
                            </div>
                          </template>
                        </AppDataTable>
                      </VCol>
                    </VRow>
                  </VCol>
                  <VCol cols="12" md="12" class="text-lg-end">
                    <VRow class="mb-2" align="center">
                      <VCol cols="12" md="4" class="text-lg-end">
                        <label>
                          พื้นที่รับผิดชอบ :
                          <small class="text-error">*</small>
                        </label>
                      </VCol>
                      <VCol cols="12" md="8">
                        <VAutocomplete
                          v-model="form.provinceId"
                          :rules="[requiredValidator]"
                          chips
                          multiple
                          item-title="provinceName"
                          item-value="provinceId"
                          :items="ListProvincesDropDown"
                          no-data-text="ไม่มีข้อมูล"
                        />
                      </VCol>
                    </VRow>
                  </VCol>
                  <VCol cols="12" md="12" class="text-lg-end">
                    <VRow class="mb-2" align="center">
                      <VCol cols="12" md="4" class="text-lg-end">
                        <label>
                          สถานะ :
                          <small class="text-error">*</small>
                        </label>
                      </VCol>
                      <VCol cols="12" md="8">
                        <VAutocomplete
                          v-model="form.isActive"
                          :items="listStatus"
                          item-title="name"
                          item-value="id"
                        />
                      </VCol>
                    </VRow>
                  </VCol>
                </VRow>
              </VCol>
              <VCol cols="12" md="1" />
            </VRow>
            <div class="d-flex flex-wrap justify-center mt-5">
              <div class="demo-space-x">
                <VBtn type="submit" color="blue-600">บันทึก</VBtn>
                <VBtn color="error-300" @click="onFormReset">ยกเลิก</VBtn>
              </div>
            </div>
          </VForm>
        </VCardText>
      </VCard>
    </VDialog>
  </div>
</template>
