<script setup>
const router = useRouter()
import FAQ from '@/@core/components/Public/FAQ.vue'
import { useAxios } from '@/store/useAxios'
const callAxios = useAxios()

router.afterEach(() => {
  window.scrollTo(0, 0)
})
</script>

<template>
  <div>
    <HeaderPublic />

    <FAQ />

    <FooterPublic />
  </div>
</template>

<style lang="scss">
@use '@public/assets/css/style.css';
@use '@public/assets/css/skin/skin-2.css';
</style>

<route lang="yaml">
meta:
  layout: blank
  action: read
</route>
