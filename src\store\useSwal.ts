import type { AxiosInstance } from 'axios'
import { defineStore } from 'pinia'
import Swal from 'sweetalert2'
import { inject, ref } from 'vue'

export const useSwal = defineStore('useSwal', () => {
  const axios = inject<AxiosInstance>('axios')!
  const error = ref('')

  const ViewFail = () => {
    Swal.fire({
      title: 'แจ้งเตือน',
      text: 'เรียกดูข้อมูลไม่สำเร็จ',
      icon: 'error',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl'
      },
      backdrop: `
            rgba(0,0,0,0.4)
            center top
            no-repeat
          `
    })
  }

  const AddSuccess = () => {
    Swal.fire({
      title: 'แจ้งเตือน',
      text: 'บันทึกข้อมูลสำเร็จ',
      icon: 'success',
      iconColor: '#2caa0e',
      showConfirmButton: false,
      cancelButtonText: '<i class="fas fa-times"></i>&nbsp; ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl'
      },
      backdrop: `
                  rgba(0,0,0,0.4)
                  center top
                  no-repeat
                `
    })
  }

  const AddDetailSuccess = (label: string) => {
    Swal.fire({
      title: 'แจ้งเตือน',
      text: `${label}สำเร็จ`,
      icon: 'success',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl'
      },
      backdrop: `
                  rgba(0,0,0,0.4)
                  center top
                  no-repeat
                `
    })
  }

  const AddFileSuccess = () => {
    Swal.fire({
      title: 'แจ้งเตือน',
      text: 'บันทึกข้อมูลสำเร็จ',
      icon: 'success',
      iconColor: '#2caa0e',
      showConfirmButton: false,
      timer: 1000,
      showCancelButton: false,
      customClass: {
        cancelButton: 'rounded-xl'
      },
      backdrop: `
                  rgba(0,0,0,0.4)
                  center top
                  no-repeat
                `
    })
  }

  const AddFail = () => {
    Swal.fire({
      title: 'แจ้งเตือน ',
      text: 'เพิ่มข้อมูลไม่สำเร็จ กรุณาลองใหม่อีกครั้ง',
      icon: 'error',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl'
      },
      backdrop: `
            rgba(0,0,0,0.4)
            center top
            no-repeat
          `
    })
  }

  const AddFailText = (customMessage: string) => {
    Swal.fire({
      title: 'แจ้งเตือน ',
      text: customMessage || 'เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง',
      icon: 'error',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl'
      },
      backdrop: `
            rgba(0,0,0,0.4)
            center top
            no-repeat
          `
    })
  }

  const AddConditionFailText = (text: string) => {
    Swal.fire({
      title: 'แจ้งเตือน ',
      html: `${text}`,
      icon: 'error',

      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl'
      },
      backdrop: `
            rgba(0,0,0,0.4)
            center top
            no-repeat
          `
    })
  }

  const AddConditionFail = () => {
    Swal.fire({
      title: 'แจ้งเตือน ',
      text: 'เพิ่มข้อมูลไม่สำเร็จ เงื่อนไขไม่ถูกต้อง',
      icon: 'error',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl'
      },
      backdrop: `
            rgba(0,0,0,0.4)
            center top
            no-repeat
          `
    })
  }

  const EditConditionFail = () => {
    Swal.fire({
      title: 'แจ้งเตือน ',
      text: 'แก้ไขข้อมูลไม่สำเร็จ เงื่อนไขไม่ถูกต้อง',
      icon: 'error',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl'
      },
      backdrop: `
            rgba(0,0,0,0.4)
            center top
            no-repeat
          `
    })
  }

  const EditSuccess = () => {
    Swal.fire({
      title: 'แจ้งเตือน',
      text: 'แก้ไขรายการสำเร็จ',
      icon: 'success',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl'
      },
      backdrop: `
                  rgba(0,0,0,0.4)
                  center top
                  no-repeat
                `
    })
  }

  const EditFail = () => {
    Swal.fire({
      title: 'แจ้งเตือน ',
      text: 'แก้ไขข้อมูลไม่สำเร็จ กรุณาลองใหม่อีกครั้ง',
      icon: 'error',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl'
      },
      backdrop: `
            rgba(0,0,0,0.4)
            center top
            no-repeat
          `
    })
  }

  const ReOrderSuccess = () => {
    Swal.fire({
      title: 'แจ้งเตือน',
      text: 'แก้ไขลำดับรายการสำเร็จ',
      icon: 'success',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl'
      },
      backdrop: `
                  rgba(0,0,0,0.4)
                  center top
                  no-repeat
                `
    })
  }

  const ApproveSuccess = () => {
    Swal.fire({
      title: 'แจ้งเตือน',
      text: 'อนุมัติรายการสำเร็จ',
      icon: 'success',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl'
      },
      backdrop: `
                  rgba(0,0,0,0.4)
                  center top
                  no-repeat
                `
    })
  }

  const ApproveFail = () => {
    Swal.fire({
      title: 'แจ้งเตือน ',
      text: 'อนุมัติไม่สำเร็จ กรุณาลองใหม่อีกครั้ง',
      icon: 'error',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl'
      },
      backdrop: `
            rgba(0,0,0,0.4)
            center top
            no-repeat
          `
    })
  }

  const isDuplicate = () => {
    Swal.fire({
      title: 'แจ้งเตือน',
      html: 'ทำรายการไม่สำเร็จ <br> ข้อมูลมีอยู่แล้วในระบบ กรุณาลองใหม่อีกครั้ง',
      icon: 'error',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl'
      },
      backdrop: `
                rgba(0,0,0,0.4)
                center top
                no-repeat
              `
    })
  }

  const ApproveSign = () => {
    Swal.fire({
      title: 'แจ้งเตือน',
      text: 'กรุณาตรวจสอบเจ้าหน้าที่ปฎิบัติงาน',
      icon: 'error',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl'
      },
      backdrop: `
                  rgba(0,0,0,0.4)
                  center top
                  no-repeat
                `
    })
  }

  const isUsed = () => {
    Swal.fire({
      title: 'แจ้งเตือน',
      text: 'ข้อมูลถูกใช้งานอยู่',
      icon: 'warning',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl'
      },
      backdrop: `
          rgba(0,0,0,0.4)
          center top
          no-repeat
        `
    })
  }

  const ApproveDelete = (endpoint: string) => {
    return new Promise(resolve => {
      Swal.fire({
        icon: 'info',
        iconColor: '#b21f29',
        title: 'แจ้งเตือน',
        text: 'ยืนยันการลบข้อมูล',
        showCancelButton: true,
        confirmButtonText: '<i class="fas fa-check-circle"></i> ยืนยัน',
        cancelButtonText: '<i class="fas fa-times-circle"></i> ยกเลิก',
        confirmButtonColor: '#1640ac',
        cancelButtonColor: '#b21f29',
        customClass: {
          cancelButton: 'rounded-xl',
          confirmButton: 'rounded-xl'
        }
      }).then(async result => {
        if (result.isConfirmed) {
          Swal.fire({
            icon: 'info',
            title: 'แจ้งเตือน',
            text: 'ระบบกำลังทำการลบข้อมูลกรุณารอสักครู่',
            timerProgressBar: true,
            allowOutsideClick: false,
            didOpen: async () => {
              Swal.showLoading()
              axios
                .delete(endpoint)
                .then((response: any) => {
                  if (response.status == 200) {
                    Swal.close()
                    Swal.fire({
                      icon: 'success',
                      iconColor: '#2caa0e',
                      title: 'แจ้งเตือน',
                      text: 'ลบข้อมูลสำเร็จ',
                      showConfirmButton: false,
                      cancelButtonText: '<i class="fas fa-times"></i>&nbsp; ปิด',
                      showCancelButton: true,
                      customClass: {
                        cancelButton: 'rounded-xl'
                      },
                      backdrop: `
                          rgba(0,0,0,0.4)
                          center top
                          no-repeat
                        `
                    })
                    resolve(true)
                  }
                })
                .catch((e: any) => {
                  if (e.response.status == 405) {
                    Swal.fire({
                      title: 'แจ้งเตือน',
                      text: 'ข้อมูลถูกใช้งานอยู่',
                      icon: 'warning',
                      showConfirmButton: false,
                      cancelButtonText: 'ปิด',
                      showCancelButton: true,
                      backdrop: `
                          rgba(0,0,0,0.4)
                          center top
                          no-repeat
                        `
                    })
                    resolve(false)

                    return
                  } else if (e.response.status == 404) {
                    Swal.fire({
                      title: 'แจ้งเตือน ',
                      text:
                        `${e.response.data.message}` || 'ลบข้อมูลไม่สำเร็จ กรุณาลองใหม่อีกครั้ง',
                      icon: 'error',
                      showConfirmButton: false,
                      cancelButtonText: 'ปิด',
                      showCancelButton: true,
                      backdrop: `
                          rgba(0,0,0,0.4)
                          center top
                          no-repeat
                        `
                    })
                    resolve(false)

                    return
                  } else if (e.response.status == 409) {
                    Swal.fire({
                      title: 'แจ้งเตือน ',
                      text: e.response.data.message,
                      icon: 'warning',
                      showConfirmButton: false,
                      cancelButtonText: 'ปิด',
                      showCancelButton: true,
                      backdrop: `
                          rgba(0,0,0,0.4)
                          center top
                          no-repeat
                        `
                    })
                    resolve(false)

                    return
                  }
                  if (e.response.status == 428) {
                    Swal.fire({
                      title: 'แจ้งเตือน',
                      text: e.response.data.message,
                      icon: 'warning',
                      showConfirmButton: false,
                      cancelButtonText: 'ปิด',
                      showCancelButton: true,
                      backdrop: `
                          rgba(0,0,0,0.4)
                          center top
                          no-repeat
                        `
                    })
                    resolve(false)
                  } else {
                    Swal.fire({
                      title: 'แจ้งเตือน ',
                      text: 'ลบข้อมูลไม่สำเร็จ กรุณาลองใหม่อีกครั้ง',
                      icon: 'error',
                      showConfirmButton: false,
                      cancelButtonText: 'ปิด',
                      showCancelButton: true,
                      backdrop: `
                          rgba(0,0,0,0.4)
                          center top
                          no-repeat
                        `
                    })
                    resolve(false)
                  }
                })
            }
          })
        }
      })
    })

    // return '.......'
  }

  const CancalDelete = (endpoint: string) => {
    return new Promise(resolve => {
      Swal.fire({
        icon: 'info',
        iconColor: '#b21f29',
        title: 'แจ้งเตือน',
        text: 'ต้องการยกเลิกรายการใช่หรือไม่',
        showCancelButton: true,
        confirmButtonText: '<i class="fas fa-check-circle"></i> ยืนยัน',
        cancelButtonText: '<i class="fas fa-times-circle"></i> ยกเลิก',
        confirmButtonColor: '#1640ac',
        cancelButtonColor: '#b21f29'
      }).then(async result => {
        if (result.isConfirmed) {
          Swal.fire({
            icon: 'info',
            title: 'แจ้งเตือน',
            text: 'ระบบกำลังทำการลบข้อมูลกรุณารอสักครู่',
            timerProgressBar: true,
            allowOutsideClick: false,
            didOpen: async () => {
              Swal.showLoading()
              axios
                .delete(endpoint)
                .then((response: any) => {
                  if (response.status == 200) {
                    Swal.close()
                    resolve(true)
                  }
                })
                .catch((e: any) => {
                  if (e.response.status == 405) {
                    Swal.fire({
                      title: 'แจ้งเตือน',
                      text: 'ข้อมูลถูกใช้งานอยู่',
                      icon: 'warning',
                      showConfirmButton: false,
                      cancelButtonText: 'ปิด',
                      showCancelButton: true,
                      customClass: {
                        cancelButton: 'rounded-xl'
                      },
                      backdrop: `
                          rgba(0,0,0,0.4)
                          center top
                          no-repeat
                        `
                    })
                    resolve(false)
                  }
                  if (e.response.status == 404) {
                    Swal.fire({
                      title: 'แจ้งเตือน ',
                      text: 'ลบข้อมูลไม่สำเร็จ กรุณาลองใหม่อีกครั้ง',
                      icon: 'error',
                      showConfirmButton: false,
                      cancelButtonText: 'ปิด',
                      showCancelButton: true,
                      customClass: {
                        cancelButton: 'rounded-xl'
                      },
                      backdrop: `
                          rgba(0,0,0,0.4)
                          center top
                          no-repeat
                        `
                    })
                    resolve(false)
                  }
                  if (e.response.status == 428) {
                    Swal.fire({
                      title: 'แจ้งเตือน',
                      text: e.response.data.message,
                      icon: 'warning',
                      showConfirmButton: false,
                      cancelButtonText: 'ปิด',
                      showCancelButton: true,
                      customClass: {
                        cancelButton: 'rounded-xl'
                      },
                      backdrop: `
                          rgba(0,0,0,0.4)
                          center top
                          no-repeat
                        `
                    })
                    resolve(false)
                  } else {
                    Swal.fire({
                      title: 'แจ้งเตือน ',
                      text: 'ลบข้อมูลไม่สำเร็จ กรุณาลองใหม่อีกครั้ง',
                      icon: 'error',
                      showConfirmButton: false,
                      cancelButtonText: 'ปิด',
                      showCancelButton: true,
                      customClass: {
                        cancelButton: 'rounded-xl'
                      },
                      backdrop: `
                          rgba(0,0,0,0.4)
                          center top
                          no-repeat
                        `
                    })
                    resolve(false)
                  }
                })
            }
          })
        }
      })
    })

    // return '.......'
  }

  const Delete = (endpoint: string) => {
    return new Promise(resolve => {
      Swal.showLoading()
      axios
        .delete(endpoint)
        .then((response: any) => {
          if (response.status == 200) {
            Swal.close()
            resolve(true)
          }

          if (response.status == 405) {
            Swal.fire({
              title: 'แจ้งเตือน',
              text: 'ข้อมูลถูกใช้งานอยู่',
              icon: 'warning',
              showConfirmButton: false,
              cancelButtonText: 'ปิด',
              showCancelButton: true,
              customClass: {
                cancelButton: 'rounded-xl'
              },
              backdrop: `
                    rgba(0,0,0,0.4)
                    center top
                    no-repeat
                  `
            })
            resolve(false)
          }
          if (response.status == 404) {
            Swal.fire({
              title: 'แจ้งเตือน ',
              text: 'ลบข้อมูลไม่สำเร็จ กรุณาลองใหม่อีกครั้ง',
              icon: 'error',
              showConfirmButton: false,
              cancelButtonText: 'ปิด',
              showCancelButton: true,
              customClass: {
                cancelButton: 'rounded-xl'
              },
              backdrop: `
                    rgba(0,0,0,0.4)
                    center top
                    no-repeat
                  `
            })
            resolve(false)
          }
        })
        .catch((e: any) => {
          if (e.status == 405) {
            Swal.fire({
              title: 'แจ้งเตือน',
              text: 'ข้อมูลถูกใช้งานอยู่',
              icon: 'warning',
              showConfirmButton: false,
              cancelButtonText: 'ปิด',
              showCancelButton: true,
              backdrop: `
                    rgba(0,0,0,0.4)
                    center top
                    no-repeat
                  `
            })
            resolve(false)
          } else {
            Swal.fire({
              title: 'แจ้งเตือน ',
              text: 'ลบข้อมูลไม่สำเร็จ กรุณาลองใหม่อีกครั้ง',
              icon: 'error',
              showConfirmButton: false,
              cancelButtonText: 'ปิด',
              showCancelButton: true,
              backdrop: `
                  rgba(0,0,0,0.4)
                  center top
                  no-repeat
                `
            })

            console.error(e.response.data)
            resolve(false)
          }
        })
    })

    // return '.......'
  }

  const callCatch = () => {
    Swal.fire({
      title: 'แจ้งเตือน',
      text: 'เรียกข้อมูลไม่สำเร็จ กรุณาลองใหม่อีกครั้ง',
      icon: 'error',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl'
      },
      backdrop: `
            rgba(0,0,0,0.4)
            center top
            no-repeat
          `
    })
  }

  const ApproveCancel = () => {
    return new Promise(resolve => {
      Swal.fire({
        icon: 'info',
        iconColor: '#65bdfd',
        title: 'แจ้งเตือน',
        text: 'ท่านต้องการยกเลิกรายการนี้ใช่หรือไม่ ?',
        showCancelButton: true,
        confirmButtonText: '<i class="fas fa-check-circle"></i> ยืนยัน',
        cancelButtonText: '<i class="fas fa-times-circle"></i> ยกเลิก',
        confirmButtonColor: '#1640ac',
        cancelButtonColor: '#b21f29',
        customClass: {
          cancelButton: 'rounded-xl',
          confirmButton: 'rounded-xl'
        }
      }).then(async result => {
        if (result.isConfirmed) resolve(true)
      })
    })
  }

  const ApproveConfirm = () => {
    return new Promise(resolve => {
      Swal.fire({
        icon: 'info',
        iconColor: '#65bdfd',
        title: 'แจ้งเตือน',
        text: 'ยืนยันการบันทึกข้อมูล',
        showCancelButton: true,
        confirmButtonText: '<i class="fas fa-check-circle"></i> ยืนยัน',
        cancelButtonText: '<i class="fas fa-times-circle"></i> ยกเลิก',
        confirmButtonColor: '#1640ac',
        cancelButtonColor: '#b21f29',
        customClass: {
          cancelButton: 'rounded-xl',
          confirmButton: 'rounded-xl'
        }
      }).then(async result => {
        if (result.isConfirmed) resolve(true)
      })
    })
  }

  const ApproveConfirmText = (text: string) => {
    return new Promise(resolve => {
      Swal.fire({
        icon: 'info',
        iconColor: '#65bdfd',
        title: 'แจ้งเตือน',
        html: `${text} `,
        showCancelButton: true,
        confirmButtonText: '<i class="fas fa-check-circle"></i> ยืนยัน',
        cancelButtonText: '<i class="fas fa-times-circle"></i> ยกเลิก',
        confirmButtonColor: '#1640ac',
        cancelButtonColor: '#b21f29',
        customClass: {
          cancelButton: 'rounded-xl',
          confirmButton: 'rounded-xl'
        }
      }).then(async result => {
        if (result.isConfirmed) resolve(true)
      })
    })
  }

  const ApproveConfirmEmail = () => {
    return new Promise(resolve => {
      Swal.fire({
        icon: 'info',
        iconColor: '#65bdfd',
        title: 'แจ้งเตือน',
        text: 'ยืนยันการส่งอีเมล',
        showCancelButton: true,
        confirmButtonText: '<i class="fas fa-check-circle"></i> ยืนยัน',
        cancelButtonText: '<i class="fas fa-times-circle"></i> ยกเลิก',
        confirmButtonColor: '#1640ac',
        cancelButtonColor: '#b21f29',
        customClass: {
          cancelButton: 'rounded-xl',
          confirmButton: 'rounded-xl'
        }
      }).then(async result => {
        if (result.isConfirmed) resolve(true)
      })
    })
  }

  const Approveremove = () => {
    return new Promise(resolve => {
      Swal.fire({
        icon: 'info',
        iconColor: '#65bdfd',
        title: 'แจ้งเตือน',
        text: 'ยืนยันการลบข้อมูล',
        showCancelButton: true,
        confirmButtonText: '<i class="fas fa-check-circle"></i> ยืนยัน',
        cancelButtonText: '<i class="fas fa-times-circle"></i> ยกเลิก',
        confirmButtonColor: '#1640ac',
        cancelButtonColor: '#b21f29',
        customClass: {
          cancelButton: 'rounded-xl',
          confirmButton: 'rounded-xl'
        }
      }).then(async result => {
        if (result.isConfirmed) resolve(true)
      })
    })
  }

  const ApproveCondition = (text: string) => {
    return new Promise(resolve => {
      Swal.fire({
        icon: 'info',
        title: 'แจ้งเตือน',
        html: `${text} `,
        showCancelButton: true,
        confirmButtonText: '<i class="fas fa-check-circle"></i> ยืนยัน',
        cancelButtonText: '<i class="fas fa-times-circle"></i> ยกเลิก',
        confirmButtonColor: '#1640ac',
        cancelButtonColor: '#b21f29',
        customClass: {
          cancelButton: 'rounded-xl',
          confirmButton: 'rounded-xl'
        }
      }).then(async result => {
        if (result.isConfirmed) resolve(true)
      })
    })
  }

  const ApproveConditionclose = (text: string) => {
    return new Promise(resolve => {
      Swal.fire({
        icon: 'info',
        title: 'แจ้งเตือน',
        html: `${text} `,
        showConfirmButton: false,
        cancelButtonText: 'ปิด',
        showCancelButton: true,
        customClass: {
          cancelButton: 'rounded-xl',
          confirmButton: 'rounded-xl'
        }
      }).then(async result => {
        if (result.isConfirmed) resolve(true)
      })
    })
  }

  const ReviseText = (label: string) => {
    return new Promise(resolve => {
      Swal.fire({
        input: 'textarea',
        inputLabel: label,
        inputPlaceholder: 'โปรดระบุเหตุผล...',
        inputAttributes: {
          'aria-label': 'โปรดระบุเหตุผล'
        },
        showCancelButton: true,
        confirmButtonText: 'ตกลง',
        cancelButtonText: 'ยกเลิก',
        customClass: {
          cancelButton: 'rounded-xl',
          confirmButton: 'rounded-xl'
        }
      }).then(async result => {
        if (result.isConfirmed) resolve(result)
      })
    })
  }

  const CheckInSuccess = () => {
    Swal.fire({
      title: 'แจ้งเตือน',
      text: 'บันทึกลงเวลางานสำเร็จ',
      icon: 'success',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl',
        confirmButton: 'rounded-xl'
      },
      backdrop: `
                  rgba(0,0,0,0.4)
                  center top
                  no-repeat
                `
    })
  }

  const CheckInFail = () => {
    Swal.fire({
      title: 'แจ้งเตือน ',
      text: 'บันทึกลงเวลางานไม่สำเร็จ กรุณาลองใหม่อีกครั้ง',
      icon: 'error',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl',
        confirmButton: 'rounded-xl'
      },
      backdrop: `
            rgba(0,0,0,0.4)
            center top
            no-repeat
          `
    })
  }

  const fetchLoading = (endpoint: string) => {
    return new Promise((resolve, reject) => {
      Swal.fire({
        icon: 'info',
        title: 'แจ้งเตือน',
        text: 'ระบบกำลังทำการเรียกข้อมูล กรุณารอสักครู่',
        timerProgressBar: true,
        allowOutsideClick: false,
        didOpen: async () => {
          Swal.showLoading()
          axios
            .get(endpoint)
            .then((response: any) => {
              if (response.status == 200) {
                Swal.close()
                resolve(response)
              }
              if (response.status == 404) {
                Swal.fire({
                  title: 'แจ้งเตือน ',
                  text: 'เรียกข้อมูลไม่สำเร็จ กรุณาลองใหม่อีกครั้ง',
                  icon: 'error',
                  showConfirmButton: false,
                  cancelButtonText: 'ปิด',
                  showCancelButton: true,
                  customClass: {
                    cancelButton: 'rounded-xl',
                    confirmButton: 'rounded-xl'
                  },
                  backdrop: `
                      rgba(0,0,0,0.4)
                      center top
                      no-repeat
                    `
                })
                resolve(false)
              }
            })
            .catch((e: any) => {
              Swal.fire({
                title: 'แจ้งเตือน ',
                text: 'เรียกข้อมูลไม่สำเร็จ กรุณาลองใหม่อีกครั้ง',
                icon: 'error',
                showConfirmButton: false,
                cancelButtonText: 'ปิด',
                showCancelButton: true,
                customClass: {
                  cancelButton: 'rounded-xl',
                  confirmButton: 'rounded-xl'
                },
                backdrop: `
                    rgba(0,0,0,0.4)
                    center top
                    no-repeat
                  `
              })

              console.error(e.response.data)
              reject(e.response.data)
            })
        }
      })
    })
  }

  const validateText = (text: string) => {
    Swal.fire({
      title: 'แจ้งเตือน ',
      text: `${text}`,
      icon: 'info',

      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl'
      },
      backdrop: `
            rgba(0,0,0,0.4)
            center top
            no-repeat
          `
    })
  }

  const fetchLoadingApi = () => {
    return new Promise<void>(resolve => {
      Swal.fire({
        icon: 'info',
        title: 'แจ้งเตือน',
        text: 'ระบบกำลังทำการเรียกข้อมูล กรุณารอสักครู่',
        timerProgressBar: true,
        allowOutsideClick: false,
        customClass: {
          cancelButton: 'rounded-xl',
          confirmButton: 'rounded-xl'
        },
        didOpen: () => {
          Swal.showLoading()
          resolve()
        }
      })
    })
  }

  const CopySuccess = () => {
    Swal.fire({
      title: 'แจ้งเตือน',
      text: 'คัดลอกรายการสำเร็จ',
      icon: 'success',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl',
        confirmButton: 'rounded-xl'
      },
      backdrop: `
                  rgba(0,0,0,0.4)
                  center top
                  no-repeat
                `
    })
  }

  const CopyFail = () => {
    Swal.fire({
      title: 'แจ้งเตือน ',
      text: 'คัดลอกข้อมูลไม่สำเร็จ กรุณาลองใหม่อีกครั้ง',
      icon: 'error',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl',
        confirmButton: 'rounded-xl'
      },
      backdrop: `
            rgba(0,0,0,0.4)
            center top
            no-repeat
          `
    })
  }

  const LabelSuccess = (label: string) => {
    Swal.fire({
      title: 'แจ้งเตือน',
      text: `${label}สำเร็จ`,
      icon: 'success',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl',
        confirmButton: 'rounded-xl'
      },
      backdrop: `
                  rgba(0,0,0,0.4)
                  center top
                  no-repeat
                `
    })
  }

  const LabelFail = (label: string) => {
    Swal.fire({
      title: 'แจ้งเตือน',
      text: `${label}ไม่สำเร็จ กรุณาลองใหม่อีกครั้ง`,
      icon: 'error',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl',
        confirmButton: 'rounded-xl'
      },
      backdrop: `
                  rgba(0,0,0,0.4)
                  center top
                  no-repeat
                `
    })
  }

  const LabelError = () => {
    Swal.fire({
      title: 'แจ้งเตือน',
      text: 'เกิดข้อผิดพลาดในการส่งอีเมลทดสอบ กรุณาลองอีกครั้ง',
      icon: 'error',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl',
        confirmButton: 'rounded-xl'
      },
      backdrop: `
                  rgba(0,0,0,0.4)
                  center top
                  no-repeat
                `
    })
  }

  const DeleteFail = () => {
    Swal.fire({
      title: 'แจ้งเตือน',
      text: 'ลบข้อมูลไม่สำเร็จ กรุณาลองใหม่อีกครั้ง',
      icon: 'error',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl',
        confirmButton: 'rounded-xl'
      },
      backdrop: `
                  rgba(0,0,0,0.4)
                  center top
                  no-repeat
                `
    })
  }

  const DeleteIsActiveCheck = () => {
    Swal.fire({
      icon: 'info',
      iconColor: '#b21f29',
      title: 'แจ้งเตือน',
      text: 'ยืนยันการลบข้อมูล',
      showCancelButton: true,
      confirmButtonText: '<i class="fas fa-check-circle"></i> ยืนยัน',
      cancelButtonText: '<i class="fas fa-times-circle"></i> ยกเลิก',
      confirmButtonColor: '#1640ac',
      cancelButtonColor: '#b21f29',
      customClass: {
        cancelButton: 'rounded-xl',
        confirmButton: 'rounded-xl'
      }
    }).then(result => {
      if (result.isConfirmed) {
        Swal.fire({
          title: 'แจ้งเตือน',
          text: 'ไม่สามารถลบข้อมูลได้ เนื่องจากมีการใช้งานอยู่',
          icon: 'error',
          showConfirmButton: false,
          cancelButtonText: 'ปิด',
          showCancelButton: true,
          customClass: {
            cancelButton: 'rounded-xl',
            confirmButton: 'rounded-xl'
          },
          backdrop: `
              rgba(0,0,0,0.4)
              center top
              no-repeat
            `
        })
      }
    })
  }

  const isRequestLost = (title: string) => {
    Swal.fire({
      title: 'แจ้งเตือน',
      text: title,
      icon: 'error',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl',
        confirmButton: 'rounded-xl'
      },
      backdrop: `
          rgba(0,0,0,0.4)
          center top
          no-repeat
        `
    })
  }

  const isNotFound = () => {
    Swal.fire({
      title: 'แจ้งเตือน',
      text: 'ไม่พบข้อมูล',
      icon: 'error',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl',
        confirmButton: 'rounded-xl'
      },
      backdrop: `
          rgba(0,0,0,0.4)
          center top
          no-repeat
        `
    })
  }

  const close = () => {
    Swal.close()
  }

  const ApproveDeleteItem = () => {
    return new Promise(resolve => {
      Swal.fire({
        icon: 'info',
        title: 'แจ้งเตือน',
        text: 'ท่านต้องการลบข้อมูลรายการนี้ใช่หรือไม่ ?',
        showCancelButton: true,
        confirmButtonText: 'ตกลง',
        cancelButtonText: 'ยกเลิก',
        customClass: {
          cancelButton: 'rounded-xl',
          confirmButton: 'rounded-xl'
        }
      }).then(async result => {
        if (result.isConfirmed) {
          Swal.close()
          Swal.fire({
            icon: 'success',
            title: 'แจ้งเตือน',
            text: 'ลบรายการสำเร็จ',
            showConfirmButton: false,
            cancelButtonText: 'ปิด',
            showCancelButton: true,
            customClass: {
              cancelButton: 'rounded-xl',
              confirmButton: 'rounded-xl'
            },
            backdrop: `
                rgba(0,0,0,0.4)
                center top
                no-repeat
              `
          })
          resolve(true)
        }
      })
    })
  }

  const Loading = () => {
    Swal.fire({
      icon: 'info',
      title: 'แจ้งเตือน',
      text: 'ระบบกำลังดำเนินการ กรุณารอสักครู่',
      timerProgressBar: true,
      allowOutsideClick: false,
      customClass: {
        cancelButton: 'rounded-xl',
        confirmButton: 'rounded-xl'
      },
      didOpen: async () => {
        Swal.showLoading()
      }
    })
  }

  const ConditionSuccessText = (text: string) => {
    Swal.fire({
      title: 'แจ้งเตือน ',
      html: `${text}`,
      icon: 'success',
      showConfirmButton: false,
      cancelButtonText: 'ปิด',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl',
        confirmButton: 'rounded-xl'
      },
      backdrop: `
            rgba(0,0,0,0.4)
            center top
            no-repeat
          `
    })
  }

  const ConditionWarningText = (text: string) => {
    Swal.fire({
      icon: 'warning',
      iconColor: '#b21f29',
      title: 'แจ้งเตือน ',
      html: `${text}`,
      showConfirmButton: false,
      cancelButtonText: '<i class="fas fa-times-circle"></i> ปิด',
      cancelButtonColor: '#474747',
      showCancelButton: true,
      customClass: {
        cancelButton: 'rounded-xl',
        confirmButton: 'rounded-xl'
      },
      backdrop: `
              rgba(0,0,0,0.4)
              center top
              no-repeat
            `
    })
  }

  const ConditionWarningTextFalse = (text: string) => {
    return new Promise(resolve => {
      Swal.fire({
        icon: 'warning',
        iconColor: '#b21f29',
        title: 'แจ้งเตือน',
        html: `${text}`,
        showCancelButton: true,
        confirmButtonText: '<i class="fas fa-check-circle"></i> ยืนยัน',
        cancelButtonText: '<i class="fas fa-times-circle"></i> ยกเลิก',
        confirmButtonColor: '#474747',
        cancelButtonColor: '#b21f29',
        customClass: {
          cancelButton: 'rounded-xl',
          confirmButton: 'rounded-xl'
        }
      }).then(async result => {
        if (result.isConfirmed) resolve(true)
      })
    })
  }

  const ConditionWarningTextTrue = (text: string) => {
    return new Promise(resolve => {
      Swal.fire({
        icon: 'warning',
        iconColor: '#b21f29',
        title: 'แจ้งเตือน',
        html: `${text}`,
        showCancelButton: false,
        confirmButtonText: '<i class="fas fa-times-circle"></i> ปิด',
        confirmButtonColor: '#474747',
        customClass: {
          cancelButton: 'rounded-xl',
          confirmButton: 'rounded-xl'
        }
      }).then(async result => {
        if (result.isConfirmed) resolve(true)
      })
    })
  }

  const ConditionWarningTextLogin = (text: string) => {
    return new Promise(resolve => {
      Swal.fire({
        icon: 'warning',
        iconColor: '#b21f29',
        title: 'แจ้งเตือน',
        html: `${text}`,
        showCancelButton: false,
        confirmButtonText: 'ตกลง',
        confirmButtonColor: '#474747',
        customClass: {
          cancelButton: 'rounded-xl',
          confirmButton: 'rounded-xl'
        }
      }).then(async result => {
        if (result.isConfirmed) resolve(true)
      })
    })
  }

  const SessionExpired = () => {
    // Swal.fire({
    //   icon: "error",
    //   title: "แจ้งเตือน",
    //   text: "เซสซันหมดอายุ โปรดเข้าสู่ระบบอีกครั้ง",
    //   showCancelButton: false,
    //   confirmButtonText: "ตกลง",
    //   allowOutsideClick: false,
    // }).then(async (result) => {
    //   if (result.isConfirmed) {
    //     localStorage.removeItem("token");
    //     localStorage.removeItem("userAbilities");
    //     localStorage.removeItem("username");
    //     router.push("/apps/login");
    //   }
    // });
  }

  const LoginSuccess = () => {
    Swal.fire({
      title: 'แจ้งเตือน',
      text: 'เข้าสู่ระบบสำเร็จ',
      icon: 'success',
      showCancelButton: false,
      showConfirmButton: false,
      allowOutsideClick: false,
      timer: 3000,
      customClass: {
        cancelButton: 'rounded-xl',
        confirmButton: 'rounded-xl'
      },
      backdrop: `
                  rgba(0,0,0,0.4)
                  center top
                  no-repeat
                `
    })
  }

  const LoginFail = () => {
    Swal.fire({
      title: 'แจ้งเตือน',
      text: 'ไม่สามารถเข้าสู่ระบบได้ กรุณาลองใหม่อีกครั้ง',
      icon: 'error',
      showCancelButton: false,
      showConfirmButton: false,
      showDenyButton: true,
      denyButtonText: 'ปิด',
      allowOutsideClick: false,
      customClass: {
        cancelButton: 'rounded-xl',
        confirmButton: 'rounded-xl'
      },
      backdrop: `
                rgba(0,0,0,0.4)
                center top
                no-repeat
              `
    }).then(async result => {
      if (result.isDenied) window.history.back()
    })
  }

  const ToastSaveSuccess = () => {
    const Toast = Swal.mixin({
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 3000,
      timerProgressBar: true,
      didOpen: toast => {
        toast.onmouseenter = Swal.stopTimer
        toast.onmouseleave = Swal.resumeTimer
      }
    })

    Toast.fire({
      icon: 'success',
      title: 'บันทึกข้อมูลสำเร็จ'
    })
  }

  const ToastSuccess = () => {
    const Toast = Swal.mixin({
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 3000,
      timerProgressBar: true,
      customClass: {
        cancelButton: 'rounded-xl',
        confirmButton: 'rounded-xl'
      },
      didOpen: toast => {
        toast.onmouseenter = Swal.stopTimer
        toast.onmouseleave = Swal.resumeTimer
      }
    })

    Toast.fire({
      icon: 'success',
      title: 'เพิ่มข้อมูลสำเร็จ'
    })
  }

  const ToastFail = () => {
    const Toast = Swal.mixin({
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 3000,
      timerProgressBar: true,
      customClass: {
        cancelButton: 'rounded-xl',
        confirmButton: 'rounded-xl'
      },
      didOpen: toast => {
        toast.onmouseenter = Swal.stopTimer
        toast.onmouseleave = Swal.resumeTimer
      }
    })

    Toast.fire({
      icon: 'error',
      title: 'เพิ่มข้อมูลไม่สำเร็จ กรุณาลองใหม่อีกครั้ง'
    })
  }

  const ToastText = (text: string, status: any) => {
    const Toast = Swal.mixin({
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 3000,
      timerProgressBar: true,
      customClass: {
        cancelButton: 'rounded-xl',
        confirmButton: 'rounded-xl'
      },
      didOpen: toast => {
        toast.onmouseenter = Swal.stopTimer
        toast.onmouseleave = Swal.resumeTimer
      }
    })

    Toast.fire({
      icon: status,
      title: text
    })
  }

  return {
    error,
    ViewFail,
    AddSuccess,
    AddDetailSuccess,
    AddFileSuccess,
    AddFail,
    AddFailText,
    AddConditionFailText,
    AddConditionFail,
    EditConditionFail,
    EditSuccess,
    EditFail,
    ReOrderSuccess,
    ApproveSuccess,
    ApproveFail,
    isDuplicate,
    ApproveSign,
    isUsed,
    ApproveDelete,
    CancalDelete,
    Delete,
    callCatch,
    ApproveCancel,
    ApproveConfirm,
    ApproveConfirmText,
    ApproveConfirmEmail,
    Approveremove,
    ApproveCondition,
    ApproveConditionclose,
    ReviseText,
    CheckInSuccess,
    CheckInFail,
    fetchLoading,
    validateText,
    fetchLoadingApi,
    CopySuccess,
    CopyFail,
    LabelSuccess,
    LabelFail,
    LabelError,
    DeleteFail,
    DeleteIsActiveCheck,
    isRequestLost,
    isNotFound,
    close,
    ApproveDeleteItem,
    Loading,
    ConditionSuccessText,
    ConditionWarningText,
    ConditionWarningTextFalse,
    ConditionWarningTextTrue,
    ConditionWarningTextLogin,
    SessionExpired,
    LoginSuccess,
    LoginFail,
    ToastSaveSuccess,
    ToastSuccess,
    ToastFail,
    ToastText
  }
})
