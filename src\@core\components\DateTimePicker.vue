<script setup>
import Datepicker from '@vuepic/vue-datepicker'
import '@vuepic/vue-datepicker/dist/main.css'
import { useTheme } from 'vuetify'
import { VInput, makeVInputProps } from 'vuetify/lib/components/VInput/VInput'
import { filterInputAttrs } from 'vuetify/lib/util/helpers'

const props = defineProps({
  ...makeVInputProps({ hideDetails: 'auto' }),
  iconStyleFlag: {
    type: Boolean,
    default: false
  },
  range: Boolean,
  mindate: String,
  disableddays: Array,
  disabledDates: Array,
  timePicker: Boolean,
  datetimePicker: Boolean,
  placeholder: String,
  yearPicker: Boolean,
  monthPicker: Boolean,
  datetimeRange: Boolean,
  teleport: {
    type: Boolean,
    default: () => true
  },
  teleportCenter: Boolean,
  themeDark: Boolean
})

const emit = defineEmits(['update:modelValue', 'click:clear'])

// console.log("props", props.iconStyleFlag);
const onClearValue = el => {
  emit('click:clear', el)
}

const attrs = useAttrs()
const [rootAttrs] = filterInputAttrs(attrs)
const isCalendarOpen = ref(false)

const onClear = el => {
  el.stopPropagation()
  nextTick(() => {
    emit('update:modelValue', '')
    emit('click:clear', el)
  })
}

const vuetifyTheme = useTheme()

const beMonth = [
  { id: 1, nameMonth: 'มกราคม' },
  { id: 2, nameMonth: 'กุมภาพันธ์' },
  { id: 3, nameMonth: 'มีนาคม' },
  { id: 4, nameMonth: 'เมษายน' },
  { id: 5, nameMonth: 'พฤษภาคม' },
  { id: 6, nameMonth: 'มิถุนายน' },
  { id: 7, nameMonth: 'กรกฎาคม' },
  { id: 8, nameMonth: 'สิงหาคม' },
  { id: 9, nameMonth: 'กันยายน' },
  { id: 10, nameMonth: 'ตุลาคม' },
  { id: 11, nameMonth: 'พฤศจิกายน' },
  { id: 12, nameMonth: 'ธันวาคม' }
]

const format = date => {
  if (props.yearPicker) {
    if (props.range) {
      if (date.length == 1) {
        const year = date[0].getFullYear()

        return `${parseInt(year)} -`
      } else if (date.length == 2) {
        const year = date[0].getFullYear()
        const year2 = date[1].getFullYear()

        return `${year} - ${year2}`
      }
    } else {
      const year = date.getFullYear().toString()

      return `${parseInt(year)}`
    }
  }

  if (props.monthPicker) {
    if (props.range) {
      if (date.length == 1) {
        const month = beMonth.find(i => i.id == date[0].getMonth() + 1).nameMonth

        return `${month}`
      } else if (date.length == 2) {
        const month = beMonth.find(i => i.id == date[0].getMonth() + 1).nameMonth

        const month2 = beMonth.find(i => i.id == date[1].getMonth() + 1).nameMonth

        return `${month} - ${month2}`
      }
    } else {
      const month = beMonth.find(i => i.id == date.getMonth() + 1).nameMonth

      return `${month}`
    }
  }

  if (props.timePicker && props.timePicker) {
    if (date.length == 1) {
      return `${date[0].getHours().toString().padStart(2, '0')}:${date[0]
        .getMinutes()
        .toString()
        .padStart(2, '0')} -`
    } else if (date.length == 2) {
      return `${date[0].getHours().toString().padStart(2, '0')}:${date[0]
        .getMinutes()
        .toString()
        .padStart(2, '0')} - ${date[1]
        .getHours()
        .toString()
        .padStart(2, '0')}:${date[1].getMinutes().toString().padStart(2, '0')}`
    }
  }
  if (props.range) {
    if (date.length === 1) {
      const day = date[0].getDate()

      const month = beMonth.find(i => i.id == date[0].getMonth() + 1).nameMonth

      const year = date[0].getFullYear() // ไม่ต้องบวก 543

      return `${day} ${month} ${year} -`
    } else if (date.length === 2) {
      const day = date[0].getDate()

      const month = beMonth.find(i => i.id == date[0].getMonth() + 1).nameMonth

      const year = date[0].getFullYear() // ไม่ต้องบวก 543
      const day2 = date[1].getDate()

      const month2 = beMonth.find(i => i.id === date[1].getMonth() + 1).nameMonth

      const year2 = date[1].getFullYear() // ไม่ต้องบวก 543

      return `${day} ${month} ${year + 543} - ${day2} ${month2} ${year2 + 543}`
    }
  }
  if (props.datetimePicker) {
    const day = date.getDate()
    const month = date.getMonth() + 1
    const year = date.getFullYear()

    return `${day} ${beMonth.find(i => i.id == month).nameMonth} ${
      year + 543
    } ${date.getHours().toString().padStart(2, '0')}:${date
      .getMinutes()
      .toString()
      .padStart(2, '0')}`
  }
  if (props.timePicker) {
    return `${date.getHours().toString().padStart(2, '0')}:${date
      .getMinutes()
      .toString()
      .padStart(2, '0')}`
  } else {
    const day = date.getDate()
    const month = date.getMonth() + 1
    const year = date.getFullYear()

    return `${day} ${beMonth.find(i => i.id == month).nameMonth} ${year + 543}`
  }
}

const { modelValue: _, ..._inputProps } = VInput.filterProps(props)
const inputProps = reactive({})

const _fieldProps = props
const fieldProps = reactive({})

computeInputFieldProps()
watch(props, computeInputFieldProps)

function computeInputFieldProps() {
  const inputPropKeys = Object.keys(_inputProps)

  inputPropKeys.forEach(key => (inputProps[key] = props[key]))

  const fieldPropKeys = Object.keys(_fieldProps)

  fieldPropKeys.forEach(key => (fieldProps[key] = props[key]))
}

const formatDateTH = val => {
  const dateTimeString = val
  const date = new Date(dateTimeString)

  const dateInGMTPlus7 = date.toLocaleString('th', {
    timeZone: 'Asia/Bangkok'
  })

  const dateComponents = dateInGMTPlus7.split(' ')[0].split('/')
  const timeComponents = dateInGMTPlus7.split(' ')[1]

  if (props.datetimePicker) {
    return `${dateComponents[2] - 543}-${dateComponents[1]
      .toString()
      .padStart(2, '0')}-${dateComponents[0].toString().padStart(2, '0')}T${timeComponents}`
  } else {
    return `${dateComponents[2] - 543}-${dateComponents[1]
      .toString()
      .padStart(2, '0')}-${dateComponents[0].toString().padStart(2, '0')}`
  }
}

const adjustYear = dateString => {
  const [year, month, day] = dateString.split('-')

  return `${Number(year) + 543}-${month}-${day}`
}

const emitModelValue = val => {
  if (!props.timePicker && !props.yearPicker && !props.monthPicker && !props.range && val)
    val = formatDateTH(val)

  if (!props.timePicker && !props.yearPicker && !props.monthPicker && props.range && val) {
    val[0] = formatDateTH(val[0])
    val[1] = val[1] ? formatDateTH(val[1]) : val[0]

    // เพิ่ม logic บวก 543 ใน array
    val[0] = val[0]
    val[1] = val[1]
  }
  if (props.yearPicker && props.range && !props.monthPicker) {
    if (val) val[1] = val[1] ? val[1] : val[0]
  }
  if (props.yearPicker) {
    if (val) {
      if (!Array.isArray(val)) {
        val = String(Number(val) + 543)
      } else {
        val[0] = val[0] + 543
        val[1] = val[1] + 543
      }
    }
  }
  emit('update:modelValue', val)
}

const getDynamicClass = computed(() => {
  return props.iconStyleFlag ? 'icon-style-enabled' : ''
})

const disabledTimes = [
  { hours: 15, minutes: '*' }, // disable full hour
  { hours: 16, minutes: 15 },
  { hours: 16, minutes: 20 },
  { hours: 17, minutes: 30 },
];
const rangeDisabledTimes = [
  [
    { hours: 12, minutes: '*' },
    { hours: 9, minutes: 10 },
  ],
  disabledTimes,
];


</script>

<template>
  <VInput
    v-bind="{ ...inputProps, ...rootAttrs }"
    :model-value="modelValue"
    :hide-details="props.hideDetails"
    class="position-relative"
  >
    <template #default="{ isDirty, isValid, isReadonly }">
      <!-- v-field -->
      <VField
        v-bind="fieldProps"
        :active="focused || isDirty.value || isCalendarOpen"
        :focused="focused || isCalendarOpen"
        role="textbox"
        :dirty="isDirty.value || props.dirty"
        :error="isValid.value === false"
        class="datePicker3"
        :class="getDynamicClass"
        @click:clear="onClear"
      >
        <template #default="{ props: vFieldProps }">
          <div :class="vFieldProps.class">
            <template v-if="datetimePicker">
              <Datepicker
                :uid="vFieldProps.id"
                :min-date="mindate"
                :disabled-week-days="disableddays"
                :teleport="teleport"
                locale="th"
                :format="format"
                month-name-format="long"
                :model-value="modelValue"
                :range="range"
                utc
                :disabled="isReadonly.value"
                :dark="vuetifyTheme.global.current.value.dark || themeDark"
                :placeholder="placeholder"
                :year-picker="yearPicker"
                :month-picker="monthPicker"
                :disabled-dates="disabledDates"
                :teleport-center="teleportCenter"
                @update:model-value="emitModelValue"
             
                @cleared="onClearValue"
              >
                <template #year="{ year }">
                  {{ parseInt(year) + 543 }}
                </template>
                <template #year-overlay-value="{ text, value }">
                  {{ value + 543 }}
                </template>
                <template #action-row="{ closePicker, selectDate }">
                  <VCol cols="12" class="d-flex flex-wrap justify-end">
                    <div class="demo-space-x">
                      <VBtn color="blue-600" class="mt-0" @click="selectDate">ตกลง</VBtn>
                      <VBtn color="error-300" class="mt-0" @click="closePicker">ยกเลิก</VBtn>
                    </div>
                  </VCol>
                </template>
              </Datepicker>
            </template>
            <template v-else>
              <Datepicker
                :uid="vFieldProps.id"
                :min-date="mindate"
                :disabled-week-days="disableddays"
                :teleport="teleport"
                locale="th"
                :format="format"
                month-name-format="long"
                :model-value="modelValue"
                :enable-time-picker="timePicker"
                :time-picker="timePicker"
                :range="range"
                utc
                :disabled="isReadonly.value"
                :dark="vuetifyTheme.global.current.value.dark || themeDark"
                :placeholder="placeholder"
                :year-picker="yearPicker"
                :month-picker="monthPicker"
                :disabled-dates="disabledDates"
                :teleport-center="teleportCenter"
                @update:model-value="emitModelValue"
                @cleared="onClearValue"
              >
                <template #input-icon>
                  <VIcon
                    size="30"
                    class="py-1"
                    :icon="timePicker ? 'mdi-clock-outline' : 'mdi-calendar-blank'"
                  />
                </template>
                <template #year="{ year }">
                  {{ parseInt(year) + 543 }}
                </template>
                <template #year-overlay-value="{ text, value }">
                  {{ value + 543 }}
                </template>
                <template #action-row="{ closePicker, selectDate }">
                  <VCol cols="12" class="d-flex flex-wrap justify-end">
                    <div class="demo-space-x">
                      <VBtn color="blue-600" class="mt-0" @click="selectDate">ตกลง</VBtn>
                      <VBtn color="error-300" class="mt-0" @click="closePicker">ยกเลิก</VBtn>
                    </div>
                  </VCol>
                </template>
              </Datepicker>
            </template>
          </div>
        </template>
      </VField>
    </template>
  </VInput>
</template>

<style>
/* Menu Styles */
.dp__menu {
  font-family: "K2D", sans-serif;
}

.dp__input {
  border-radius: 8px;
  background-color: transparent;
  font-family: "K2D", sans-serif;
  min-block-size: 56px;
}

.datePicker3.icon-style-enabled .dp__input {
  border-radius: 27px !important;
}

.dp__input:hover {
  border-color: rgb(var(--v-theme-primary));
}

/* Dark Theme Adjustments */
.datePicker3.icon-style-enabled .v-theme--dark .dp__input {
  color: rgb(var(--v-theme-on-surface)) !important;
}

.v-theme--dark .dp__input {
  border-radius: 8px !important;
  color: rgb(var(--v-theme-on-surface)) !important;
}

.v-theme--dark .dp__disabled {
  background: rgb(var(--v-theme-grey-secondary)) !important;
  color: rgb(var(--v-theme-grey-50));
}

/* DatePicker Wrapper */
.datePicker3 .v-input {
  border: none; /* Removes the outer border */
  box-shadow: none; /* Removes shadow effects */
}

.datePicker3 .v-field {
  border: none; /* Removes the inner field border */
  box-shadow: none; /* Removes shadow effects */
  outline: none; /* Removes focus outline */
}

.datePicker3.icon-style-enabled .v-input__control {
  padding: 10px; /* Adjust padding */
  border: none; /* Remove border from control */
  border-radius: 8px; /* Rounded corners */
  background-color: var(--v-primary-base); /* Use primary color as background */
  color: #fff; /* High contrast text */
}

.datePicker3 .v-field__input {
  padding: 0; /* Remove extra padding */
  color: #fff; /* Ensure text contrast */
  font-size: 16px; /* Adjust font size for readability */
  font-weight: 500; /* Increase font weight for better clarity */
}

/* Icon Styling (เปิดด้วย flag iconStyleFlag) */
.datePicker3.icon-style-enabled .v-icon {
  color: #fff;
  margin-inline: 12px 8px;
}

.datePicker3.icon-style-enabled .dp__icon {
  fill: rgb(255 255 255) !important;
  stroke: rgb(255 254 254) !important;
}

/* Hover and Focus States */
.datePicker3 .v-input:hover,
.datePicker3 .v-input:focus-within,
.datePicker3 .v-field:hover,
.datePicker3 .v-field:focus-within {
  border-color: transparent; /* Prevent borders on hover/focus */
  box-shadow: none; /* Prevent shadow effects */
}

/* Action Row Styling */
.datePicker3 .action-row {
  display: flex;
  flex-direction: column;
  align-items: center;
  inline-size: 100%;
}

/* Custom Month and Year Components */
.datePicker3 .custom-month-year-component {
  display: flex;
  align-items: center;
  margin-block: 0;
  margin-inline: auto;
}

/* Input Field Focus and Hover Overrides */
.datePicker3 .v-input__control:hover {
  border-color: transparent; /* Prevent border on hover */
}

.datePicker3 .v-input__control:focus-within {
  border-color: transparent; /* Prevent border on focus */
}

/* Dark Theme Adjustments for Input Control */
.v-theme--dark .datePicker3 .v-input__control {
  background-color: #30334e; /* Dark mode background color */
  color: #fff; /* Ensure text contrast in dark mode */
}

/* Final Adjustments for Text Alignment */
.datePicker3 .v-field__control {
  padding: 0;
}

/* เฉพาะ class input#dp-input-input-6 (จาก inspect) */
.datePicker3.icon-style-enabled .dp__input_icon_pad {
  color: inherit !important;
  padding-inline-start: 50px;
}

.datePicker3.icon-style-enabled input::placeholder {
  color: rgba(255, 255, 255) !important;
  font-size: 16px;
}
</style>
