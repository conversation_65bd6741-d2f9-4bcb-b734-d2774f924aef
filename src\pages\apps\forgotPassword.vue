<script setup lang="ts">
import { VCol, VRow } from 'vuetify/lib/components/index.mjs'
import Footer from '@/layouts/components/Footer.vue'
import { useAppAbility } from '@/plugins/casl/useAppAbility'
import { useAuth } from '@/store/useAuth'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import bgLogin from '@images/pages/bgLogin.png'
import { emailValidator, requiredValidator } from '@validators'

const callAxios = useAxios()

const Swal = useSwal()
const callAuth = useAuth()
const AppAbility = useAppAbility()
const router = useRouter()
const refVForm = ref()

interface FormType {
  email: string
}

const form: Ref<FormType> = ref({
  email: ''
})

const isCardSendEmail = ref(true)
const isCardSendEmailAgain = ref(false)

const countdown = ref(300) // เริ่มต้นที่ 300 วินาที (5 นาที)

const startCountdown = () => {
  const interval = setInterval(() => {
    if (countdown.value > 0) countdown.value--
    else clearInterval(interval) // หยุดการนับถอยหลังเมื่อถึง 0
  }, 1000) // อัปเดตทุก ๆ 1 วินาที
}

// เฝ้าดู isCardRegisterAgain
watch(
  () => isCardSendEmailAgain.value,
  newValue => {
    if (newValue) startCountdown() // เรียก startCountdown เมื่อค่าเปลี่ยนเป็น true
  }
)

const AddForgotPassword = async () => {
  try {
    const confirmResponse = await Swal.ApproveConfirmEmail()

    if (confirmResponse) {
      const data = form.value

      const response = await callAxios.RequestPost('/Authenticate/ForgotPassword', data)

      if (response.status === 200) {
        // รีเซ็ต countdown กลับไปที่ค่าเริ่มต้น
        countdown.value = 300 // ตั้งเวลาใหม่เป็น 5 นาที (300 วินาที)
        startCountdown() // เริ่มการนับถอยหลังใหม่

        Swal.ConditionSuccessText(
          response.data.message || 'Successfully resent the confirmation email.'
        )
        isCardSendEmail.value = false
        isCardSendEmailAgain.value = true
      } else {
        // Handle non-200 responses
        Swal.AddConditionFailText(response.data?.message || 'Request failed. Please try again.')
      }
    }
  } catch (error) {
    // Handle request errors
    Swal.AddConditionFailText(
      error.response?.data?.message || 'Bad request. Please check your input and try again.'
    )
  }
}

const onSumbit = () => {
  refVForm.value?.validate().then(({ valid: isValid }) => {
    if (isValid) AddForgotPassword()
  })
}

// แปลงวินาทีเป็นรูปแบบ mm:ss
const formattedTime = computed(() => {
  const minutes = Math.floor(countdown.value / 60)
    .toString()
    .padStart(2, '0')

  const seconds = (countdown.value % 60).toString().padStart(2, '0')

  return `${minutes}:${seconds}`
})

const goBack = async () => {
  const confirmed = await Swal.ApproveCancel()
  if (!confirmed) return
  router.go(-1)
}
</script>

<template>
  <HeaderPublic />

  <div class="auth-wrapper d-flex">
    <VContainer>
      <VRow class="pt-10">
        <VCol cols="12" justify="center">
          <VCard v-if="isCardSendEmail" class="auth-card login-card pa-2 pt-7">
            <VCardText class="pt-2">
              <div class="text-center mb-4">
                <img src="@images/forgot.png" alt="Website logo" />
              </div>
              <h4 class="text-center">ลืมรหัสผ่าน กรุณากรอกอีเมลที่ใช้ในการสมัคร</h4>

              <!--
                <p class="mb-0">
                <span class="text-capitalize">{{ themeConfig.app.title }}</span>
                </p>
              -->
            </VCardText>
            <VCardText>
              <VForm ref="refVForm" @submit.prevent="onSumbit">
                <VRow justify="center" class="align-center">
                  <VCol cols="12" md="5">
                    <label>
                      ชื่อผู้ใช้งาน (อีเมลหน่วยงาน) :
                      <small class="text-error">*</small>
                    </label>
                    <VTextField
                      v-model="form.email"
                      :counter="100"
                      :maxlength="100"
                      density="compact"
                      :rules="[requiredValidator, emailValidator]"
                      placeholder="ตัวอย่าง : <EMAIL>"
                    />
                  </VCol>
                  <VCol cols="12" md="1">
                    <!-- login button -->
                    <VBtn color="blue-200" type="submit">ตกลง</VBtn>
                  </VCol>
                  <VCol cols="12" class="d-flex flex-wrap justify-center">
                    <div class="demo-space-x">
                      <VBtn color="error-300" @click="goBack">ยกเลิก</VBtn>
                    </div>
                  </VCol>
                </VRow>
              </VForm>
            </VCardText>
          </VCard>

          <VCard v-if="isCardSendEmailAgain" class="auth-card login-card pa-2 pt-7 mb-5">
            <VCardText class="text-center">
              <img src="@images/forgot.png" alt="RegisterSuccess" />
              <h3 class="text-h6">
                ลืมรหัสผ่าน
                <br />
                ระบบส่งลิงก์ไปทาง E-mail
                <span class="text-blue-200">{{ form.email }}</span>
                เรียบร้อยแล้ว
              </h3>
              <p v-if="countdown > 0">ส่งลิงก์ยืนยันซ้ำอีกครั้ง {{ formattedTime }}</p>
              <p v-else>
                ส่งลิงก์ยืนยันซ้ำอีกครั้ง
                <VBtn color="error-200" class="text-dark" type="button" @click="AddForgotPassword">
                  ส่งรหัสยืนยัน
                  <VTooltip open-delay="500" location="top" activator="parent">
                    ส่งรหัสยืนยัน
                  </VTooltip>
                </VBtn>
              </p>

              <!-- <p>ส่งลิงก์ยืนยันซ้ำอีกครั้ง {{ formattedTime }}</p> -->

              <VBtn color="error-300 mt-4" @click="goBack">ยกเลิก</VBtn>
            </VCardText>
          </VCard>
        </VCol>

        <VCol cols="12">
          <Footer />
        </VCol>
      </VRow>
    </VContainer>

    <VImg :src="bgLogin" role="presentation" class="d-none d-md-block auth-footer-mask" />
    <div class="auth-logo" />
    <GovWebsiteAccess />
  </div>
</template>

<style lang="css">
@import '@public/assets/css/style.css';
@import '@public/assets/css/skin/skin-2.css';
</style>

<style lang="css" scoped>
.v-container {
  position: relative;
  z-index: 1;
}

.auth-footer-mask {
  inset-block-end: 0% !important;
  /* เปลี่ยนค่าเมื่อหน้าจอเล็กลง */
}
</style>

<style lang="scss">
@use '@core/scss/template/pages/page-auth.scss';
</style>

<style lang="scss" scoped>
.layout-blank {
  .auth-wrapper {
    min-block-size: calc(var(--vh, 1vh) * 0);

    &::before {
      content: '';
      display: block;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 50%;
      background: linear-gradient(
        180deg,
        rgb(var(--v-theme-blue-100)) 0%,
        rgba(255, 255, 255, 0) 100%
      );
      /* Example background */
      z-index: 1;
      /* Make sure it's behind other content */
    }
  }

  .auth-card {
    z-index: 1 !important;
  }
}
</style>

<route lang="yaml">
meta:
  layout: blank
  action: read
  subject: Auth
  redirectIfLoggedIn: true
</route>
