.slick-initialized .slick-slide {
  /* padding-left: 15px; */
  /* padding-right: 15px; */
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  padding-left: calc(var(--bs-gutter-x) * 0.5);
}

._point .point-top .block-table-cell,
.slick-slider-content .point-top .block-table-cell,
.promo-box .point-top .block-table-cell,
.home4_banner_big .point-top .block-table-cell,
.img-box .point-top .block-table-cell {
  vertical-align: top;
}

._point .point-center .block-table-cell,
.slick-slider-content .point-center .block-table-cell,
.promo-box .point-center .block-table-cell,
.home4_banner_big .point-center .block-table-cell,
.img-box .point-center .block-table-cell {
  vertical-align: middle;
}

._point .point-bottom .block-table-cell,
.slick-slider-content .point-bottom .block-table-cell,
.promo-box .point-bottom .block-table-cell,
.home4_banner_big .point-bottom .block-table-cell,
.img-box .point-bottom .block-table-cell {
  vertical-align: bottom;
}

._point .point-center .block-table-cell,
.slick-slider-content .point-center .block-table-cell,
.promo-box .point-center .block-table-cell,
.home4_banner_big .point-center .block-table-cell,
.img-box .point-center .block-table-cell {
  text-align: center;
}

._point .point-left .block-table-cell,
.slick-slider-content .point-left .block-table-cell,
.promo-box .point-left .block-table-cell,
.home4_banner_big .point-left .block-table-cell,
.img-box .point-left .block-table-cell {
  text-align: left;
}

._point .point-right .block-table-cell,
.slick-slider-content .point-right .block-table-cell,
.promo-box .point-right .block-table-cell,
.home4_banner_big .point-right .block-table-cell,
.img-box .point-right .block-table-cell {
  text-align: right;
}

._point .point-top.point-center .block-table-cell,
.slick-slider-content .point-top.point-center .block-table-cell,
.promo-box .point-top.point-center .block-table-cell,
.home4_banner_big .point-top.point-center .block-table-cell,
.img-box .point-top.point-center .block-table-cell {
  vertical-align: top;
  text-align: center;
}

._point .point-center-horizontal .block-table-cell,
.slick-slider-content .point-center-horizontal .block-table-cell,
.promo-box .point-center-horizontal .block-table-cell,
.home4_banner_big .point-center-horizontal .block-table-cell,
.img-box .point-center-horizontal .block-table-cell {
  text-align: center;
}

._point .point-center-vertical .block-table-cell,
.slick-slider-content .point-center-vertical .block-table-cell,
.promo-box .point-center-vertical .block-table-cell,
.home4_banner_big .point-center-vertical .block-table-cell,
.img-box .point-center-vertical .block-table-cell {
  vertical-align: middle;
}

._point .right-offset .block-table,
.slick-slider-content .right-offset .block-table,
.promo-box .right-offset .block-table,
.home4_banner_big .right-offset .block-table,
.img-box .right-offset .block-table {
  width: 60%;
  margin-left: 40%;
}

._point .right-offset .block-table-cell,
.slick-slider-content .right-offset .block-table-cell,
.promo-box .right-offset .block-table-cell,
.home4_banner_big .right-offset .block-table-cell,
.img-box .right-offset .block-table-cell {
  text-align: left;
}

._point .left-offset .block-table,
.slick-slider-content .left-offset .block-table,
.promo-box .left-offset .block-table,
.home4_banner_big .left-offset .block-table,
.img-box .left-offset .block-table {
  width: 60%;
}

._point .left-offset .block-table-cell,
.slick-slider-content .left-offset .block-table-cell,
.promo-box .left-offset .block-table-cell,
.home4_banner_big .left-offset .block-table-cell,
.img-box .left-offset .block-table-cell {
  text-align: left;
}

._point .point-left.text-center .block-table-cell,
.slick-slider-content .point-left.text-center .block-table-cell,
.promo-box .point-left.text-center .block-table-cell,
.home4_banner_big .point-left.text-center .block-table-cell,
.img-box .point-left.text-center .block-table-cell,
._point .left-offset.text-center .block-table-cell,
.slick-slider-content .left-offset.text-center .block-table-cell,
.promo-box .left-offset.text-center .block-table-cell,
.home4_banner_big .left-offset.text-center .block-table-cell,
.img-box .left-offset.text-center .block-table-cell {
  text-align: center;
}

.header-menu-product .slick-arrow {
  background: none !important;
}

.header-menu-product .slick-arrow {
  margin-top: -45px;
}

@media (min-width: 769px) {
  .header-menu-product .slick-next {
    right: -9px !important;
  }

  .header-menu-product .slick-prev {
    right: 30px !important;
  }
}

.menu-vertical nav .carouselTab .slick-slide {
  padding-left: 12px;
  padding-right: 12px;
}

.tab-aside .slick-list {
  display: block;
}

.product-scroll-image .slick-dots {
  top: 50%;
  margin-top: -101px;
  width: auto;
  bottom: auto;
}

.product-scroll-image .slick-dots li {
  display: block;
  margin: 13px 30px;
  text-align: left;
  cursor: inherit;
}

.product-scroll-image .slick-dots li button {
  display: inline-block;
}

.mobileGallery-product .slick-slide {
  position: relative;
}

@media (min-width: 1025px) {
  .slick-slider-indent {
    padding-top: 4px;
  }
}

.slick-slider img {
  width: initial;
  /*height: auto*/
}

.slick-slider-content img {
  width: 100%;
  /*height: auto*/
}

.slick-slider-content .slick-slide {
  position: relative;
}

.slick-slider-content .block-table-cell {
  text-align: center;
}

.slick-slider-content .description {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  padding: 8.9% 11% 10.7%;
  color: #fff;
}

.slick-slider-content .description .title:not([class^='title color-']) {
  color: #fff;
}

.slick-slider-content .description .block-table-cell > *:nth-child(1) {
  margin-top: 0 !important;
}

.slick-slider-content .description .title {
  font-size: 46px;
  line-height: 57px;
  font-weight: 500;
}

@media (min-width: 1676px) {
  .slick-slider-content .description .title.font-size80 {
    font-size: 80px;
    line-height: 100px;
  }
}

.slick-slider-content .description p:not([class^='color-']) {
  color: #fff;
}

.slick-slider-content .description p {
  font-size: 36px;
  line-height: 47px;
  margin: 2px 0 0 0;
  font-weight: 500;
}

.slick-slider-content .description .btn {
  margin-top: 39px;
  position: relative;
  z-index: 7;
}

.slick-slider-content .description img {
  width: auto;
  height: auto;
  display: inline-block;
}

.slick-slider-content .description .extra-img {
  margin-left: 6%;
}

.slick-slider-content .slick-dots {
  bottom: 11.8%;
}

.slick-slider-content .slick-dots li {
  margin: 0 15px;
}

@media (max-width: 1299px) {
  .slick-slider-content .description {
    padding: 6%;
  }

  .slick-slider-content .description .extra-img {
    display: none !important;
  }

  .slick-slider-content .description .title {
    font-size: 36px;
    line-height: 42px;
  }

  .slick-slider-content .description p {
    font-size: 27px;
    line-height: 33px;
  }

  .slick-slider-content .description br {
    display: none;
  }

  .slick-slider-content .description .btn {
    margin-top: 20px;
  }
}

@media (max-width: 1024px) {
  .slick-slider-content .description {
    padding: 5%;
  }

  .slick-slider-content .description .title {
    font-size: 26px;
    line-height: 30px;
  }

  .slick-slider-content .description p {
    font-size: 22px;
    line-height: 28px;
  }

  .slick-slider-content .description br {
    display: none;
  }

  .slick-slider-content .description .btn {
    margin-top: 20px;
    height: 40px;
    padding-left: 18px;
    padding-right: 18px;
    font-size: 14px;
    line-height: 49px;
  }

  .slick-slider-content .slick-dots {
    bottom: 6.3%;
  }
}

@media (max-width: 537px) {
  .slick-slider-content .description .title {
    font-size: 15px;
    line-height: 22px;
  }

  .slick-slider-content .description p {
    font-weight: 300;
    font-size: 16px;
    line-height: 25px;
  }
}

@media (max-width: 426px) {
  .slick-slider-content .description {
    padding: 0% 5% 5% 5%;
  }

  .slick-slider-content .description p {
    display: none;
  }

  .slick-slider-content .description .btn {
    margin-top: 10px;
    font-size: 11px;
    line-height: 16px;
    height: 32px;
  }

  .slick-slider-content .slick-dots {
    bottom: 3.3%;
  }
}

.product-sliderVert-nav .slick-slide {
  padding: 10px 0;
}

.product-sliderVert-nav .slick-slide img {
  -moz-opacity: 0.3;
  -khtml-opacity: 0.3;
  -webkit-opacity: 0.3;
  opacity: 0.3;
}

.product-sliderVert-nav .slick-current img {
  -moz-opacity: 1;
  -khtml-opacity: 1;
  -webkit-opacity: 1;
  opacity: 1;
}

.product-slider-nav {
  position: relative;
  margin-left: -10px;
  margin-right: -10px;
  backface-visibility: hidden;
}

.product-slider-nav .slick-slide {
  padding: 0 10px;
}

.product-slider-nav .slick-slide a {
  display: inline-block;
  position: relative;
}

.product-slider-nav .slick-slide a:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.55);
  z-index: 7;
  transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
}

.product-slider-nav .slick-current a:before,
.product-slider-nav .slick-slide:hover a:before {
  background: rgba(255, 255, 255, 0);
}

.custom-layout:not(.indent-col-none):not(.slick-slider) {
  margin-top: -50px;
  font-size: 0;
  padding-bottom: 1px;
}

.custom-layout:not(.indent-col-none):not(.slick-slider) .promo-box,
.custom-layout:not(.indent-col-none):not(.slick-slider) .services-block-border,
.custom-layout:not(.indent-col-none):not(.slick-slider) .box-product-info {
  margin-top: 50px;
}

.custom-layout:not(.indent-col-none):not(.slick-slider) [class*='col-'] {
  float: none;
  display: inline-block;
  vertical-align: top;
}

.slick-slider .promo-box .description:not(.point-left):not(.point-right) .title,
.slick-slider .promo-box .description:not(.point-left):not(.point-right) p {
  padding-left: 20px;
  padding-right: 20px;
}

.carousel-brands .slick-slide {
  text-align: center;
}

.carousel-brands .slick-prev {
  margin-left: -15px;
}

@media (min-width: 790px) {
  .carousel-brands .slick-prev {
    margin-left: -36px;
  }
}

.carousel-brands .slick-next {
  margin-right: -15px;
}

@media (min-width: 790px) {
  .carousel-brands .slick-next {
    margin-left: -36px;
  }
}

.carousel-brands:not(.slick-slider) > div {
  float: left;
}

@media (min-width: 1025px) {
  .carousel-brands .slick-arrow {
    display: none !important;
  }

  .carousel-brands:hover .slick-arrow {
    display: block !important;
  }
}

.testimonialsAsid .slick-dots {
  position: relative;
}

.testimonialsAsid .slick-slide {
  padding-bottom: 26px;
  text-align: center;
}

.testimonialsAsid .slick-dots {
  margin: 5px 0 0 0;
}

.testimonialsAsid .slick-dots li.slick-active button {
  background: none;
}

.subcategory-listing:not(.slick-slider) {
  font-size: 0;
}

.subcategory-listing:not(.slick-slider) > div {
  float: none;
  display: inline-block;
  vertical-align: top;
}

@media (min-width: 1298px) {
  .hover-product .carousel-products-1 .slick-list,
  .hover-product .carousel-products-2 .slick-list,
  .hover-product .carousel-products-3 .slick-list {
    padding-bottom: 250px;
    margin-bottom: -250px;
    padding-left: 20px;
    margin-left: -20px;
    padding-right: 20px;
    margin-right: -20px;
  }
}

@media (max-width: 1298px) {
  .carousel-products-1 .slick-list {
    padding-left: 0px;
    margin-left: -0px;
    padding-right: 0px;
    margin-right: -0px;
  }
}

.header-menu-product .slick-arrow {
  margin-top: -42px !important;
}

.tab-content.tab-content-carusel,
.carouselTab-col-4 .slick-slide {
  margin: -2px 0 0 0;
}

.carouselTab .slick-slide,
.carouselTab-col-4 .slick-slide {
  padding-left: 25px;
  padding-right: 25px;
}

@media (min-width: 1298px) {
  .carousel-products:hover {
    z-index: 7;
  }

  .hover-product .carousel-products:hover .slick-list,
  .hover-product .carouselTab-col-4:hover .slick-list {
    padding-bottom: 250px;
    margin-bottom: -250px;
    padding-left: 20px;
    margin-left: -20px;
    padding-right: 20px;
    margin-right: -20px;
  }
}

.slick-list {
  position: inherit !important;
}

@media (max-width: 1298px) {
  .carousel-products .slick-list {
    padding-left: 0px;
    margin-left: -0px;
    padding-right: 0px;
    margin-right: -0px;
  }
}

.product-col-image .product-images-carousel-vertical .slick-slider {
  width: 100%;
  padding: 0;
  margin: 0;
  border: none;
}

.product-col-image .product-images-carousel-vertical .slick-slider .slick-slide {
  padding-bottom: 20px;
}

.product-col-image
  .product-images-carousel-vertical
  .slick-slider
  .slick-slide
  .video-link-product {
  margin-top: 1px;
}

@media (max-width: 1279px) {
  .product-col-image .product-images-carousel-vertical .slick-slider .slick-slide {
    padding-bottom: 16px;
  }

  .product-col-image
    .product-images-carousel-vertical
    .slick-slider
    .slick-slide
    .video-link-product {
    margin-top: 0px;
  }
}

@media (max-width: 1024px) {
  .product-col-image .product-images-carousel-vertical .slick-slider .slick-slide {
    padding-bottom: 22px;
  }
}

.product-col-image .product-images-carousel-vertical .slick-slider .slick-slide img {
  -moz-opacity: 0.3;
  -khtml-opacity: 0.3;
  -webkit-opacity: 0.3;
  opacity: 0.3;
}

.product-col-image .product-images-carousel-vertical .slick-slider .slick-slide:hover img {
  -moz-opacity: 0.6;
  -khtml-opacity: 0.6;
  -webkit-opacity: 0.6;
  opacity: 0.6;
}

.product-col-image .product-images-carousel-vertical .slick-slider .zoomGalleryActive img {
  -moz-opacity: 1;
  -khtml-opacity: 1;
  -webkit-opacity: 1;
  opacity: 1;
}

.product-col-image .product-images-carousel-vertical .slick-arrow {
  top: auto;
  bottom: -37px;
  margin-top: 0;
}

@media (max-width: 1299px) {
  .product-col-image .product-images-carousel-vertical .slick-prev {
    margin-left: -3px;
  }

  .product-col-image .product-images-carousel-vertical .slick-next {
    margin-right: -3px;
  }
}

@media (max-width: 1024px) {
  .product-col-image .product-images-carousel-vertical .slick-prev {
    margin-left: 0px;
  }

  .product-col-image .product-images-carousel-vertical .slick-next {
    margin-right: 5px;
  }
}

@media (max-width: 1279px) {
  .product-col-image .product-images-carousel-vertical .slick-slide .video-link-product {
    margin-top: 0px;
  }
}

.product-images-carousel ul .slick-list {
  margin-right: -25px;
}

.product-images-carousel img {
  width: 100%;
}

.product-images-carousel .slick-slider .slick-arrow {
  -moz-opacity: 0;
  -khtml-opacity: 0;
  -webkit-opacity: 0;
  opacity: 0;
}

.product-images-carousel .slick-slider:hover .slick-arrow {
  -moz-opacity: 1;
  -khtml-opacity: 1;
  -webkit-opacity: 1;
  opacity: 1;
}

.post .title-block .post-img .slick-slider,
.post .title-block .post-img .slick-slide {
  overflow: hidden;
}

.slider-blog-fluid .slick-dots {
  bottom: 16.2%;
}

.blog-fluid .slick-slide {
  padding-bottom: 26px;
  text-align: center;
}

.slick-prev {
  left: 0px;
}

[dir='rtl'] .slick-prev {
  left: auto;
  right: 0px;
}

.slick-prev:before {
  content: '\e5cb';
  /* IE 9 */
}

[dir='rtl'] .slick-prev:before {
  content: '\e8e4';
}

.slick-next {
  right: 0px;
}

[dir='rtl'] .slick-next {
  left: 0px;
  right: auto;
}

.slick-next:before {
  content: '\e5cc';
}

[dir='rtl'] .slick-next:before {
  content: '\e8e4';
}

.slick-dots {
  /*position: absolute;*/
  margin-top: 15px;
  /* bottom: 10.3%; */
  list-style: none;
  display: block;
  text-align: center;
  padding: 0;
  width: 100%;
}

.slick-dots li {
  position: relative;
  display: inline-block;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.slick-dots li button {
  border: 1px solid #e9cb69;
  display: block;
  height: 20px;
  width: 20px;
  outline: none;
  line-height: 0px;
  font-size: 0px;
  padding: 0 5px;
  color: transparent;
  cursor: pointer;
  background: transparent;
  position: relative;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
  transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
}

.slick-dots li button:focus {
  outline: none;
}

.slick-dots li button:hover {
  background: #e9cb69;
  outline: none;
}

.slick-dots li button:before {
  border: 0;
  background: transparent;
  display: block;
  height: 10px;
  width: 0px;
  outline: none;
  line-height: 0px;
  font-size: 0px;
  padding: 0 5px;
  color: transparent;
  cursor: pointer;
  background: #c9a766;
  position: relative;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
  transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
}

.slick-dots li.slick-active button {
  background: transparent;
  position: relative;
}

.slick-dots li.slick-active button:before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  background: #e9cb69;
  width: 100%;
  height: 100%;
  /* -webkit-transform: scale(0.45); */
  -moz-transform: scale(0.45);
  -ms-transform: scale(0.45);
  -o-transform: scale(0.45);
  /* transform: scale(0.45); */
}

@media (max-width: 426px) {
  .slick-dots {
    bottom: 5%;
  }

  .slick-dots li {
    margin: 0 9px;
  }
}

@media (min-width: 791px) {
  .slick-arrow-top .slick-prev {
    top: 0;
    left: auto;
    right: 59px;
  }

  .slick-arrow-top .slick-next {
    top: 0;
    right: 8px;
  }
}

.slick-arrow-1 .slick-arrow {
  background: none !important;
}

.slick-arrow-1 .slick-prev:before,
.slick-arrow-1 .slick-next:before {
  font-size: 50px;
}

.slick-arrow {
  pointer-events: auto;
}

.slick-arrow.slick-disabled {
  pointer-events: none;
  opacity: 0.6;
}

.slick-arrow {
  display: block;
  height: 50px;
  width: 50px;
  cursor: pointer;
  background: transparent;
  border: 0px solid #000000 !important;
  padding: 0;
  border: none;
  outline: none;
  position: absolute;
  z-index: 1;
  top: 50%;
  margin-top: -25px;
  font-size: 0;
  border-radius: 0%;
  transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
}

@media (max-width: 790px) {
  .products-mobile-arrow .slick-arrow {
    top: 36% !important;
    margin-top: -22px !important;
  }
}

@media (max-width: 630px) {
  .products-mobile-arrow .slick-arrow {
    top: 33.7% !important;
    margin-top: -22px !important;
  }
}

@media (max-width: 490px) {
  .carousel-products-mobile .slick-arrow,
  .carousel-products-mobile-md .slick-arrow {
    top: 50%;
  }
}

.slick-arrow:before {
  display: block;
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 30px;
  line-height: 50px;
  color: #ffffff;
  background: rgb(8 25 51 / 50%);
  opacity: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
}

.slick-arrow:hover {
  outline: none;
}

.slick-arrow:hover:before {
  /*opacity: 0.8;*/
  color: #ffffff;
  background: #081933;
}

.slick-arrowslick-disabled:before {
  opacity: 1;
}

.slick-arrow-top .slick-arrow,
.slick-arrow-top1 .slick-arrow,
.slick-arrow-top2 .slick-arrow {
  overflow: hidden;
}

.slick-arrow-top .slick-arrow {
  margin-top: -52px;
}

.slick-arrow-top1 .slick-arrow {
  margin-top: -38px;
}

.slick-arrow-top2 .slick-arrow {
  margin-top: -58px;
}

.slick-arrow-top-bottom .slick-prev {
  top: 0;
  width: 100%;
}

.slick-arrow-top-bottom .slick-prev:before {
  content: '\e5cb';
}

.slick-arrow-top-bottom .slick-next {
  top: auto;
  bottom: 0;
  width: 100%;
}

.slick-arrow-top-bottom .slick-next:before {
  content: '\e8e4';
}

.vertical-carousel {
  padding-bottom: 64px;
}

.vertical-carousel .slick-arrow {
  top: inherit;
  bottom: 0;
}

.no-zoom .zoomContainer {
  display: none !important;
}

.slick-track {
  margin: auto;
}

.arrow-style-2 .slick-arrow {
  border: 1px solid #e9e9e9;
  background: transparent;
}

.arrow-style-2 .slick-arrow:before {
  color: #c2c2c2;
  opacity: 1;
  line-height: 49px;
  transform: rotate(90deg);
}

.arrow-style-2 .slick-arrow:hover {
  border: 1px solid #333;
  background: #333;
}

.arrow-style-2 .slick-arrow:hover:before {
  color: #fff;
}

.arrow-style-2 .slick-next {
  right: auto;
  left: 55px;
}

header .carouselTab .slick-slide {
  padding-left: 13px;
  padding-right: 13px;
}

.banner_index .slick-dots {
  position: absolute;
  margin-top: 15px;
  right: 20px;
  bottom: 8%;
  list-style: none;
  display: block;
  text-align: center;
  padding: 0;
  width: 100%;
}

.info_slick .slick-dots {
  position: absolute;
  margin-top: 15px;
  right: 20px;
  bottom: 10px;
  list-style: none;
  display: block;
  text-align: center;
  padding: 0;
  width: 100%;
}
