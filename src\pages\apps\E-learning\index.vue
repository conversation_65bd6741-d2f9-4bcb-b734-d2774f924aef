<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import Filters from '@core/components/Filters.vue'
import { requiredValidator } from '@validators'

const router = useRouter()
const callAxios = useAxios()
const Swal = useSwal()

const searchQuery: Ref<string | number> = ref('')
const isEdit: Ref<boolean> = ref(false)
const refVForm: Ref<any> = ref(null)
const fileUpload: Ref<any> = ref([])

const editId: Ref<number> = ref(0)

interface FormType {
  recordDate: string
  complainChannelName: string
  isActive: string
  complainChannelId: number
}

const form: Ref<FormType> = ref({
  recordDate: '',
  complainChannelName: '',
  isActive: ''
})

const formRaw: Ref<FormType> = ref({
  recordDate: '',
  complainChannelName: '',
  isActive: ''
})

const listStatus = ref([
  { id: true, name: 'ใช้งาน' },
  { id: false, name: 'ไม่ใช้งาน' }
])

// ทำสี
const statusActive = ref({
  ใช้งาน: 'success',
  ไม่ใช้งาน: 'error'
})

const breadcrumbItems = [
  {
    title: 'MOC E-learning',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const filter = ref({
  searchDate: null,
  searchQuery: null
})

const listFilter = reactive([
  {
    name: 'searchDate',
    type: 'dateRange',
    label: 'เลือกวันที่'
  },
  {
    name: 'searchQuery',
    type: 'text',
    label: 'ค้นหา'
  }
])

const listFields = ref([
  {
    field: 'course_name',
    header: 'รายการ',
    sortable: true
  },
  {
    field: 'department',
    header: 'หน่วยงานที่จัดกิจกรรม',
    sortable: true
  },
  {
    field: 'startdate',
    header: 'วันที่เริ่มต้น',
    sortable: true
  },
  {
    field: 'enddate',
    header: 'วันที่สิ้นสุด',
    sortable: true
  },

  {
    field: 'options',
    header: 'การจัดการ'
  }
])

const ListItem = ref([])
const ListItemStore = ref([])

const isDialogVisible = ref(false)

const token = localStorage.token ? localStorage.token : null

const GetList = () => {
  callAxios.RequestGet(`/OtherMaster/GetTraining?Idcard=${token}`).then(response => {
    if (response.status == 200) {
      ListItem.value = response.data.response
      ListItemStore.value = response.data.response
    }
  })
}

onMounted(async () => {
  GetList()
})

const GetListById = id => {
  // form.value = ListItem.value[id];
  // editId.value = id;
  callAxios

    // .RequestGetById(
    //   `/SystemSetting/GetCertificateLogoByID?linkId=${id}`
    // )
    .RequestGetById('/Complain/GetComplainChannelById', `?ComplainChannelId=${id}`)
    .then(response => {
      if (response.status == 200) {
        form.value = response.data.response
        editId.value = form.value.complainChannelId
      }
    })
}

const AddForm = () => {
  callAxios.RequestPost('/Complain/AddComplainChannel', form.value).then(response => {
    if (response.status == 200) {
      router.push({ path: '/apps/moccomplain/managechannels' })
      isDialogVisible.value = false
      GetList()
    }
  })
}

const EditForm = () => {
  Swal.ApproveConfirm().then(response => {
    if (response) {
      // ListItem.value.splice(id, 1, form.value);
      // onFormReset();
      // form.value.createdDateTime = "1900-01-01";
      // form.value.modifiedDateTime = "1900-01-01";
      callAxios
        .RequestPut(`/Complain/UpdateComplainChannel?ComplainChannelId=${editId.value}`, form.value)
        .then(response => {
          if (response.status == 200) {
            isDialogVisible.value = false
            GetList()
          }
        })
    }
  })
}

const onFormSubmit = () => {
  refVForm.value?.validate().then(({ valid: isValid }: { valid: boolean }) => {
    if (isValid) {
      if (isEdit.value === false) AddForm()
      else EditForm(editId.value)
    }
  })
}

const onFormReset = () => {
  isDialogVisible.value = false
  form.value = structuredClone(toRaw(formRaw.value))
}

const ApproveDelete = id => {
  const endpoint = `/Complain/DeleteComplainChannel?ComplainChannelId=${id}`

  // console.log(listFields.value);

  // ListItem.value.splice(id, 1);

  Swal.ApproveDelete(endpoint).then(response => {
    if (response) GetList()
  })
}

function onSearch(values) {
  alert(JSON.stringify(values, null, 2))
}
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <Filters :fields="listFilter" :model-value="filter" />

    <VCard>
      <AppDataTable :filters="filter.searchQuery" :columns="listFields" :value="ListItem">
        <template #options="slotProps">
          <div v-if="slotProps.data.attachments" class="text-center">
            <a :href="slotProps.data.attachments" target="_blank">
              <VIcon icon="mdi-eye-outline" />
              <!--
                <VTooltip open-delay="500" location="top" activator="parent">
                </VTooltip>
              -->
            </a>
          </div>
        </template>
      </AppDataTable>
    </VCard>

    <VDialog v-model="isDialogVisible" persistent class="v-dialog-lg" no-click-animation scrollable>
      <VCard :title="isEdit ? 'แก้ไขข้อมูล' : 'เพิ่มข้อมูล'">
        <DialogCloseBtn variant="text" size="small" @click="onFormReset" />

        <VCardText>
          <VForm ref="refVForm" @submit.prevent="onFormSubmit">
            <VRow class="mb-4 mx-2 mt-6">
              <VCol cols="12" md="1" />
              <VCol cols="12" md="10">
                <VRow>
                  <VCol cols="12" md="12" class="text-lg-end">
                    <VRow class="mb-2" align="center">
                      <VCol cols="12" md="4" class="text-lg-end">
                        <label>
                          ช่องทางการรับเรื่อง :
                          <small class="text-error">*</small>
                        </label>
                      </VCol>
                      <VCol cols="12" md="8">
                        <VTextField
                          v-model="form.complainChannelName"
                          :rules="[requiredValidator]"
                        />
                      </VCol>
                    </VRow>
                  </VCol>
                  <VCol cols="12" md="12" class="text-lg-end">
                    <VRow class="mb-2" align="center">
                      <VCol cols="12" md="4" class="text-lg-end">
                        <label>
                          สถานะ :
                          <small class="text-error">*</small>
                        </label>
                      </VCol>
                      <VCol cols="12" md="8">
                        <VAutocomplete
                          v-model="form.isActive"
                          :items="listStatus"
                          item-title="name"
                          item-value="id"
                        />
                      </VCol>
                    </VRow>
                  </VCol>
                </VRow>
              </VCol>
              <VCol cols="12" md="1" />
            </VRow>
            <div class="d-flex flex-wrap justify-center mt-5">
              <div class="demo-space-x">
                <VBtn type="submit" color="blue-600">บันทึก</VBtn>
                <VBtn color="error-300" @click="onFormReset">ยกเลิก</VBtn>
              </div>
            </div>
          </VForm>
        </VCardText>
      </VCard>
    </VDialog>
  </div>
</template>
