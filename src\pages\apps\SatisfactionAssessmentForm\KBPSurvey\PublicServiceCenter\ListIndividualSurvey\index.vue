<script setup>
import { useAxios } from '@/store/useAxios'

const callAxios = useAxios()
const items = ref([])

const breadcrumbItems = [
  {
    title: 'แบบประเมินความพึงพอใจ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'KBP Survey',
    disabled: false,
    to: ''
  },
  {
    title: 'แบบประเมินความพึงพอใจการให้บริการ',
    disabled: false,
    to: ''
  },
  {
    title: 'สร้างแบบประเมินความพึงพอใจการให้บริการ (รายบุคคล)',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const isDialogDisable = ref(false)

const submitDialogFormEvaluation = event => {
  callAxios.RequestPost('/EvaluationForm/AddKBPSurveyPersonal', event).then(response => {
    if (response.status == 200) {
      isDialogDisable.value = true
      GetKBPSurveyPersonals()
    }
  })
}

const GetKBPSurveyPersonals = () => {
  callAxios.RequestGet('/EvaluationForm/GetKBPSurveyPersonals').then(response => {
    if (response.status == 200) setAttribute.value.items = response.data.response
  })
}

const setAttribute = ref({
  urlFilter: 'GetKBPSurveyPersonals',
  toQR: 'apps-SatisfactionAssessmentForm-KBPSurvey-PublicServiceCenter-ListIndividualSurvey-QRcode-id',
  toPrint:
    'apps-SatisfactionAssessmentForm-KBPSurvey-PublicServiceCenter-ListIndividualSurvey-Example-id',
  toTransaction:
    'apps-SatisfactionAssessmentForm-KBPSurvey-PublicServiceCenter-ListIndividualSurvey-Transaction-id',
  to: 'apps-SatisfactionAssessmentForm-KBPSurvey-PublicServiceCenter-ListIndividualSurvey-id',
  items: []
})

onMounted(() => {
  GetKBPSurveyPersonals()
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <BtnGoBack />
    <ListsTable
      v-bind="setAttribute"
      v-model:isDialogDisable="isDialogDisable"
      @submit="event => submitDialogFormEvaluation(event)"
      @update:modelValue="GetKBPSurveyPersonals"
    />
  </div>
</template>
