export interface FormDownloadLinks {
  pictureFiles: File | null
  files: File | null
  pdfFiles: File | null
  linkId: string
  linkName: string
  fileName: string
  pathName: string
  fileNamePicture: string
  pathNamePicture: string
  fileNamePDF: string
  pathNamePDF: string
  isActive: boolean
  selectedOptions: {
    isUrl: boolean
    isFile: boolean
  }
  linkURL: string
}
export interface FormLogos {
  imageFiles: File | null
  name: string
  fileName: string
  pathName: string
  isActive: boolean
  id: string
  logoId: string
  createDate: string
  createBy: string
  updateDate: string
  updateBy: string
  systemName: string | null
  logoName: string
  sortCreateDate: string
  sortUpdateDate: string
  isUrl: string
}

export interface FormBanners {
  imageFiles: File | null
  banner_id: number | null
  banner_name: string
  file_name: string
  url: string
  is_active: boolean
  sorting: number
  create_date: Date
  create_by: string
  modilfy_date: Date | null
  modify_by: Date | null
  period_typeid: number
  start_date: Date | null
  end_date: Date | null
}

export interface FormWebpagesBannerTab {
  imageFiles: File | null
  webpageId: string
  contentId: string | null
  webpageName: string
  fileName: string
  pathName: string
  url: string
  isActive: boolean
  isNavbar: boolean
  isUrl: boolean
  sorting: number | null
}

export interface FormWebpagesNavbarTab {
  webpageId: string
  contentId: string | null
  parrentId: string | null
  webpageName: string
  fileName: string
  pathName: string
  url: string
  isActive: boolean
  isNavbar: boolean
  isUrl: boolean
  sorting: number | null
  isSubWebpage: boolean | null
}

export interface FormRoles {
  roleId: string
  permissionName: string
  permissionCode: string
  timeOut: number
  isActive: boolean
}

export interface FormCertificationLevels {
  certificationId: string
  certificationName: string
  pathNamePicture: string
  imageFiles: File | null
  isActive: boolean
  selectedOptions: boolean
  minScore: number
  maxScore: number
}

export interface FormConfigurationEmails {
  systemEmailServiceId: number
  systemId: number
  smtpHost: string
  smtpEmail: string
  smtpPort: string
  activessl: true
  activeAuthentication: true
  username: string
  password: string
  senderName: string
  timeOut: number | null
  createDate: string
  updateDate: string
  createBy: string
  updateBy: string
  testMail: string
}

export interface FormTemplate {
  imageFiles: File | null
  id: string
  templateId: string
  name: string
  isActive: boolean
  isDefault: boolean
  color: string
  pathName: string
  templateName: string
  templateColor: string
}

export interface FormFooter {
  footerId: string
  address: string
  phone: string
  fax: string
  email: string
  facebook: string
  twitter: string
  youtube: string
  instagram: string
  tiktok: string
  provision: string
  policy: string
  isActive: boolean
}

export interface FormServiceType {
  transactionServiceTypeId: string
  serviceTypeName: string
  isActive: boolean
  description: string
  yearStart: string
  yearEnd: string
  contentName: string
  year: string[]
}
