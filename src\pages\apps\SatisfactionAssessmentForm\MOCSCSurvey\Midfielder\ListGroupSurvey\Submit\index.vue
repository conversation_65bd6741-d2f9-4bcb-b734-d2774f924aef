<script setup>
import { useAxios } from '@/store/useAxios'

const callAxios = useAxios()
const items = ref([])

const breadcrumbItems = [
  {
    title: 'แบบประเมินความพึงพอใจ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'MOCSC Survey',
    disabled: false,
    to: ''
  },
  {
    title: 'แบบประเมินความพึงพอใจการให้บริการศูนย์บริการประชาชน',
    disabled: false,
    to: ''
  },
  {
    title: 'รายการแบบประเมินความพึงพอใจการให้บริการศูนย์บริการประชาชน (ส่วนกลาง)',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const isDialogDisable = ref(false)

const submitDialogFormEvaluation = event => {
  callAxios.RequestPost('/EvaluationForm/AddMOCSCSurveyGroup', event).then(response => {
    if (response.status == 200) {
      isDialogDisable.value = true
      GetMOCSCSurveyGroups()
    }
  })
}

const GetMOCSCSurveyGroups = () => {
  callAxios.RequestGet('/EvaluationForm/GetMOCSCSurveyGroups').then(response => {
    if (response.status == 200)
      setAttribute.value.items = response.data.response.filter(x => x.isActive === 'ใช้งาน')
  })
}

const setAttribute = ref({
  urlFilter: 'GetMOCSCSurveyGroups',
  toQR: 'apps-SatisfactionAssessmentForm-MOCSCSurvey-Midfielder-ListGroupSurvey-QRcodeSubmit-id',
  toQROfficer: true,
  toPrint: 'apps-SatisfactionAssessmentForm-MOCSCSurvey-Midfielder-ListGroupSurvey-Example-id',
  toTransaction:
    'apps-SatisfactionAssessmentForm-MOCSCSurvey-Midfielder-ListGroupSurvey-Transaction-id',
  to: 'apps-SatisfactionAssessmentForm-MOCSCSurvey-Midfielder-ListGroupSurvey-id',
  items: []
})

onMounted(() => {
  GetMOCSCSurveyGroups()
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <BtnGoBack />
    <ListsTable
      v-bind="setAttribute"
      v-model:isDialogDisable="isDialogDisable"
      @submit="event => submitDialogFormEvaluation(event)"
      @update:modelValue="GetMOCSCSurveyGroups"
    />
  </div>
</template>
