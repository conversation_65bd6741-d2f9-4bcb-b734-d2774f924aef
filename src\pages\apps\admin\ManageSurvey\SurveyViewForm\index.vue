<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import BtnGoBack from '@/@core/components/button/BtnGoBack.vue'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import Image from '@images/toplogo.png'
import { requiredValidator } from '@validators'

const Swal = useSwal()
const router = useRouter()
const route = useRoute()
const callAxios = useAxios()

const id = ref(route.query.id)

const breadcrumbItems = [
  { title: 'ผู้ดูแลระบบ', disabled: false, to: '/apps/home' },
  {
    title: 'จัดการแบบประเมินความพึงพอใจ',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const assessments = ref([])

const form = reactive({
  govermentId: null,
  explanationType: 'mark',
  explanation: true,
  selectedChannel: '',
  otherServiceChannel: '',
  comments2: ''
})

const options = reactive({
  location: false,
  online: false
})

const formDefault = structuredClone(toRaw(form))

const resetForm = () => {
  Object.assign(form, structuredClone(formDefault))
}

const listGoverment = ref([])

// Fetch dropdown items for government departments
const getDropdownItems = async () => {
  try {
    const response = await callAxios.RequestGet('/OtherMaster/GovermentIndexList')

    if (response.status === 200) listGoverment.value = response.data.response
  } catch (error) {
    Swal.callCatch()
  }
}

// Fetch assessments data
const fetchAssessments = async () => {
  try {
    const response = await callAxios.RequestGet(
      `/UserManagement/GetAssessments?SystemAssessmentsId=${id.value}`
    )

    if (response.status === 200) {
      const { detail, assessments: apiAssessments } = response.data.response

      assessments.value = apiAssessments
      if (assessments.value.length > 0) {
        assessments.value[0] = {
          ...assessments.value[0],
          detail: detail || 'ไม่มีข้อมูลรายละเอียด'
        }
      }

      // Initialize form with dynamic fields
      apiAssessments.forEach(assessment => {
        if (assessment.subModels) {
          assessment.subModels.forEach(sub => {
            form[sub.assessmentsId] = '' // Initialize answer
            form[`remark_${sub.assessmentsId}`] = sub.remark || '' // Load remark if exists

            if (sub.subModels) {
              sub.subModels.forEach(option => {
                form[option.assessmentsId] = '' // Initialize option answers
              })
            }
          })
        }
      })
    }
  } catch (error) {
    Swal.AddConditionFailText('Error fetching assessments.')
  }
}

const goBack = async () => {
  const response = await Swal.ApproveCancel()
  if (response) resetForm()

  router.go(-1)
}

// Fetch assessments and dropdown items on component mount
onMounted(() => {
  fetchAssessments()
  getDropdownItems()
})
</script>

<template>
  <div>
    <!-- Breadcrumb -->
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <VCard class="px-4">
      <div class="mt-5">
        <VImg :height="200" :src="Image" />
      </div>
      <VCol cols="12" md="12">
        <div class="ms-4">
          แบบสำรวจความคิดเห็นของผู้รับบริการจากหน่วยงานของรัฐที่ได้รับการรับรองมาตรฐานการให้บริการของศูนย์ราชการสะดวก
        </div>
        <div>
          {{ assessments[0]?.detail || 'ไม่มีข้อมูลรายละเอียด' }}
        </div>
      </VCol>

      <VCard>
        <VCol cols="12" md="12">
          <VRow class="mb-2 mx-5 mt-3" align="center">
            <VCol cols="12" md="12" class="mb-2 py-0">
              <label>
                ชื่อหน่วยงานที่ท่านรับบริการ :
                <small class="text-error">*</small>
              </label>
            </VCol>
            <VCol cols="12" md="12" class="py-0">
              <VAutocomplete
                v-model="form.govermentId"
                :items="listGoverment"
                item-value="govermentId"
                item-title="govermentName"
                :persistent-placeholder="true"
                placeholder="ตัวอย่าง : ศูนย์บริการประชาชนกระทรวงพาณิชย์"
                class="no-select"
                no-data-text="ไม่มีข้อมูล"
                :rules="[v => !!v || 'โปรดเลือกหน่วยงาน']"
              />
            </VCol>
          </VRow>
        </VCol>
      </VCard>
      <VCard class="mt-4">
        <VRow class="mb-2 mx-5 mt-3">
          <VCol cols="12" md="12" class="py-0">
            <VCardText>
              <VRow align="center" class="d-flex align-items-center justify-start">
                <!-- Label -->
                <label class="me-2 mb-0">คำชี้แจง : โปรดทำเครื่องหมาย</label>

                <!-- Radio Group -->
                <VRadioGroup
                  v-model="form.explanationType"
                  class="d-inline-flex align-items-center"
                  style="width: auto; margin-right: 8px"
                >
                  <VRadio label="หรือ" value="mark" />
                </VRadioGroup>

                <!-- Checkbox -->
                <VCheckbox
                  v-model="form.explanation"
                  class="d-inline-flex align-items-center"
                  style="width: auto"
                  label="ที่ตรงกับความเป็นจริง"
                  @change="form.explanation = true"
                />
              </VRow>
            </VCardText>
          </VCol>
        </VRow>
      </VCard>

      <VCard
        v-for="(assessment, index) in assessments"
        :key="assessment.assessmentsId"
        class="mt-4 mb-4"
      >
        <VCardTitle>ตอนที่ {{ index + 1 }} : {{ assessment.assessmentsName }}</VCardTitle>
        <VCardText>
          <div
            v-if="
              assessment.assessmentsName.includes('ความพึงพอใจ') ||
              assessment.assessmentsName.includes('ภาพรวม')
            "
          >
            <VCardText>
              <div
                v-for="(mainSub, mainIndex) in assessment.subModels"
                :key="mainSub.assessmentsId"
              >
                <!-- Check and render only the first occurrence of the specific name -->
                <div
                  v-if="
                    mainIndex ===
                      assessment.subModels.findIndex(
                        sub =>
                          sub.assessmentsName ===
                          'ช่องทางออนไลน์ที่ท่านใช้บริการมากที่สุดและมีประสิทธิภาพสูงสุด'
                      ) &&
                    (mainSub.no == 2.2 || (mainSub.no == 2.3 && options.online))
                  "
                  class="mt-2"
                >
                  <label>
                    {{ mainSub.assessmentsName }}
                    <small v-if="mainSub.isRequest" class="text-error">*</small>
                  </label>
                  <VRadioGroup v-model="form[mainSub.assessmentsId]">
                    <VRadio
                      v-for="option in mainSub.subModels.filter(option => option.isActive)"
                      :key="option.assessmentsId"
                      :label="option.assessmentsName"
                      :value="option.assessmentsId"
                    />
                    <VRadio label="อื่น ๆ (โปรดระบุ)" value="" />
                  </VRadioGroup>

                  <VTextField
                    v-if="form[mainSub.assessmentsId] === ''"
                    v-model="form.otherServiceChannel"
                    placeholder="โปรดระบุ"
                    :rules="[requiredValidator]"
                    :counter="100"
                    :maxlength="100"
                    density="comfortable"
                    class="mt-3"
                  />
                  <!-- Remark Section -->
                  <div class="mt-4">
                    <label>
                      ข้อเสนอแนะอื่น ๆ (ถ้ามี) สำหรับหัวข้อ:
                      {{ mainSub.assessmentsName }}
                    </label>
                    <VTextarea
                      v-model="form[`remark_${mainSub.assessmentsId}`]"
                      :counter="1000"
                      placeholder="กรอกข้อเสนอแนะ"
                      density="comfortable"
                    />
                  </div>
                </div>

                <!-- Render other tables -->
                <VTable v-else class="mt-4">
                  <template
                    v-if="
                      (mainSub.no == 2.1 && options.location) ||
                      (mainSub.no == 2.2 && options.online) ||
                      (mainSub.no == 3.1 && mainSub.isActive === true)
                    "
                  >
                    <thead>
                      <tr>
                        <th class="text-h6">หัวข้อประเมิน</th>
                        <th class="text-center text-h6">ระดับความพึงพอใจ / ความคิดเห็น</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td colspan="2">
                          <strong>
                            {{ mainIndex + 1 }}. {{ mainSub.assessmentsName }}
                            <small v-if="mainSub.isRequest || mainSub.no == 3.1" class="text-error">
                              *
                            </small>
                          </strong>
                        </td>
                      </tr>
                      <template
                        v-for="(sub, subIndex) in mainSub.subModels"
                        :key="sub.assessmentsId"
                      >
                        <tr v-if="sub.isActive">
                          <td>{{ sub.assessmentsName }}</td>
                          <td>
                            <VRadioGroup v-model="form[sub.assessmentsId]" row>
                              <VRadio label="😀 มากที่สุด" value="5" />
                              <VRadio label="😊 มาก" value="4" />
                              <VRadio label="😐 ปานกลาง" value="3" />
                              <VRadio label="🙁 น้อย" value="2" />
                              <VRadio label="😢 น้อยที่สุด" value="1" />
                            </VRadioGroup>
                          </td>
                        </tr>
                      </template>
                      <tr v-if="!mainSub.subModels || mainSub.subModels.length === 0">
                        <td>{{ mainSub.assessmentsName }}</td>
                        <td>
                          <VRadioGroup v-model="form[mainSub.assessmentsId]" row>
                            <VRadio label="😀 มากที่สุด" value="5" />
                            <VRadio label="😊 มาก" value="4" />
                            <VRadio label="😐 ปานกลาง" value="3" />
                            <VRadio label="🙁 น้อย" value="2" />
                            <VRadio label="😢 น้อยที่สุด" value="1" />
                          </VRadioGroup>
                        </td>
                      </tr>
                    </tbody>
                  </template>
                </VTable>
                <!-- Remark Below the Current Table -->
                <div
                  v-if="
                    (mainSub.no !== 3.1 &&
                      mainSub.assessmentsName !==
                        'ช่องทางออนไลน์ที่ท่านใช้บริการมากที่สุดและมีประสิทธิภาพสูงสุด' &&
                      mainSub.no == 2.1 &&
                      options.location) ||
                    (!mainSub.no == 2.2 && options.online)
                  "
                  class="mt-4"
                >
                  <label>
                    ข้อเสนอแนะอื่น ๆ (ถ้ามี) สำหรับหัวข้อ:
                    {{ mainSub.assessmentsName }}
                  </label>
                  <VTextarea
                    v-model="form[`remark_${mainSub.assessmentsId}`]"
                    :counter="1000"
                    placeholder="กรอกข้อเสนอแนะ"
                    density="comfortable"
                  />
                </div>
              </div>
            </VCardText>
          </div>

          <div v-else>
            <div v-for="sub in assessment.subModels" :key="sub.assessmentsId">
              <VRow>
                <VCol cols="12" md="3">
                  <label>
                    {{ sub.assessmentsName }} :
                    <small v-if="sub.isRequest" class="text-error">*</small>
                  </label>
                </VCol>
                <VCol cols="12" md="9">
                  <VRadioGroup
                    v-if="sub.subModels && sub.subModels.length > 1"
                    v-model="form[sub.assessmentsId]"
                    row
                  >
                    <VRadio
                      v-for="option in sub.subModels.filter(option => option.isActive)"
                      :key="option.assessmentsId"
                      :label="option.assessmentsName"
                      :value="option.assessmentsId"
                    />
                  </VRadioGroup>
                </VCol>
              </VRow>
            </div>

            <VRow>
              <VCol cols="12" md="3">
                <label>
                  ช่องทางการรับบริการ :
                  <small class="text-error">*</small>
                </label>
              </VCol>
              <VCol cols="12" md="9">
                <VCheckbox v-model="options.location" label="ณ สำนักงาน/ที่ตั้งหน่วยงาน" />
                <VCheckbox v-model="options.online" label="ผ่านระบบออนไลน์ของหน่วยงาน" />

                <!-- <p>Selected Options location: {{ options.location }}</p> -->
                <!-- <p>Selected Options online: {{ options.online }}</p> -->
              </VCol>
            </VRow>
          </div>
        </VCardText>
      </VCard>
    </VCard>
    <div class="d-flex justify-end mt-5">
      <div class="demo-space-x">
        <BtnGoBack />
      </div>
    </div>
  </div>
</template>

<style scoped>
.no-select {
  user-select: none;
}

::v-deep .v-input {
  flex: initial;
}

table {
  border-collapse: collapse;
  inline-size: 100%;
}

thead th,
tbody td {
  border: 1px solid #ccc;
  text-align: start;
}

thead th {
  background-color: #f9f9f9;
}

tbody tr:nth-child(odd) {
  background-color: #f5f5f5;
}

/* DarkTheme */
.v-theme--dark thead th {
  background-color: #25283d;
}

.v-theme--dark tbody tr:nth-child(odd) {
  background-color: #25283d;
}
</style>
