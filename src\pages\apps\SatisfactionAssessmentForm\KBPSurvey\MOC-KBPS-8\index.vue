<script setup lang="ts">
import { filterMenu } from '@utils'

const inProcess = ref(true)

interface BreadcrumbItem {
  title: string
  disabled: boolean
  to?: string
  active?: boolean
  activeClass?: string
}

interface MenuItem {
  title: string
  to: {
    name: string
    params?: { id: number }
    query?: { controller: string }
  }
  icon: string
  color: string
}

const breadcrumbItems: Ref<BreadcrumbItem[]> = ref([
  {
    title: 'แบบประเมินความพึงพอใจ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'KBP Survey',
    disabled: false
  },
  {
    title: 'แบบประเมินความพึงพอใจของผู้มีส่วนได้ส่วนเสีย',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
])

const ListsMenu: Ref<MenuItem[]> = ref([
  {
    title: 'สร้างแบบประเมินความพึงพอใจของผู้มีส่วนได้ส่วนเสีย',
    toRaw: 'สร้างแบบสำรวจความพึงพอใจของผู้มีส่วนได้ส่วนเสีย',
    to: {
      name: 'apps-SatisfactionAssessmentForm-KBPSurvey-MOC-KBPS-9'
    },
    icon: 'mdi-note-plus',
    color: 'blue-700'
  },
  {
    title: 'ส่งแบบประเมินความพึงพอใจของผู้มีส่วนได้ส่วนเสีย',
    toRaw: 'ส่งแบบสำรวจความพึงพอใจของผู้มีส่วนได้ส่วนเสีย',
    to: {
      name: 'apps-SatisfactionAssessmentForm-KBPSurvey-MOC-KBPS-10'
    },
    icon: 'mdi-list-box',
    color: 'info-500'
  },
  {
    title: 'รายงานสรุปภาพรวมความพึงพอใจของผู้มีส่วนได้ส่วนเสีย',
    toRaw: 'รายงานสรุปภาพรวมความพึงพอใจของผู้มีส่วนได้ส่วนเสีย',
    to: {
      name: 'apps-Report-id',
      params: { id: 1 },
      query: { controller: 'RP_014_KBP_SurveySummary' }
    },
    icon: 'mdi-chart-box',
    color: 'error-600'
  }
])

onMounted(async () => {
  if (ListsMenu.value) {
    ListsMenu.value = await filterMenu(ListsMenu.value)
    inProcess.value = false
  } else {
    inProcess.value = false
  }
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <VCard title="" class="mb-5">
      <VCardText>
        <BtnGoBack />
        <VRow class="align-center">
          <VCol
            v-for="(item, index) in ListsMenu"
            :key="index"
            cols="12"
            md="6"
            class="align-center"
          >
            <RouterLink :to="item.to">
              <VAlert prominent variant="outlined" :color="item.color" :icon="item.icon">
                {{ item.title }}
              </VAlert>
            </RouterLink>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
  </div>
</template>
