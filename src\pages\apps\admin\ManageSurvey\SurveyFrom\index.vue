<script setup lang="ts">
import { onMounted, reactive } from 'vue'
import { VCol } from 'vuetify/lib/components/index.mjs'
import BtnGoBack from '@/@core/components/button/BtnGoBack.vue'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import { requiredValidator } from '@validators'

const callAxios = useAxios()
const Swal = useSwal()

// Breadcrumb Items
const breadcrumbItems = [
  { title: 'ผู้ดูแลระบบ', disabled: false, to: '/apps/home' },
  {
    title: 'จัดการแบบประเมินความพึงพอใจ',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

// Filter and Form State
const filter = reactive({
  Keyword: '',
  Status: ''
})

const form = reactive({
  explanationType: 'mark',
  explanation: true,

  // Section 1: User Information
  gender: '',
  age: '',
  education: '',
  frequency: '',
  servicePurpose: '',
  serviceChannel: [],

  // Section 2: Satisfaction Levels
  table1: {
    row1: '',
    row2: '',
    row3: ''
  },
  comments1: '',

  table2: {
    row1: '',
    row2: ''
  },
  comments2: '',

  // Service Channel Selection
  selectedChannel: '',
  otherServiceChannel: ''
})

const preventChange = (value: any) => {
  // คืนค่าเดิมกลับไปที่ v-model
  form.explanationType = 'mark'
}

const preventCheckboxChange = (value: any) => {
  // คืนค่าเดิมกลับไปที่ v-model
  form.explanation = true
}

// Fetch Data
const GetList = async () => {
  try {
    const response = await callAxios.RequestGet(
      `/UserManagement/GetSystemDepartment?Keyword=${filter.Keyword}&Status=${filter.Status}`
    )

    if (response.status === 200) console.log('Data fetched successfully', response.data)
    else console.log('No data available')
  } catch (error) {
    console.error('Error fetching data', error)
    Swal.callCatch()
  }
}

onMounted(() => {
  console.log('Component mounted')

  // Uncomment to fetch data on mount
  // GetList();
})
</script>

<template>
  <div>
    <!-- Breadcrumb -->
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <VCard class="px-4">
      <div>
        <VImg :height="200" src="\public\toplogo.png" />
      </div>
      <VCol cols="12" md="12">
        <div class="ms-4">
          แบบสำรวจความคิดเห็นของผู้รับบริการจากหน่วยงานของรัฐที่ได้รับการรับรองมาตรฐานการให้บริการของศูนย์ราชการสะดวก
        </div>
        <div>
          ศูนย์บริการประชาชน สำนักงานปลัดสำนักนายกรัฐมนตรี
          ในฐานะฝ่ายเลขานุการคณะกรรมการอำนวยการศูนย์ราชการสะดวก
          ขอความร่วมมือผู้รับบริการทุกท่านทำแบบประเมินความพึงพอใจ ทั้งนี้
          เพื่อเป็นการรักษามาตรฐานการให้บริการประชาชนของหน่วยงานรัฐ ในฐานะศูนย์ราชการสะดวก
          และนำข้อมูลที่ได้มาปรับปรุงพัฒนาการให้บริการของศูนย์ราชการสะดวกต่อไป
        </div>
      </VCol>

      <VCol cols="12" md="12">
        <VCard>
          <VRow class="mb-2 mx-5 mt-3" align="center">
            <VCol cols="12" md="12" class="mb-2">
              <label>
                แบบสำรวจความคิดเห็น :
                <small class="text-error">*</small>
              </label>
            </VCol>
            <VCol cols="12" md="12" class="py-0">
              <VAutocomplete
                :rules="[requiredValidator]"
                density="comfortable"
                placeholder="ตัวอย่าง : แแบบสำรวจความคิดเห็นประจำปี 2567"
                no-data-text="ไม่มีข้อมูล"
                :counter="100"
                :maxlength="100"
              />
            </VCol>
          </VRow>
        </VCard>
        <VCard class="mt-4">
          <VRow class="mb-2 mx-5 mt-3">
            <VCol cols="12" md="12" class="py-0">
              <VCardText>
                <VRow align="center" class="d-flex align-items-center justify-start">
                  <!-- Label -->
                  <label class="me-2 mb-0">คำชี้แจง : โปรดทำเครื่องหมาย</label>

                  <!-- Radio Group -->
                  <VRadioGroup
                    v-model="form.explanationType"
                    class="d-inline-flex align-items-center"
                    style="width: auto; margin-right: 8px"
                    @change="preventChange"
                  >
                    <VRadio label="หรือ" value="mark" />
                  </VRadioGroup>

                  <!-- Checkbox -->
                  <VCheckbox
                    v-model="form.explanation"
                    class="d-inline-flex align-items-center"
                    style="width: auto"
                    label="ที่ตรงกับความเป็นจริง"
                    @change="preventCheckboxChange"
                  />
                </VRow>
              </VCardText>
            </VCol>
          </VRow>
        </VCard>

        <!-- User Information Form -->
        <VCard class="mt-4">
          <VCardTitle>ตอนที่ 1 : ข้อมูลผู้รับบริการ</VCardTitle>
          <VCardText>
            <VRow>
              <!-- Gender Selection -->
              <VCol cols="12" md="3">
                <label>
                  เพศ :
                  <span class="text-error">*</span>
                </label>
              </VCol>
              <VCol cols="12" md="9">
                <VRadioGroup v-model="form.gender" :rules="[requiredValidator]" row>
                  <VRadio label="ชาย" value="male" />
                  <VRadio label="หญิง" value="female" />
                </VRadioGroup>
              </VCol>

              <!-- Age Selection -->
              <VCol cols="12" md="3">
                <label>
                  อายุ :
                  <span class="text-error">*</span>
                </label>
              </VCol>
              <VCol cols="12" md="9">
                <VRadioGroup v-model="form.age" :rules="[requiredValidator]" row>
                  <VRadio label="ต่ำกว่า 30 ปี" value="<30" />
                  <VRadio label="30 - 50 ปี" value="30-50" />
                  <VRadio label="51 - 60 ปี" value="51-60" />
                  <VRadio label="มากกว่า 60 ปี" value=">60" />
                </VRadioGroup>
              </VCol>

              <!-- Education Level -->
              <VCol cols="12" md="3">
                <label>
                  ระดับการศึกษาสูงสุด :
                  <span class="text-error">*</span>
                </label>
              </VCol>
              <VCol cols="12" md="9">
                <VRadioGroup v-model="form.education" :rules="[requiredValidator]" row>
                  <VRadio label="ต่ำกว่ามัธยมศึกษาตอนปลาย" value="highschool" />
                  <VRadio label="ปริญญาตรี" value="bachelor" />
                  <VRadio label="ปริญญาโท" value="master" />
                  <VRadio label="สูงกว่าปริญญาโท" value="doctorate" />
                </VRadioGroup>
              </VCol>

              <!-- Frequency of Service -->
              <VCol cols="12" md="3">
                <label>
                  ความถี่ในการรับบริการต่อเดือน :
                  <span class="text-error">*</span>
                </label>
              </VCol>
              <VCol cols="12" md="9">
                <VRadioGroup v-model="form.frequency" :rules="[requiredValidator]" row>
                  <VRadio label="1 - 2 ครั้ง" value="1-2" />
                  <VRadio label="3 - 4 ครั้ง" value="3-4" />
                  <VRadio label="4 ครั้งขึ้นไป" value=">4" />
                </VRadioGroup>
              </VCol>

              <!-- Service Purpose -->
              <VCol cols="12" md="3">
                <label>
                  เป็นผู้ที่ต้องการความช่วยเหลือเป็นพิเศษ :
                  <span class="text-error">*</span>
                </label>
              </VCol>
              <VCol cols="12" md="9">
                <VRadioGroup v-model="form.servicePurpose" :rules="[requiredValidator]" row>
                  <VRadio label="สตรีมีครรภ์" value="pregnant" />
                  <VRadio label="ผู้สูงอายุ" value="elderly" />
                  <VRadio label="พระภิกษุสงฆ์/สามเณร" value="monk" />
                </VRadioGroup>
              </VCol>

              <!-- Service Channel -->
              <VCol cols="12" md="3">
                <label>
                  ช่องทางการรับบริการ :
                  <span class="text-error">*</span>
                </label>
              </VCol>
              <VCol cols="12" md="9">
                <VCheckboxGroup v-model="form.serviceChannel" :rules="[requiredValidator]">
                  <VCheckbox label="ณ สำนักงาน/ที่ตั้งหน่วยงาน" value="onsite" />
                  <VCheckbox label="ผ่านระบบออนไลน์ของหน่วยงาน" value="online" />
                </VCheckboxGroup>
              </VCol>
            </VRow>
          </VCardText>
        </VCard>

        <VCard class="mt-4">
          <VCardTitle>ตอนที่ 2 : ระดับความพึงพอใจต่อการให้บริการ</VCardTitle>
          <VCardText>
            <!-- Table 1 -->
            <div class="mb-4">
              <VTable>
                <thead>
                  <tr>
                    <th class="text-h6">หัวข้อประเมิน</th>
                    <th class="text-center text-h6">ระดับความพึงพอใจ / ความคิดเห็น</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>1. จุดให้บริการ ณ สำนักงาน/ที่ตั้งหน่วยงาน</td>
                    <td />
                  </tr>
                  <tr>
                    <td>จุดให้บริการ ณ สำนักงาน/ที่ตั้งหน่วยงาน</td>
                    <td>
                      <VRow>
                        <VCol>
                          <VRadioGroup v-model="form.table1.row1" row>
                            <VRadioGroup v-model="form.table1.row1" row>
                              <VRadio label="😀 มากที่สุด" value="มากที่สุด" />
                              <VRadio label="😊 มาก" value="มาก" />
                              <VRadio label="😐 ปานกลาง" value="ปานกลาง" />
                              <VRadio label="🙁 น้อย" value="น้อย" />
                              <VRadio label="😢 น้อยที่สุด" value="น้อยที่สุด" />
                            </VRadioGroup>
                          </VRadioGroup>
                        </VCol>
                      </VRow>
                    </td>
                  </tr>
                  <tr>
                    <td>ความสะดวกของสถานที่ให้บริการ</td>
                    <td>
                      <VRow>
                        <VCol>
                          <VRadioGroup v-model="form.table1.row2" row>
                            <VRadio label="😀 มากที่สุด" value="มากที่สุด" />
                            <VRadio label="😊 มาก" value="มาก" />
                            <VRadio label="😐 ปานกลาง" value="ปานกลาง" />
                            <VRadio label="🙁 น้อย" value="น้อย" />
                            <VRadio label="😢 น้อยที่สุด" value="น้อยที่สุด" />
                          </VRadioGroup>
                        </VCol>
                      </VRow>
                    </td>
                  </tr>
                  <tr>
                    <td>การจัดลำดับคิวและความรวดเร็ว</td>
                    <td>
                      <VRow>
                        <VCol>
                          <VRadioGroup v-model="form.table1.row3" row>
                            <VRadio label="😀 มากที่สุด" value="มากที่สุด" />
                            <VRadio label="😊 มาก" value="มาก" />
                            <VRadio label="😐 ปานกลาง" value="ปานกลาง" />
                            <VRadio label="🙁 น้อย" value="น้อย" />
                            <VRadio label="😢 น้อยที่สุด" value="น้อยที่สุด" />
                          </VRadioGroup>
                        </VCol>
                      </VRow>
                    </td>
                  </tr>
                </tbody>
              </VTable>
            </div>

            <!-- Additional Comments -->
            <div class="mb-4">
              <label>ข้อเสนอแนะอื่น ๆ (ถ้ามี) :</label>
              <VTextarea v-model="form.comments1" :counter="1000" placeholder="กรอกข้อเสนอแนะ" />
            </div>

            <!-- Table 2 -->
            <div class="mb-4">
              <VTable>
                <thead>
                  <tr>
                    <th class="text-h6">หัวข้อประเมิน</th>
                    <th class="text-center text-h6">ระดับความพึงพอใจ / ความคิดเห็น</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>2. ช่องทางระบบออนไลน์ของหน่วยงาน</td>
                    <td />
                  </tr>
                  <tr>
                    <td>การใช้งานระบบออนไลน์ของหน่วยงาน</td>
                    <td>
                      <VRow>
                        <VCol>
                          <VRadioGroup v-model="form.table2.row1" row>
                            <VRadio label="😀 มากที่สุด" value="มากที่สุด" />
                            <VRadio label="😊 มาก" value="มาก" />
                            <VRadio label="😐 ปานกลาง" value="ปานกลาง" />
                            <VRadio label="🙁 น้อย" value="น้อย" />
                            <VRadio label="😢 น้อยที่สุด" value="น้อยที่สุด" />
                          </VRadioGroup>
                        </VCol>
                      </VRow>
                    </td>
                  </tr>
                  <tr>
                    <td>ความครบถ้วนของข้อมูล</td>
                    <td>
                      <VRow>
                        <VCol>
                          <VRadioGroup v-model="form.table2.row2" row>
                            <VRadio label="😀 มากที่สุด" value="มากที่สุด" />
                            <VRadio label="😊 มาก" value="มาก" />
                            <VRadio label="😐 ปานกลาง" value="ปานกลาง" />
                            <VRadio label="🙁 น้อย" value="น้อย" />
                            <VRadio label="😢 น้อยที่สุด" value="น้อยที่สุด" />
                          </VRadioGroup>
                        </VCol>
                      </VRow>
                    </td>
                  </tr>
                </tbody>
              </VTable>
            </div>

            <!-- Additional Service Channels -->
            <div class="mb-4">
              <label>
                ช่องทางที่ท่านใช้บริการมากที่สุดและเป็นที่พึงพอใจสูงสุด :
                <small class="text-error">*</small>
              </label>
              <VRadioGroup v-model="form.selectedChannel">
                <VRadio label="เว็บไซต์" value="website" />
                <VRadio label="Line Official" value="line" />
                <VRadio label="Application ของหน่วยงาน" value="application" />
                <VRadio label="อื่น ๆ (โปรดระบุ)" value="others" />
              </VRadioGroup>

              <VTextField
                v-if="form.selectedChannel === 'others'"
                v-model="form.otherServiceChannel"
                placeholder="โปรดระบุ"
                :rules="[requiredValidator]"
                :counter="100"
                :maxlength="100"
                density="comfortable"
              />
            </div>

            <div>
              <label>ข้อเสนอแนะอื่น ๆ (ถ้ามี) :</label>
              <VTextarea v-model="form.comments2" :counter="1000" placeholder="กรอกข้อเสนอแนะ" />
            </div>
          </VCardText>
        </VCard>

        <VCard class="mt-4">
          <VCardTitle>ตอนที่ 3 : ภาพรวมการให้บริการ</VCardTitle>
          <VCardText>
            <div class="mb-4">
              <VTable>
                <thead>
                  <tr>
                    <th class="text-h6">หัวข้อประเมิน</th>
                    <th class="text-center text-h6">ระดับความพึงพอใจ / ความคิดเห็น</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>
                      <VRow>
                        <VCol>
                          <h4>โปรดให้คะแนนการให้บริการของหน่วยงานของรัฐ ตั้งแต่ 1 - 5 คะแนน</h4>
                          <label>
                            (1 ดาว หมายถึง ไม่พึงพอใจ และ 5 ดาว หมายถึง พึงพอใจมากที่สุด)
                          </label>
                        </VCol>
                      </VRow>
                    </td>
                    <td>
                      <VRow>
                        <VCol>
                          <VRadioGroup v-model="form.table1.row1">
                            <VRadio label="😀 มากที่สุด" value="มากที่สุด" />
                            <VRadio label="😊 มาก" value="มาก" />
                            <VRadio label="😐 ปานกลาง" value="ปานกลาง" />
                            <VRadio label="🙁 น้อย" value="น้อย" />
                            <VRadio label="😢 น้อยที่สุด" value="น้อยที่สุด" />
                          </VRadioGroup>
                        </VCol>
                      </VRow>
                    </td>
                  </tr>
                </tbody>
              </VTable>
            </div>
          </VCardText>
        </VCard>
      </VCol>
    </VCard>

    <div class="d-flex justify-end mt-5">
      <div class="demo-space-x">
        <BtnGoBack />
      </div>
    </div>
  </div>
</template>

<style scoped>
.no-select {
  user-select: none;
}

::v-deep .v-input {
  flex: initial;
}

table {
  border-collapse: collapse;
  inline-size: 100%;
}

thead th,
tbody td {
  border: 1px solid #ccc;
  text-align: start;
}

thead th {
  background-color: #f9f9f9;
}

tbody tr:nth-child(odd) {
  background-color: #f5f5f5;
}
</style>
