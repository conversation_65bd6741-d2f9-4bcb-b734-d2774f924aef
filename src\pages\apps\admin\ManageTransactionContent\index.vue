<script setup lang="ts">
import { VBtn } from 'vuetify/lib/components/index.mjs'
import EditContent from './EditContent.vue'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'

const callAxios = useAxios()
const Swal = useSwal()

const currentPage = ref(1)
const rowPerPage = ref(20)
const isDialogVisible = ref(false)
const isView: Ref<boolean> = ref(false)
const isEdit: Ref<boolean> = ref(false)
const isAdd: Ref<boolean> = ref(false)
const editId: Ref<string> = ref('')
const ListItem = ref([])
const totalCount = ref(0)
const inProcess = ref(false)

const breadcrumbItems = [
  {
    title: 'ผู้ดูแลระบบ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'จัดการเนื้อหา',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const filter = reactive({
  Keyword: '',
  status: ''
})

const listFilter = ref([
  {
    name: 'status',
    type: 'select',
    label: 'สถานะ',
    default: '',
    value: 'id',
    title: 'name',
    items: [
      { id: '', name: 'ทั้งหมด' },
      { id: 'true', name: 'ใช้งาน' },
      { id: 'false', name: 'ไม่ใช้งาน' }
    ]
  },
  {
    name: 'Keyword',
    type: 'text',
    label: 'คำค้น',
    default: '',
    items: [],
    placeholder: 'กรอกข้อมูลที่ต้องการค้นหา'
  }
])

const listFields = ref([
  {
    field: 'no',
    header: 'ลำดับที่',
    sortable: false,
    style: { textAlign: 'center' }
  },
  {
    field: 'contentName',
    header: 'ชื่อรายการ',
    sortable: false
  },
  {
    field: 'isActive',
    header: 'สถานะ',
    sortable: false
  },
  {
    field: 'createDate',
    header: 'วันที่สร้าง',
    sortable: false
  },
  {
    field: 'createBy',
    header: 'ชื่อผู้สร้าง',
    sortable: false
  },
  {
    field: 'updateDate',
    header: 'วันที่แก้ไข',
    sortable: false,
    style: { textAlign: 'center' }
  },
  {
    field: 'updateBy',
    header: 'ชื่อผู้แก้ไข',
    sortable: false,
    style: { textAlign: 'center' }
  },

  {
    field: 'options',
    header: 'การจัดการ',
    sortable: false,
    style: { textAlign: 'center' }
  }
])

const openAddDialog = () => {
  isAdd.value = true
  isEdit.value = false
  isView.value = false
  isDialogVisible.value = true // ตรวจสอบว่าได้ตั้งค่านี้
}

const openEditDialog = (id: string) => {
  isEdit.value = true
  isView.value = false
  isAdd.value = false
  isDialogVisible.value = true
  editId.value = id
}

const openViewDialog = (id: string) => {
  isView.value = true
  isEdit.value = false
  isAdd.value = false
  isDialogVisible.value = true
  editId.value = id
}

const onback = () => {
  isEdit.value = false
  isAdd.value = false
  isView.value = false
  isDialogVisible.value = false
  GetList()
}

const GetList = async () => {
  try {
    const params = new URLSearchParams({
      Page: currentPage.value.toString(),
      PageSize: rowPerPage.value.toString()
    })

    if (filter.Keyword) params.append('Keyword', filter.Keyword)
    if (filter.status) params.append('Status', filter.status)

    const response = await callAxios.RequestGet(`/UserManagement/GetSystemContent?${params}`)

    if (response.status === 200) {
      if (response.data.count > 0) {
        totalCount.value = response.data.count
        ListItem.value = response.data.response
      } else {
        ListItem.value = []
        onNotFound()
      }
    } else {
      onNotFound()
      ListItem.value = []
    }
  } catch (error) {
    console.error('error', error)
    Swal.callCatch()
  } finally {
    inProcess.value = false
  }
}

const onPageChange = (event: { page: number; rows: number }) => {
  currentPage.value = event.page + 1
  rowPerPage.value = event.rows
  GetList()
}

const ApproveDelete = async (id: number) => {
  const endpoint = `/UserManagement/DeleteSystemContent?ContentId=${id}`
  try {
    const response = await Swal.ApproveDelete(endpoint)
    if (response) await GetList()
  } catch (error) {
    console.error('Error during deletion:', error)
    Swal.DeleteFail() // Example error handler if needed
  }
}

onMounted(() => {
  GetList()
})

const onNotFound = () => {
  ListItem.value = []
  totalCount.value = 0
  currentPage.value = 0
  rowPerPage.value = 0
}
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <div v-if="!isDialogVisible">
      <FiltersAPI v-model="filter" :fields="listFilter" @submit="GetList" />
    </div>
    <VCard v-if="!isDialogVisible" class="px-4">
      <VCardText>
        <div class="d-flex align-center flex-wrap gap-4">
          <VBtn
            class="mt-2"
            prepend-icon="mdi-plus-box-multiple"
            color="btn-add"
            rounded="xl"
            @click="openAddDialog"
          >
            เพิ่มรายการ
          </VBtn>
        </div>
      </VCardText>
      <AppDataTableAPI
        :columns="listFields"
        :value="ListItem"
        :total-records="totalCount"
        :header-no="false"
        @page="onPageChange"
      >
        <!-- กำหนดสีของ Chip ในคอลัมน์สถานะ -->
        <template #isActive="slotProps">
          <div class="text-center">
            <ChipStatus
              :status="slotProps.data.isActive"
              :label="slotProps.data.isActive === 'ใช้งาน' ? 'ใช้งาน' : ''"
            />
          </div>
        </template>
        <!-- ส่วน Icon การจัดการ -->
        <template #options="slotProps">
          <div class="text-center">
            <IconBtn @click="openViewDialog(slotProps.data.contentId)">
              <VIcon icon="mdi-eye-outline" />
              <VTooltip open-delay="500" location="top" activator="parent">ดูข้อมูล</VTooltip>
            </IconBtn>
            <IconBtn @click="openEditDialog(slotProps.data.contentId)">
              <VIcon icon="mdi-pencil-outline" />
              <VTooltip open-delay="500" location="top" activator="parent">แก้ไข</VTooltip>
            </IconBtn>
            <IconBtn @click="ApproveDelete(slotProps.data.contentId)">
              <VIcon icon="mdi-delete-outline" />
              <VTooltip open-delay="500" location="top" activator="parent">ลบข้อมูล</VTooltip>
            </IconBtn>
          </div>
        </template>
      </AppDataTableAPI>
    </VCard>

    <!-- ส่วน Dialog -->
    <EditContent
      v-show="isDialogVisible"
      v-model="isDialogVisible"
      :is-add="isAdd"
      :is-edit="isEdit"
      :is-view="isView"
      :view-id="editId"
      :edit-id="editId"
      @update="onback"
    />
  </div>
</template>

<style scoped>
.no-select {
  user-select: none;
}
</style>
