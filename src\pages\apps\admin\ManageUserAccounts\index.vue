<script setup lang="ts">
import type { Ref } from 'vue'
import { onMounted, reactive, ref } from 'vue'
import { VBtn } from 'vuetify/lib/components/index.mjs'
import type { IGetSystemDepartmentRes } from '@/src/@core/interfaces/UserManagementInterface'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'

// Dependencies
const callAxios = useAxios()
const Swal = useSwal()
const router = useRouter()

// Reactive State
const searchQuery: Ref<string | number> = ref('')
const currentPage = ref(1)
const rowPerPage = ref(20)
const totalCount = ref(0)
const ListItem = ref<IGetSystemDepartmentRes[]>([])

// Breadcrumb Items
const breadcrumbItems = ref([
  { title: 'ผู้ดูแลระบบ', disabled: false, to: '/apps/home' },
  {
    title: 'จัดการบัญชีผู้ใช้งาน',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
])

// Filters
const filter = reactive({
  keyWord: '',
  status: '',
  roleUser: '',
  branch: ''
})

const listFilter = ref([
  {
    name: 'branch',
    type: 'select',
    label: 'หน่วยงาน',
    default: '',
    value: 'id',
    title: 'name',
    items: []
  },
  {
    name: 'status',
    type: 'select',
    label: 'สถานะ',
    default: '',
    value: 'id',
    title: 'name',
    items: [
      { name: 'ทั้งหมด', id: '' },
      { name: 'ใช้งาน', id: 'true' },
      { name: 'ไม่ใช้งาน', id: 'false' }
    ]
  },
  {
    name: 'roleUser',
    type: 'select',
    label: 'สิทธิการใช้งาน',
    default: '',
    title: 'name',
    value: 'id',
    items: [
      { name: 'ทั้งหมด', id: '' },
      { name: 'คณะกรรมการ', id: 1 },
      { name: 'ผู้ใช้งานทั่วไป', id: 2 }
    ]
  },
  {
    name: 'keyWord',
    type: 'text',
    label: 'คำค้น',
    default: '',
    placeholder: 'ระบุ ชื่อผู้ใช้งาน/อีเมล'
  }
])

// Data Table Fields
const listFields = ref([
  { field: 'no', header: 'ลำดับที่', sortable: true },
  { field: 'fullName', header: 'ชื่อผู้ใช้งาน', sortable: true },
  { field: 'orgStructure', header: 'หน่วยงาน', sortable: true },
  { field: 'govermentName', header: 'ศูนย์ราชการสะดวก', sortable: true },
  { field: 'email', header: 'อีเมล', sortable: true },
  { field: 'roleName', header: 'สิทธิการใช้งาน', sortable: true },
  { field: 'isActive', header: 'สถานะ', sortable: true },
  { field: 'createDate', header: 'วันที่สร้าง', sortable: true },
  { field: 'createBy', header: 'ชื่อผู้สร้าง', sortable: true },
  { field: 'updateDate', header: 'วันที่แก้ไข', sortable: true },
  {
    field: 'updateBy',
    header: 'ชื่อผู้แก้ไข',
    sortable: true,
    sortField: 'sortUpdateDate'
  },
  {
    field: 'options',
    header: 'การจัดการ',
    sortable: false,
    style: { textAlign: 'center' }
  }
])

const GetDropDown = async () => {
  try {
    const response = await callAxios.RequestGet('/OtherMaster/DepartmentSubLists')

    if (response.status === 200) {
      const listDepartmentSubLists = listFilter.value.find(filter => filter.name === 'branch')

      if (listDepartmentSubLists) {
        listDepartmentSubLists.items = [{ name: 'ทั้งหมด', id: '' }, ...response.data.response]
      }
    }
  } catch (error) {}
}

// Fetch List Data
const GetList = async () => {
  Swal.fetchLoadingApi()
  try {
    const queryParams = new URLSearchParams({
      Page: currentPage.value.toString(),
      PageSize: rowPerPage.value.toString()
    })

    if (filter.keyWord) queryParams.append('Keyword', filter.keyWord)
    if (filter.status) queryParams.append('Status', filter.status)
    if (filter.branch) queryParams.append('OrgStructure', filter.branch)
    if (filter.roleUser) queryParams.append('Role', filter.roleUser)

    const response = await callAxios.RequestGet(
      `/UserManagement/GetSystemPersonalInfo?${queryParams.toString()}`
    )

    if (response.status === 200) {
      ListItem.value = response.data.response
      totalCount.value = response.data.count
    } else {
      onNotFound()
    }
  } catch (error) {
    onNotFound()
  } finally {
    Swal.close()
  }
}

const fetchRolePermissions = async () => {
  try {
    const response = await callAxios.RequestGet('OtherMaster/GetRolesList')
    if (response.status === 200) {
      const RolePermissions = listFilter.value.find(filter => filter.name === 'roleUser')

      if (RolePermissions) {
        RolePermissions.items = [{ name: 'ทั้งหมด', id: '' }, ...response.data.response]
      }
    }
  } catch (error) {
    console.log('error', error)

    // Swal.callCatch();
  }
}

const onNotFound = () => {
  ListItem.value = []
  totalCount.value = 0
  currentPage.value = 0
  rowPerPage.value = 10
}

const onPageChange = (event: { page: number; rows: number }) => {
  currentPage.value = event.page + 1
  rowPerPage.value = event.rows
  GetList()
}

const ApproveDelete = async (id: number) => {
  const endpoint = `/UserManagement/DeleteSystemPersonalInfo?Id=${id}`
  const response = await Swal.ApproveDelete(endpoint)
  if (response) GetList()
}

const handleEdit = (id: string) => {
  router.push({
    name: 'apps-admin-ManageUserAccounts-Edit',
    query: { id }
  })
}

const handleView = (id: string) => {
  router.push({
    name: 'apps-admin-ManageUserAccounts-View',
    query: { id }
  })
}

onMounted(() => {
  GetDropDown()
  fetchRolePermissions()
  GetList()
})
</script>

<template>
  <div>
    <!-- Breadcrumbs -->
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <FiltersAPI v-model="filter" :fields="listFilter" @submit="GetList" />

    <!-- Data Table -->
    <VCard class="px-4">
      <VCardText>
        <div class="d-flex align-center flex-wrap gap-4">
          <VBtn
            to="/apps/admin/manageuseraccounts/add"
            class="mt-2"
            prepend-icon="mdi-plus-box-multiple"
            color="btn-add"
            rounded="xl"
          >
            เพิ่มรายการ
          </VBtn>
        </div>
      </VCardText>
      <AppDataTableAPI
        :filters="searchQuery"
        :columns="listFields"
        :value="ListItem"
        :total-records="totalCount"
        :header-no="false"
        @page="onPageChange"
      >
        <!-- Status Column -->
        <template #isActive="slotProps">
          <div class="text-center">
            <ChipStatus
              :status="slotProps.data.isActive"
              :label="slotProps.data.isActive ? 'ใช้งาน' : 'ไม่ใช้งาน'"
            />
          </div>
        </template>

        <!-- Action Column -->
        <template #options="slotProps">
          <div class="text-center">
            <IconBtn @click="handleView(slotProps.data.id)">
              <VIcon icon="mdi-eye-outline" />
              <VTooltip open-delay="500" location="top" activator="parent">ดูข้อมูล</VTooltip>
            </IconBtn>
            <IconBtn @click="handleEdit(slotProps.data.id)">
              <VIcon icon="mdi-pencil-outline" />
              <VTooltip open-delay="500" location="top" activator="parent">แก้ไข</VTooltip>
            </IconBtn>
            <IconBtn @click="ApproveDelete(slotProps.data.id)">
              <VIcon icon="mdi-delete-outline" />
              <VTooltip open-delay="500" location="top" activator="parent">ลบข้อมูล</VTooltip>
            </IconBtn>
          </div>
        </template>
      </AppDataTableAPI>
    </VCard>
  </div>
</template>

<style scoped>
.no-select {
  user-select: none;
}
</style>
