<script setup lang="ts">
import { ref } from 'vue'

const listFields = ref([
  {
    field: 'listFields01',
    header: 'รายละเอียด',
    sortable: false
  },
  {
    field: 'listFields02',
    header: 'การดำเนินการ',
    sortable: false
  },
  {
    field: 'listFields03',
    header: 'กรอกข้อมูลใบสมัคร',
    sortable: false
  },
  {
    field: 'listFields04',
    header: 'ผลการประเมิน/หมายเหตุ',
    sortable: false
  }
])

const listFieldsFile = ref([
  {
    field: 'fileName',
    header: 'สิ่งอำนวยความสะดวก',
    sortable: false
  },
  {
    field: 'refFile',
    header: 'รายการไฟล์',
    sortable: false
  },
  {
    field: 'createDate',
    header: 'วันที่บันทึก',
    sortable: false
  },
  {
    field: 'options',
    header: 'การจัดการ',
    sortable: false
  }
])

const itemsdata = ref([
  {
    id: '86c17f01-3c33-4b8e-9308-0fdc19912a9b',
    listFields01: 'รายละเอียด',
    listFields02: 'การดำเนินการ',
    listFields03: [
      {
        id: '6706a6b1-8d9f-489c-b7e9-0a4c8cd3e8de',
        fileName: 'WithholdingTax.pdf',
        refFile: 'file01.pdf',
        createDate: '10/10/2567 เวลา 14:32 น.'
      },
      {
        id: '8004173f-f221-4ee2-b1ea-fcd8a67305bd',
        fileName: '11708e69bdca.docx',
        refFile: 'file01.pdf',
        createDate: '10/10/2567 เวลา 14:32 น.'
      }
    ],
    listFields04: {
      isStatusUpdate: 2,
      howProceed: 'testtesttesttest'
    }
  }
])
</script>

<template lang="">
  <div>
    <AppDataTable :paginator="false" :columns="listFields" :value="itemsdata">
      <template #listFields03="slotProps">
        <AppDataTable
          :paginator="false"
          :columns="listFieldsFile"
          :value="slotProps.data.listFields03"
        />
      </template>
      <template #listFields04="slotProps">
        <VRadioGroup v-model="slotProps.data.listFields04.isStatusUpdate" class="mb-5">
          <VRadio label="1 คะแนน" :value="0" />
          <VRadio label="2 คะแนน" :value="1" />
          <VRadio label="ไม่ผ่าน" :value="2" />
          <VRadio label="รอประเมิน" :value="3" />
        </VRadioGroup>

        <VTextarea v-model="slotProps.data.listFields04.howProceed" rows="3" />
      </template>
    </AppDataTable>
  </div>
</template>

<style lang=""></style>
