<script setup>
import { useRoute } from 'vue-router'
import LogoMOC from '@images/logos/Logo_MOC.png'
import { useAxios } from '@/store/useAxios'
import { formatDateThai } from '@core/utils/formatters'
import { useSwal } from '@/store/useSwal'

const route = useRoute()
const Swal = useSwal()
const queryData = route.query.date.split(',')
const callAxios = useAxios()
const currentDate = new Date()

const year = currentDate.getFullYear()
const month = currentDate.getMonth() + 1 // เดือนเริ่มที่ 0
const day = currentDate.getDate()

const formattedDate = `${year}-${month}-${day}`

const itemsFilter1 = [
  {
    id: 0,
    name: 'รวมทั้งประเทศ'
  },
  {
    id: 1,
    name: 'รายจังหวัด'
  },
  {
    id: 2,
    name: 'ภาค'
  }
]

const itemsFilter2 = [
  {
    id: 0,
    name: 'รวมทุกหน่วยงาน'
  },
  {
    id: 1,
    name: 'ฝ่ายบริหารงานทั่วไป (บท.)'
  },
  {
    id: 2,
    name: 'กลุ่มยุทธศาสตร์และแผนงาน (ยผ.)'
  },
  {
    id: 3,
    name: 'กลุ่มกำกับและพัฒนาเศรษฐกิจการค้า (กศ.)'
  },
  {
    id: 4,
    name: 'กลุ่มส่งเสริมการประกอบธุรกิจการค้าและการตลาด (สธ.)'
  },
  {
    id: 5,
    name: 'กลุ่มทะเบียนธุรกิจและอำนวยความสะดวกทางการค้า (ทอ.)'
  }
]

const columns = ref([
  {
    field: 'name',
    header: 'ประเด็น',
    sortable: false
  },
  {
    field: 'satis',
    header: 'ระดับความพึงพอใจ (ร้อยละ)',
    sortable: false,
    style: { textAlign: 'center' }
  },
  {
    field: 'criter',
    header: 'เกณฑ์ประเมิน',
    sortable: false,
    style: { textAlign: 'center' }
  }
])

const filters = ref({
  searchProvince: 0,
  searchProvinceById: 0,
  searchRegion: 0,
  searchGroup: 'รวมทุกหน่วยงาน',
  searchValue: 0,
  searchDate: [formattedDate, formattedDate]
})

const solidCardData = [
  {
    cardBg: '#0079FF',
    text: 'สรุปผลความพึงพอใจต่องานบริการของศูนย์บริการประชาชนกระทรวงพาณิชย์',
    textColor: 'white'
  },
  {
    cardBg: '',
    text: 'ร้อยละความพึงพอใจต่อการบริการโดยรวม',
    textColor: 'black'
  }
]

const solidCardDataPick = ref([
  {
    searchProvince: null,
    searchGroup: null,
    searchDate: null,
    searchCount: null
  },
  {
    searchProvince: null,
    searchGroup: null,
    searchCount: null,
    total: 0
  }
])

const dataDashboard = ref({})

const GetList = (
  searchProvince = 0,
  searchValue = 0,
  searchGroup = 'รวมทุกหน่วยงาน',
  date = null
) => {
  if (!date) date = [null, null]

  callAxios
    .RequestGet(
      `/EvaluationForm/GetSurveyDashboard?Type=${searchProvince === null ? 0 : searchProvince}&Value=${searchValue === null ? 0 : searchValue}&Service=${searchGroup}&StartDate=${date[0] ? date[0] : null}&EndDate=${date[1] ? date[1] : null}&EvaluationSystemId=2&EvaluationSystemType=7`
    )
    .then(response => {
      if (response.status == 200) {
        if (
          typeof response.data.response === 'object' &&
          Object.keys(response.data.response).length === 0
        ) {
          Swal.AddConditionFailText('ไม่พบข้อมูลของวันที่กำหนด')
        } else {
          dataDashboard.value = response.data.response
          solidCardDataPick.value[1].total = dataDashboard.value.datasRadarChart.total
          fetchData()
        }
      }
    })
}

const fetchData = () => {
  solidCardDataPick.value[0].searchDate = queryData !== null ? getNameFilterDate(queryData) : null
  solidCardDataPick.value[1].searchCount = 0

  const dateValue = []

  dateValue[0] = queryData !== null ? queryData[0] : null
  dateValue[1] = queryData !== null ? queryData[1] : null
}

function getNameFilterDate(date) {
  return `ตั้งแต่วันที่ ${formatDateThai(date[0])} ถึง ${formatDateThai(date[1])}`
}

const print = () => {
  window.print()
}

onMounted(() => {
  GetList(0, 0, 'รวมทุกหน่วยงาน', queryData)
})
</script>

<template>
  <section>
    <VCardText>
      <VRow>
        <VCol cols="12">
          <div class="d-flex flex-wrap gap-4 justify-end">
            <VBtn class="hide-on-print" prepend-icon="mdi-printer" color="primary" @click="print">
              พิมพ์
            </VBtn>
          </div>
        </VCol>
      </VRow>

      <VRow v-if="dataDashboard.datasList">
        <VCol cols="12">
          <VCard>
            <VCardText>
              <div class="text-center my-2">
                <VImg height="100" :src="LogoMOC" />
              </div>
              <HeaderDashboard
                class="mb-5"
                :solid-card-data="solidCardData[0]"
                :solid-card-data-pick="solidCardDataPick[0]"
              />
              <ListDashboard :datas="dataDashboard.datasList" />
              <AppDataTable
                v-model="dataDashboard.datasTable"
                :columns="columns"
                :header-no="false"
                :paginator="false"
              />
            </VCardText>
          </VCard>
        </VCol>
      </VRow>
      <VRow v-if="dataDashboard.datasBarChartGroup">
        <VCol cols="12">
          <VCard>
            <VCardText>
              <HeaderDashboard
                class="mb-5"
                :solid-card-data="solidCardData[0]"
                :solid-card-data-pick="solidCardDataPick[0]"
              />
              <VRow v-if="dataDashboard.datasHeader">
                <VCol cols="12">
                  <VCard color="#D9EDF7">
                    <VCardText align="center">
                      <p class="clamp-text mb-0">
                        {{ dataDashboard.datasHeader.header }}
                      </p>
                      <h1 class="clamp-text mb-0">{{ dataDashboard.datasHeader.total }} ราย</h1>
                    </VCardText>
                  </VCard>
                </VCol>
              </VRow>
              <VRow>
                <VCol cols="12" md="12" sm="12">
                  <VAlert color="secondary">
                    <h3 class="text-white">
                      {{ dataDashboard.datasBarChartGroup.header }}
                    </h3>
                  </VAlert>
                </VCol>
                <VCol cols="12" md="6" sm="12">
                  <BarChart :datas-bar-chart="dataDashboard.datasBarChartGroup.byGroup" />
                </VCol>
                <VCol cols="12" md="6" sm="12">
                  <BarChart :datas-bar-chart="dataDashboard.datasBarChartGroup.bySex" />
                </VCol>
              </VRow>
              <VRow>
                <VCol v-if="dataDashboard.datasBarChartGroup.byAge" cols="12" md="6" sm="12">
                  <BarChart :datas-bar-chart="dataDashboard.datasBarChartGroup.byAge" />
                </VCol>
                <VCol v-if="dataDashboard.datasBarChartGroup.byEducation" cols="12" md="6" sm="12">
                  <BarChart :datas-bar-chart="dataDashboard.datasBarChartGroup.byEducation" />
                </VCol>
              </VRow>
              <VRow>
                <VCol cols="12" md="12" sm="12">
                  <BarChart :datas-bar-chart="dataDashboard.datasBarChartGroup.byJob" />
                </VCol>
              </VRow>

              <VRow v-if="dataDashboard.datasRadarChart">
                <VCol cols="12" md="12" sm="12">
                  <VAlert color="secondary">
                    <h3 class="text-white">
                      {{ dataDashboard.datasBarChartGroup.header }}
                    </h3>
                  </VAlert>
                </VCol>
                <VCol cols="12" md="12" sm="12">
                  <HeaderDashboard
                    :solid-card-data="solidCardData[1]"
                    :solid-card-data-pick="solidCardDataPick[1]"
                  />
                </VCol>
                <VCol cols="12" md="12" sm="12">
                  <RadarChart :datas-radar-chart="dataDashboard.datasRadarChart.data" />
                </VCol>
              </VRow>
            </VCardText>
          </VCard>
        </VCol>
      </VRow>
    </VCardText>
  </section>
</template>

<style>
@media print {
  .hide-on-print {
    display: none !important;
  }
}
</style>

<route lang="yaml">
meta:
  layout: blank
  action: read
</route>
