<script setup lang="ts">
import { VCol, VRow, VTextField } from 'vuetify/lib/components/index.mjs'
import Footer from '@/layouts/components/Footer.vue'
import { useAppAbility } from '@/plugins/casl/useAppAbility'
import { useAuth } from '@/store/useAuth'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import bgLogin from '@images/pages/bgLogin.png'
import {
  confirmedValidator,
  emailValidator,
  integerValidator,
  passwordValidator,
  requiredValidator
} from '@validators'

const tooltipTitle = 'คำแนะนำในการกำหนดรหัสผ่าน'

const tooltipContent = `
  1. มีความยาว 8-12 ตัวอักษร<br />
  2. ตัวอักษรตัวแรกเป็นตัวพิมพ์ใหญ่<br />
  3. ต้องมีตัวเลข อย่างน้อย 1 ตัว และอักขระพิเศษ (#, ?, !, @, $, %, ^, &, *, -)<br />
  อย่างน้อย 1 ตัวอักษร และตัวเลข (0-9) อย่างน้อย 1 ตัวอักษร<br />
  ตัวอย่าง:Cdd@1234
  `

const callAxios = useAxios()

const Swal = useSwal()
const callAuth = useAuth()
const AppAbility = useAppAbility()
const router = useRouter()
const refVForm = ref()

interface FormType {
  firstName: string
  lastName: string
  title: number | null
  email: string
  role: string
  positionName: string
  password: string
  confirmPassword: string
  govermentId: string
  govermentName: string
  orgGroupId: string
  orgGroupName: string
  orgStructureId: string
  orgStructureName: string
  systemGovermentId: string
  office: string
  branch: string
  transactionServiceTypeId: string
  address: string
  provinceCode: number | null
  districtCode: number | null
  postCode: string
  subDistrictCode: number | null
  isActive: boolean
  isAudit: boolean
  latitude: string
  longitude: string
}

const form: Ref<FormType> = ref({
  firstName: '',
  lastName: '',
  title: null,
  email: '',
  role: '',
  positionName: '',
  password: '',
  confirmPassword: '',
  govermentId: '',
  govermentName: '',
  orgGroupId: '',
  orgGroupName: '',
  orgStructureId: '',
  orgStructureName: '',
  systemGovermentId: '',
  office: '',
  branch: '',
  transactionServiceTypeId: '',
  address: '',
  provinceCode: null,
  districtCode: null,
  postCode: '',
  subDistrictCode: null,
  isActive: true,
  isAudit: true,
  latitude: '',
  longitude: ''
})

const goBack = async () => {
  const confirmed = await Swal.ApproveCancel()
  if (!confirmed) return
  router.go(-1)
}

// computed properties สำหรับแปลงค่าจาก string เป็น number
const latitudeAsNumber = computed(() => {
  // ตรวจสอบและแปลงค่าให้ปลอดภัย
  return form.value.latitude ? parseFloat(form.value.latitude) : 0
})

const longitudeAsNumber = computed(() => {
  // ตรวจสอบและแปลงค่าให้ปลอดภัย
  return form.value.longitude ? parseFloat(form.value.longitude) : 0
})

const isDisabled = ref(true)

const CheckEmail = async (email: string) => {
  if (!email || email.trim() === '') {
    Swal.ApproveConditionclose('รูปแบบอีเมลไม่ถูกต้อง')

    return
  }

  // ตรวจสอบรูปแบบอีเมลโดยใช้ Regular Expression
  const emailPattern = /^[^\s@]+@[a-z]+\.[^\s@]+$/
  if (!emailPattern.test(email)) {
    Swal.ApproveConditionclose('รูปแบบอีเมลไม่ถูกต้อง')

    return
  }

  const thaiRegex = /[\u0E00-\u0E7F]/ // ตรวจสอบอักษรภาษาไทย
  if (thaiRegex.test(email)) {
    Swal.ApproveConditionclose('รูปแบบอีเมลไม่ถูกต้อง')

    return
  }
  try {
    const encodedEmail = encodeURIComponent(email)

    const response = await callAxios.RequestPost('auth/check-email', {
      email
    })

    if (response.status === 200 && response.data) {
      if (response.data.status === true) {
        const confirmed = await Swal.ConditionWarningTextTrue(response.data.message)

        if (!confirmed) return
        router.push({ path: '/apps/login' })
      } else if (response.data.status === false) {
        const confirmed = await Swal.ConditionWarningTextFalse(response.data.message)

        if (!confirmed) return
        AddRegisterAgain() // เรียกฟังก์ชัน AddForm เมื่อ confirmed เป็น true
        isCardRegister.value = false
        isCardRegisterAgain.value = true
        isRegisterAgain.value = true
        isRegisterSuccess.value = false
      } else {
        Swal.ConditionSuccessText(response.data.message)
        isDisabled.value = false
      }
    } else if (response.status === 428 && response.data) {
      Swal.ConditionWarningText(response.data.message)
      isDisabled.value = true
    }
  } catch (error) {
    Swal.ConditionWarningText('อีเมลนี้มีอยู่ในระบบแล้ว กรุณาใช้อีเมลอื่นในการลงทะเบียน')
    isDisabled.value = true
  }
}

const isPasswordVisible = ref(false)
const isConFirmPasswordVisible = ref(false)
const isClearDistrict = ref(false)

// Watch for isClearDistrict changes
watch(
  () => isClearDistrict.value,
  newVal => {
    if (newVal) {
      form.value.districtCode = null // Clear district code
      form.value.subDistrictCode = null // Clear sub-district code
      // form.value.postCode = "";  Clear postcode

      // Reset isClearDistrict back to false if necessary
      isClearDistrict.value = false
    }
  }
)

const listTitle = ref([])

const GetTitleDropDown = () => {
  // callAxios
  //   .RequestGet(`/OtherMaster/SystemTitleMasterLists`)
  //   .then((response) => {
  //     if (response.status == 200) {
  //       listTitle.value = response.data.response;
  //     }
  //   });
}

GetTitleDropDown()

const listGoverment = ref([])

watchEffect(() => {
  if (form.value.orgGroupId && form.value.orgStructureId) GetGovermentDropDown()
})

const GetGovermentDropDown = () => {
  const orgGroupId = form.value.orgGroupId || ''
  const orgStructureId = form.value.orgStructureId || ''

  //  callAxios
  //   .RequestGet(
  //     `/TransactionRegister/GetDropDownGoverment?flag=false&OrgStructureId=${orgStructureId}&OrgGroupId=${orgGroupId}`
  //   )
  //   .then((response) => {
  //     if (response.status == 200) {
  //       listGoverment.value = response.data.response;
  //     }
  //   })
  //   .catch((error) => {
  //     console.error("Error fetching goverment data:", error);
  //   });
}

const listSystemGovermentId = ref([])

const GetSystemGovermentIdDropDown = () => {
  // callAxios.RequestGet(`/OtherMaster/GovermentLists`).then((response) => {
  //   if (response.status == 200) {
  //     listSystemGovermentId.value = response.data.response;
  //   }
  // });
}

GetSystemGovermentIdDropDown()

const listOrgGroup = ref([])

const GetOrgGroupDropDown = () => {
  // callAxios
  //   .RequestGet(`/OtherMaster/DepartmentParrentLists`)
  //   .then((response) => {
  //     if (response.status == 200) {
  //       listOrgGroup.value = response.data.response;
  //     }
  //   });
}

GetOrgGroupDropDown()

const listOrgStructure = ref([])

const GetOrgStructuretDropDown = (idOrgGroup: number | string) => {
  const orgGroupIdParam = idOrgGroup === '' ? '' : idOrgGroup

  // callAxios
  //   .RequestGet(
  //     `/OtherMaster/DepartmentChildLists?OrgGroupId=${orgGroupIdParam}`
  //   )
  //   .then((response) => {
  //     if (response.status === 200) {
  //       listOrgStructure.value = response.data.response;

  //       // ตรวจสอบเงื่อนไข idOrgGroup เป็น "" เพื่อเลือก "อื่นๆ" อัตโนมัติ
  //       if (idOrgGroup === "") {
  //         const otherOption = listOrgStructure.value.find(
  //           (item) => item.id === "1" && item.name === "อื่นๆ"
  //         );
  //         if (otherOption) {
  //           form.value.orgStructureId = otherOption.id; // ตั้งค่าเริ่มต้นเป็น "อื่นๆ"
  //         }
  //       }
  //     }
  //   });
}

watch(
  () => form.value.orgGroupId,
  newOrgGroupId => {
    // ตรวจสอบว่ามีค่า form.orgGroupId
    if (newOrgGroupId) {
      const idOrgGroup = newOrgGroupId === '1' ? '' : newOrgGroupId

      GetOrgStructuretDropDown(idOrgGroup)
      form.value.orgStructureId = ''
    } else {
      listOrgStructure.value = [] // ล้างข้อมูลถ้า orgGroupId ไม่มีค่า
    }
  }
)

const listserviceType = ref([])

const GetServiceTypeDropDown = () => {
  // callAxios.RequestGet(`/OtherMaster/ServiceTypeList`).then((response) => {
  //   if (response.status == 200) {
  //     listserviceType.value = response.data.response;
  //   }
  // });
}

GetServiceTypeDropDown()

const listProvice = ref([])

const GetProvincesDropDown = () => {
  // callAxios.RequestGet(`/OtherMaster/ProvinceLists`).then((response) => {
  //   if (response.status == 200) {
  //     listProvice.value = response.data.response;
  //   }
  // });
}

GetProvincesDropDown()

watch(
  () => form.value.provinceCode,
  (newProvinceCode, oldProvinceCode) => {
    if (newProvinceCode && newProvinceCode !== oldProvinceCode) {
      GetDistrictDropDown(newProvinceCode)

      if (isWatch.value) {
        form.value.districtCode = null // Clear sub-districts list
        form.value.subDistrictCode = null // Clear sub-districts list
        form.value.postCode = '' // Clear sub-districts list
      }

      if (!isWatch.value) form.value.subDistrictCode = null // Clear sub-districts list
    } else {
      listDistrict.value = [] // Clear districts if no province is selected
    }
  }
)

const listDistrict = ref([])

const GetDistrictDropDown = (idProvince: number) => {
  // callAxios
  //   .RequestGet(`/OtherMaster/DistrictLists?Province=${idProvince}`)
  //   .then((response) => {
  //     if (response.status === 200) {
  //       listDistrict.value = response.data.response;
  //     }
  //   });
}

watch(
  () => form.value.districtCode,
  (newDistrictCode, oldDistrictCode) => {
    if (newDistrictCode && newDistrictCode !== oldDistrictCode && form.value.provinceCode) {
      GetSubDistrictDropDown(form.value.provinceCode, newDistrictCode)

      if (isWatch.value) {
        form.value.subDistrictCode = null // Clear sub-districts list
        form.value.postCode = '' // Clear sub-districts list
      }

      if (!isWatch.value) form.value.subDistrictCode = null // Clear sub-districts list
    } else {
      listSubDistrict.value = [] // Clear sub-districts if district is cleared
      form.value.subDistrictCode = null // Clear sub-district value
    }
  }
)

const listSubDistrict = ref([])

const GetSubDistrictDropDown = (idProvince: number, idDistrict: number) => {
  // callAxios
  //   .RequestGet(
  //     `/OtherMaster/SubDistrictLists?District=${idDistrict}&Province=${idProvince}`
  //   )
  //   .then((response) => {
  //     if (response.status === 200) {
  //       listSubDistrict.value = response.data.response;
  //       isWatch.value = true;
  //       // form.value.postCode = listSubDistrict.value[0].postCode;
  //     }
  //   });
}

const GetPostcode = async (PostCode: string) => {
  try {
    // ตรวจสอบว่ามี subDistrictCode หรือไม่ ถ้าไม่มีให้ใช้ค่า "0"
    const subDistrictCodeParam = `&SubDistrict=${form.value.subDistrictCode || '0'}`

    // ส่งคำขอไปยัง API พร้อมกับ PostCode และ SubDistrict
    const response = await callAxios.RequestGet(
      `/OtherMaster/PostcodeLists?PostCode=${PostCode}${subDistrictCodeParam}`
    )

    if (response.status === 200) {
      form.value.provinceCode = response.data.response.provinceCode
      form.value.districtCode = response.data.response.districtCode
    }
  } catch (error) {
    console.error('Error fetching postcode data:', error)
  }
}

// watch เพื่อตรวจสอบการเปลี่ยนแปลงของค่า form.postCode
const handlePostCodeKeyup = () => {
  const postCode = form.value.postCode
  if (postCode && postCode.length === 5) {
    GetPostcode(postCode)
    isWatch.value = false
  }
}

const isWatch = ref(true)

watch(
  () => form.value.subDistrictCode,
  newSubDistrictCode => {
    if (newSubDistrictCode) {
      const selectedSubDistrict = listSubDistrict.value.find(item => item.id === newSubDistrictCode)

      if (selectedSubDistrict) {
        form.value.postCode = selectedSubDistrict.postCode // อัปเดตค่า postCode
        isWatch.value = true // ตั้งค่า isWatch เป็น false หลังจากอัปเดตค่า postCode
        isClearDistrict.value = false
      }
    }
  }
)

const isRegisterAgain = ref(false)
const isRegisterSuccess = ref(false)

const AddForm = async () => {
  try {
    const confirmResponse = await Swal.ApproveConfirm()

    if (confirmResponse) {
      const data = form.value

      const response = await callAxios.RequestPost('/Authenticate/Register', data)

      if (response.status === 200) {
        // goBack();
        isCardRegister.value = false
        isCardRegisterAgain.value = true
        isRegisterAgain.value = true
        isRegisterSuccess.value = false
      } else {
        // Handle non-200 responses
        Swal.AddConditionFailText(response.data?.message || 'Request failed. Please try again.')
      }
    }
  } catch (error) {
    // Handle request errors
    Swal.AddConditionFailText(
      error.response?.data?.message || 'Bad request. Please check your input and try again.'
    )
  }
}

const onSubmit = () => {
  refVForm.value?.validate().then(({ valid: isValid }) => {
    if (isValid) AddForm()
  })
}

const isCardRegister = ref(true)
const isCardRegisterAgain = ref(false)

const countdown = ref(10800) // เริ่มต้นที่ 3 ชั่วโมง

const startCountdown = () => {
  const interval = setInterval(() => {
    if (countdown.value > 0) countdown.value--
    else clearInterval(interval) // หยุดการนับถอยหลังเมื่อถึง 0
  }, 1000) // อัปเดตทุก ๆ 1 วินาที
}

// เฝ้าดู isCardRegisterAgain
watch(
  () => isCardRegisterAgain.value,
  newValue => {
    if (newValue) startCountdown() // เรียก startCountdown เมื่อค่าเปลี่ยนเป็น true
  }
)

// แปลงวินาทีเป็นรูปแบบ mm:ss
const formattedTime = computed(() => {
  const minutes = Math.floor(countdown.value / 60)
    .toString()
    .padStart(2, '0')

  const seconds = (countdown.value % 60).toString().padStart(2, '0')

  return `${minutes}:${seconds}`
})

const AddRegisterAgain = async () => {
  try {
    const data = {
      email: form.value.email,
      password: '******' // ส่ง password ที่กำหนดไว้
    }

    const response = await callAxios.RequestPost('/Authenticate/RegisterAgain', data)

    if (response.status === 200) {
      // รีเซ็ต countdown กลับไปที่ค่าเริ่มต้น
      countdown.value = 10800 // ตั้งเวลาใหม่เป็น 3 ชั่วโมง
      startCountdown() // เริ่มการนับถอยหลังใหม่

      Swal.ConditionSuccessText(
        response.data.message || 'Successfully resent the confirmation email.'
      )
    } else {
      // Handle non-200 responses
      Swal.AddConditionFailText(response.data?.message || 'Request failed. Please try again.')
    }
  } catch (error) {
    // Handle request errors
    Swal.AddConditionFailText(
      error.response?.data?.message || 'Bad request. Please check your input and try again.'
    )
  }
}

const isRecaptchaVerified = ref(false)

// Callback เมื่อผู้ใช้ Verify สำเร็จ
const onVerify = (token: string) => {
  console.log('reCAPTCHA verified! Token:', token)
  isRecaptchaVerified.value = true // เปลี่ยนสถานะเมื่อ Verify สำเร็จ
}

const isDialogVisible = ref(false)

const onFormReset = () => {
  isDialogVisible.value = false
}

// Watchers to fetch data ศูนย์ราชการสะดวก
const isDisabledGoverment = ref(true)

watch(
  () => form.value.govermentId,
  async newgovermentId => {
    if (newgovermentId === '1') isDisabledGoverment.value = false
    else isDisabledGoverment.value = true
  }
)
</script>

<template>
  <HeaderPublic />

  <RegisterStep :is-register-again="isRegisterAgain" :is-register-success="isRegisterSuccess" />

  <div class="auth-wrapper d-flex">
    <VContainer>
      <VRow class="pt-10">
        <VCol cols="12" justify="center">
          <VCard v-if="isCardRegister" class="auth-card login-card pa-2 pt-7 mb-5">
            <VCardText>
              <VForm ref="refVForm" @submit.prevent="onSubmit">
                <VRow class="align-center">
                  <VCol cols="12" md="6" class="py-0">
                    <label>
                      ชื่อผู้ใช้งาน (อีเมลหน่วยงาน) :
                      <small class="text-error">*</small>
                    </label>
                    <VTextField
                      v-model="form.email"
                      density="compact"
                      :counter="100"
                      :maxlength="100"
                      type="text"
                      :rules="[emailValidator]"
                      placeholder="ตัวอย่าง : <EMAIL>"
                    />
                  </VCol>

                  <VCol cols="12" md="6" class="py-0">
                    <!-- login button -->
                    <VBtn
                      color="error-200"
                      class="text-white"
                      type="button"
                      @click="CheckEmail(form.email)"
                    >
                      ตรวจสอบอีเมล
                      <VTooltip open-delay="500" location="top" activator="parent">
                        ตรวจสอบอีเมล
                      </VTooltip>
                    </VBtn>
                  </VCol>
                </VRow>
                <VRow class="align-start">
                  <VCol cols="12" md="6" class="py-0 mb-4">
                    <label>
                      รหัสผ่าน:
                      <small class="text-error">*</small>
                    </label>
                    <VTextField
                      v-model="form.password"
                      density="compact"
                      :type="isPasswordVisible ? 'text' : 'password'"
                      :append-inner-icon="
                        form.password
                          ? isPasswordVisible
                            ? 'mdi-eye-off-outline'
                            : 'mdi-eye-outline'
                          : ''
                      "
                      :counter="20"
                      :maxlength="20"
                      :readonly="isDisabled"
                      :bg-color="isDisabled ? 'grey-secondary' : undefined"
                      :rules="isDisabled ? [] : [passwordValidator]"
                      @click:append-inner="isPasswordVisible = !isPasswordVisible"
                    />
                    <span v-if="!form.password" class="guidTooltip">
                      <small class="text-error">ดูคำแนะนำในการกำหนดรหัสผ่าน</small>
                      <TooltipFormDialog :title="tooltipTitle" :content="tooltipContent" />
                    </span>
                    <span v-else class="guidTooltip" />
                  </VCol>

                  <VCol cols="12" md="6" class="py-0">
                    <label>
                      ยืนยันรหัสผ่าน:
                      <small class="text-error">*</small>
                    </label>
                    <VTextField
                      v-model="form.confirmPassword"
                      density="compact"
                      :type="isConFirmPasswordVisible ? 'text' : 'password'"
                      :append-inner-icon="
                        form.confirmPassword
                          ? isConFirmPasswordVisible
                            ? 'mdi-eye-off-outline'
                            : 'mdi-eye-outline'
                          : ''
                      "
                      :counter="20"
                      :maxlength="20"
                      :bg-color="isDisabled ? 'grey-secondary' : undefined"
                      :readonly="isDisabled"
                      :rules="[
                        requiredValidator,
                        () => confirmedValidator(form.confirmPassword, form.password)
                      ]"
                      @click:append-inner="isConFirmPasswordVisible = !isConFirmPasswordVisible"
                    />
                  </VCol>
                  <VCol cols="6" md="6" class="py-0">
                    <label>
                      สิทธิการใช้งาน :
                      <small class="text-error">*</small>
                    </label>
                    <VAutocomplete
                      v-model="form.title"
                      :rules="isDisabled ? [] : [requiredValidator]"
                      :items="listTitle"
                      item-title="name"
                      item-value="id"
                      density="compact"
                      :readonly="isDisabled"
                      :bg-color="isDisabled ? 'grey-secondary' : undefined"
                      no-data-text="ไม่มีข้อมูล"
                      placeholder="ตัวอย่าง : นาย"
                    />
                  </VCol>
                  <VCol cols="6" md="6" class="py-0">
                    <label>
                      หน่วยงาน :
                      <small class="text-error">*</small>
                    </label>
                    <VAutocomplete
                      v-model="form.title"
                      :rules="isDisabled ? [] : [requiredValidator]"
                      :items="listTitle"
                      item-title="name"
                      item-value="id"
                      density="compact"
                      :readonly="isDisabled"
                      :bg-color="isDisabled ? 'grey-secondary' : undefined"
                      no-data-text="ไม่มีข้อมูล"
                      placeholder="ตัวอย่าง : นาย"
                    />
                  </VCol>
                  <VCol cols="12" md="2" class="py-0">
                    <label>
                      คำนำหน้า :
                      <small class="text-error">*</small>
                    </label>
                    <VAutocomplete
                      v-model="form.title"
                      :rules="isDisabled ? [] : [requiredValidator]"
                      :items="listTitle"
                      item-title="name"
                      item-value="id"
                      density="compact"
                      :readonly="isDisabled"
                      :bg-color="isDisabled ? 'grey-secondary' : undefined"
                      no-data-text="ไม่มีข้อมูล"
                      placeholder="ตัวอย่าง : นาย"
                    />
                  </VCol>
                  <VCol cols="12" md="4" class="py-0">
                    <label>
                      ชื่อ :
                      <small class="text-error">*</small>
                    </label>
                    <VTextField
                      v-model="form.firstName"
                      :counter="50"
                      :maxlength="50"
                      density="compact"
                      type="text"
                      :bg-color="isDisabled ? 'grey-secondary' : undefined"
                      :readonly="isDisabled"
                      :rules="isDisabled ? [] : [requiredValidator]"
                      placeholder="ตัวอย่าง : สมศักดิ์"
                    />
                  </VCol>

                  <VCol cols="12" md="6" class="py-0">
                    <label>
                      นามสกุล :
                      <small class="text-error">*</small>
                    </label>
                    <VTextField
                      v-model="form.lastName"
                      :counter="50"
                      :maxlength="50"
                      density="compact"
                      type="text"
                      :bg-color="isDisabled ? 'grey-secondary' : undefined"
                      :readonly="isDisabled"
                      :rules="isDisabled ? [] : [requiredValidator]"
                      placeholder="ตัวอย่าง : คงผาศักดิ์"
                    />
                  </VCol>

                  <VCol cols="12" md="6" class="py-0">
                    <label>
                      จังหวัด:
                      <small class="text-error">*</small>
                    </label>
                    <VAutocomplete
                      v-model="form.provinceCode"
                      class="mb-4"
                      :rules="isDisabled ? [] : [requiredValidator]"
                      :items="listProvice"
                      item-title="name"
                      item-value="id"
                      density="compact"
                      :readonly="isDisabled"
                      :bg-color="isDisabled ? 'grey-secondary' : undefined"
                      no-data-text="ไม่มีข้อมูล"
                      placeholder="ตัวอย่าง : นนทบุรี"
                    />
                  </VCol>
                  <VCol cols="12" md="6" class="py-0">
                    <label>
                      เขต/อำเภอ :
                      <small class="text-error">*</small>
                    </label>
                    <VAutocomplete
                      v-model="form.districtCode"
                      :rules="isDisabled ? [] : [requiredValidator]"
                      :items="listDistrict"
                      item-title="name"
                      item-value="id"
                      density="compact"
                      :readonly="isDisabled"
                      :bg-color="isDisabled ? 'grey-secondary' : undefined"
                      no-data-text="ไม่มีข้อมูล"
                      placeholder="ตัวอย่าง : เมืองนนทบุรี"
                    />
                  </VCol>

                  <VCol cols="12" md="6" class="py-0">
                    <label>
                      แขวง/ตำบล :
                      <small class="text-error">*</small>
                    </label>
                    <VAutocomplete
                      v-model="form.subDistrictCode"
                      :rules="isDisabled ? [] : [requiredValidator]"
                      :items="listSubDistrict"
                      item-title="name"
                      item-value="id"
                      density="compact"
                      :readonly="isDisabled"
                      :bg-color="isDisabled ? 'grey-secondary' : undefined"
                      no-data-text="ไม่มีข้อมูล"
                      placeholder="ตัวอย่าง : บางกระสอ"
                    />
                  </VCol>

                  <VCol cols="12" md="6" class="py-0">
                    <label>
                      รหัสไปรษณีย์ :
                      <small class="text-error">*</small>
                    </label>
                    <VTextField
                      v-model="form.postCode"
                      density="compact"
                      :counter="5"
                      :maxlength="5"
                      type="text"
                      :readonly="isDisabled"
                      :bg-color="isDisabled ? 'grey-secondary' : undefined"
                      :rules="isDisabled ? [] : [requiredValidator, integerValidator]"
                      placeholder="ตัวอย่าง : 11000"
                      @keyup="handlePostCodeKeyup"
                    />
                  </VCol>

                  <VCol cols="12" role="presentation">
                    <div class="text-center">
                      <Recaptcha @verify="onVerify" />
                      <label class="text-white" for="g-recaptcha-response">
                        Captcha Verification
                      </label>
                    </div>
                  </VCol>
                  <VCol cols="12" class="d-flex flex-wrap justify-center">
                    <div class="demo-space-x">
                      <VBtn type="submit" color="blue-600" :disabled="!isRecaptchaVerified">
                        ลงทะเบียน
                      </VBtn>
                      <VBtn color="error" @click="goBack">
                        <Icon />
                        ยกเลิก
                      </VBtn>
                    </div>
                  </VCol>
                </VRow>
              </VForm>
            </VCardText>
          </VCard>

          <VCard v-if="isCardRegisterAgain" class="auth-card login-card pa-2 pt-7 mb-5">
            <VCardText class="text-center">
              <img src="@images/subscribe.png" alt="RegisterSuccess" />
              <h3 class="text-h6">
                ระบบส่งลิงก์ยืนยันไปทาง E-mail
                <span class="text-blue-200">{{ form.email }}</span>
                แล้ว
                <br />
                กรุณาคลิกลิงก์ที่ได้รับเพื่อเป็นการยืนยันตัวตน
              </h3>
              <p v-if="countdown > 0">ส่งลิงก์ยืนยันซ้ำอีกครั้ง {{ formattedTime }}</p>
              <p v-else>
                ส่งลิงก์ยืนยันซ้ำอีกครั้ง
                <VBtn color="error-200" class="text-dark" type="button" @click="AddRegisterAgain">
                  ส่งรหัสยืนยัน
                  <VTooltip open-delay="500" location="top" activator="parent">
                    ส่งรหัสยืนยัน
                  </VTooltip>
                </VBtn>
              </p>
              <!-- <p>ส่งลิงก์ยืนยันซ้ำอีกครั้ง {{ formattedTime }}</p> -->

              <VBtn color="error-300 mt-4" @click="goBack">ยกเลิก</VBtn>
            </VCardText>
          </VCard>
        </VCol>

        <VCol cols="12">
          <Footer />
        </VCol>
      </VRow>
    </VContainer>

    <VDialog v-model="isDialogVisible" persistent class="v-dialog-lg" no-click-animation scrollable>
      <VCard title="วิธีการกรอกข้อมูลละติจูด, ลองจิจูด">
        <DialogCloseBtn variant="text" size="small" @click="onFormReset" />

        <VCardText>
          <ol>
            <li>
              เปิด
              <a
                href="https://www.google.co.th/maps/@18.3170581,99.3986862,17z?hl=th"
                target="_blank"
                rel="noopener noreferrer"
                aria-label="เปิดตำแหน่งนี้ใน Google Maps (เปิดแท็บใหม่)"
              >
                Google Maps :
              </a>

              <a
                href="https://www.google.co.th/maps/@18.3170581,99.3986862,17z?hl=th"
                target="_blank"
                rel="หรือเข้าผ่าน URL"
                aria-label="หรือเข้าผ่าน URL"
              >
                https://www.google.co.th/maps/@18.3170581,99.3986862,17z?hl=th
              </a>
              <br />
              และค้นหาตำแหน่งของศูนย์บริการประชาชน
            </li>
            <li>
              Copy ตัวเลขที่เป็นตำแหน่งของ ละติจูด, ลองจิจูด ดังภาพ

              <img src="@images/latlong.png" alt="วิธีการกรอกข้อมูลละติจูด, ลองจิจูด" />
            </li>
            <li>นำตัวเลขดังกล่าวมากรอกที่ช่อง ละติจูด, ลองจิจูด</li>
          </ol>
          <div class="d-flex flex-wrap justify-center mt-5">
            <div class="demo-space-x">
              <VBtn color="grey-900" @click="onFormReset">
                <VIcon icon="mdi-close" />
                ปิด
              </VBtn>
            </div>
          </div>
        </VCardText>
      </VCard>
    </VDialog>

    <VImg :src="bgLogin" role="presentation" class="d-none d-md-block auth-footer-mask" />
    <div class="auth-logo" />
    <GovWebsiteAccess />
  </div>
</template>

<style lang="css">
@import '@public/assets/css/style.css';
@import '@public/assets/css/skin/skin-2.css';
</style>

<style lang="css" scoped>
li {
  list-style: inherit;
}

.v-container {
  position: relative;
  z-index: 1;
}

.auth-footer-mask {
  inset-block-end: 0% !important;

  /* เปลี่ยนค่าเมื่อหน้าจอเล็กลง */
}

.guidTooltip {
  position: absolute;
  margin-block-start: -20px;
}
</style>

<style lang="scss">
@use '@core/scss/template/pages/page-auth.scss';
</style>

<style lang="scss" scoped>
.layout-blank {
  .auth-wrapper {
    min-block-size: calc(var(--vh, 1vh) * 0);

    &::before {
      position: absolute;

      /* Example background */
      z-index: 1;
      display: block;
      background: linear-gradient(
        180deg,
        rgb(var(--v-theme-blue-100)) 0%,
        rgba(255, 255, 255, 0%) 100%
      );
      block-size: 50%;
      content: '';
      inline-size: 100%;
      inset-block-start: 0;
      inset-inline-start: 0;

      /* Make sure it's behind other content */
    }
  }

  .auth-card {
    z-index: 1 !important;
  }
}
</style>

<route lang="yaml">
meta:
  layout: blank
  action: read
  subject: Auth
  redirectIfLoggedIn: true
</route>
