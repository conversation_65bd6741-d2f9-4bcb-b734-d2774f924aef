<script setup lang="ts">
interface breadcrumbItems {
  title: string
  disabled: boolean
  to: string
  active: boolean
  activeClass?: string
}

const breadcrumbItems: breadcrumbItems[] = [
  {
    title: 'แบบประเมินความพึงพอใจ',
    disabled: false,
    to: '/apps/home',
    active: false,
    activeClass: ''
  },
  {
    title: 'KBP Survey',
    disabled: false,
    to: '',
    active: false,
    activeClass: ''
  },
  {
    title: 'แบบประเมินความพึงพอใจการให้บริการ',
    disabled: false,
    to: '',
    active: false,
    activeClass: ''
  },
  {
    title: 'รายการแบบประเมินความพึงพอใจการให้บริการศูนย์บริการ (รายกลุ่ม)',
    disabled: false,
    to: '',
    active: true,
    activeClass: 'text-info'
  }
]
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <BtnGoBack />
    <EvaluationFormResult
      is-answer
      title="แบบประเมินความพึงพอใจการให้บริการของสำนักงานพาณิชย์จังหวัด"
    />
  </div>
</template>
