<script setup>
import { useAxios } from '@/store/useAxios'

const callAxios = useAxios()
const items = ref([])

const breadcrumbItems = [
  {
    title: 'แบบประเมินความพึงพอใจ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'MOCSC Survey',
    disabled: false,
    to: ''
  },
  {
    title: 'แบบประเมินความพึงพอใจการให้บริการศูนย์บริการประชาชน',
    disabled: false,
    to: ''
  },
  {
    title: 'สร้างแบบประเมินความพึงพอใจการให้บริการศูนย์บริการประชาชน (รายบุคคล)',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const isDialogDisable = ref(false)

const submitDialogFormEvaluation = event => {
  callAxios.RequestPost('/EvaluationForm/AddMOCCSurveyPersonal', event).then(response => {
    if (response.status == 200) {
      isDialogDisable.value = true
      GetMOCSCSSurveyPersonals()
    }
  })
}

const GetMOCSCSSurveyPersonals = () => {
  callAxios.RequestGet('/EvaluationForm/GetMOCSCSSurveyPersonals').then(response => {
    if (response.status == 200) setAttribute.value.items = response.data.response
  })
}

const setAttribute = ref({
  urlFilter: 'GetMOCSCSSurveyPersonals',
  toQR: 'apps-SatisfactionAssessmentForm-MOCSCSurvey-Midfielder-ListIndividualSurvey-QRcode-id',
  toPrint: 'apps-SatisfactionAssessmentForm-MOCSCSurvey-Midfielder-ListIndividualSurvey-Example-id',
  toTransaction:
    'apps-SatisfactionAssessmentForm-MOCSCSurvey-Midfielder-ListIndividualSurvey-Transaction-id',
  to: 'apps-SatisfactionAssessmentForm-MOCSCSurvey-Midfielder-ListIndividualSurvey-id',
  items: []
})

onMounted(() => {
  GetMOCSCSSurveyPersonals()
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <BtnGoBack />
    <ListsTable
      v-bind="setAttribute"
      v-model:isDialogDisable="isDialogDisable"
      @submit="event => submitDialogFormEvaluation(event)"
      @update:modelValue="GetMOCSCSSurveyPersonals"
    />
  </div>
</template>
