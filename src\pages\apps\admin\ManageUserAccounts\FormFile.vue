<script setup>
import Tooltip from '@core/components/Public/Tooltip.vue'

const { modelValue, fields } = defineProps({
  modelValue: [Object],
  fields: [Array]
})

const emit = defineEmits(['update:modelValue', 'submit', 'submitNewEmail', 'submitChangePassword'])

const tooltipTitle = 'เงื่อนไขใหม่สำหรับรหัสผ่าน'

const tooltipContent = `
  1. ระบุค่าละติจูดในช่องที่กำหนด เช่น ละติจูด(Latitude): 13.756331<br />
  2. ระบุค่าลองจิจูดในช่องที่กำหนด เช่น ลองจิจูด(Longitude): 100.501762<br />
`

const isPasswordVisible = ref(false)
const isConfirmPasswordVisible = ref(false)
const elementFields = ref(Array)
const flattenFields = ref(Array)

const fieldsDefault = reactive([{}])

// Improved initial values generation
const initialValues = modelValue ? ref(unref(modelValue)) : {}

elementFields.value = fields || fieldsDefault
flattenFields.value = fields?.flat() || fieldsDefault.flat()

// Enhanced initial values setting
const setInitialValues = () => {
  ;(fields?.flat() || fieldsDefault).forEach(field => {
    switch (field.type) {
      case 'number':
        initialValues[field.name] = field.default || 0
        break
      case 'date':
        initialValues[field.name] = field.default || new Date()
        break
      case 'dateRange':
        initialValues[field.name] = field.default || [new Date(), new Date()]
        break
      case 'select':
        initialValues[field.name] =
          field.default || (field.items && field.items.length ? field.items[0].id : null)
        break
      case 'text':
        initialValues[field.name] = field.default || ''
        break
      case 'password':
        initialValues[field.name] = field.default || ''
        break
      case 'datetime':
        initialValues[field.name] = field.default || new Date()
        break
    }
  })
}

// Call the initial values setting function
setInitialValues()

const fieldValues = ref(initialValues)

// Improved watch mechanism
watch(
  fieldValues.value,
  newValues => {
    console.log('Value is changed!')
    emit('submit', newValues)

    // Uncomment if you want to update modelValue
    // emit("update:modelValue", newValues);
  },
  { deep: true }
)

const onSubmitNewEmail = () => {
  console.log('Submit new email')
  emit('submitNewEmail', fieldValues.value)
}

const onSubmitChangePassword = () => {
  console.log('Submit change password')
  emit('submitChangePassword', fieldValues.value)
}

// Error messages handling
const activeField = ref(null)

const getErrorMessages = fieldName => {
  // ถ้าฟิลด์นี้ไม่ใช่ฟิลด์ที่กำลังแก้ไข ให้ return empty array
  if (activeField.value !== fieldName) return []

  const field = elementFields.value.find(f => f.name === fieldName)
  if (field && field.rules) {
    const value = fieldValues.value[fieldName]

    return field.rules.map(rule => rule(value)).filter(message => typeof message === 'string')
  }

  return []
}

// เพิ่มเมธอดเพื่ออัปเดต active field
const onFieldFocus = fieldName => {
  activeField.value = fieldName
}

const shouldShowValidation = ref(false)

const triggerValidation = () => {
  shouldShowValidation.value = true
}

const resetValidation = () => {
  shouldShowValidation.value = false
}

// Function to evaluate display condition
const shouldDisplayField = field => {
  // If no display property, default to true
  if (field.display === undefined) return true

  if (typeof field.display === 'boolean') return field.display

  if (typeof field.display === 'function') return field.display(fieldValues.value)

  return true
}
</script>

<template>
  <VCardText class="pb-0">
    <VRow class="align-start mb-7 py-0">
      <!-- Render Form Fields -->
      <template v-for="(field, i) in fields" :key="i">
        <!-- Text Fields -->
        <template v-if="field.type === 'text' && shouldDisplayField(field)">
          <VCol :cols="field.col" :md="field.md ?? 6" :sm="field.sm ?? 6">
            <label>
              {{ field.label }} :
              <small v-if="field.required" class="text-error">*</small>
            </label>
            <VTextField
              v-model="fieldValues[field.name]"
              :rules="!field.disabled ? field.rules : []"
              :readonly="field.disabled"
              :bg-color="field.disabled ? 'grey-secondary' : ''"
              :error-messages="!field.disabled ? getErrorMessages(field.name) : []"
              density="default"
              :placeholder="field.placeholder"
              :counter="field.counter ?? null"
              :maxlength="field.counter ?? null"
              class="input-margin-top"
              :class="[{ 'show-validation': shouldShowValidation }]"
              @focus="onFieldFocus(field.name)"
              @ValidationFocus="triggerValidation"
              @ValidationReset="resetValidation"
            />

            <small v-if="field.remark" class="text-error">{{ field.remark }}</small>
            <Tooltip v-if="field.tooltip" :title="field.tooltip" :content="field.tooltipContent" />
          </VCol>
        </template>
        <template v-if="field.type === 'password' && shouldDisplayField(field)">
          <VCol :cols="field.col" :md="field.md ?? 6" :sm="field.sm ?? 6">
            <label>
              {{ field.label }} :
              <small v-if="field.required" class="text-error">*</small>
            </label>
            <VTextField
              v-model="fieldValues[field.name]"
              :rules="field.rules"
              :readonly="field.disabled"
              :bg-color="field.disabled ? 'grey-secondary' : ''"
              :type="isPasswordVisible ? 'text' : 'password'"
              :counter="field.counter ?? null"
              :maxlength="field.counter ?? null"
              :append-inner-icon="
                fieldValues[field.name]
                  ? isPasswordVisible
                    ? 'mdi-eye-off-outline'
                    : 'mdi-eye-outline'
                  : null
              "
              :persistent-placeholder="true"
              placeholder="ตัวอย่าง : 12345678"
              @click:append-inner="
                fieldValues[field.name] && (isPasswordVisible = !isPasswordVisible)
              "
            />
            <small v-if="field.remark" class="text-error">{{ field.remark }}</small>
            <Tooltip v-if="field.tooltip" :title="field.tooltip" :content="field.tooltipContent" />
          </VCol>
        </template>

        <template v-if="field.type === 'confirmpassword' && shouldDisplayField(field)">
          <VCol :cols="field.col" :md="field.md ?? 6" :sm="field.sm ?? 6">
            <label>
              {{ field.label }} :
              <small v-if="field.required" class="text-error">*</small>
            </label>
            <VTextField
              v-model="fieldValues[field.name]"
              :rules="field.rules"
              :readonly="field.disabled"
              :bg-color="field.disabled ? 'grey-secondary' : ''"
              :type="isConfirmPasswordVisible ? 'text' : 'password'"
              :counter="field.counter ?? null"
              :maxlength="field.counter ?? null"
              :append-inner-icon="
                fieldValues[field.name]
                  ? isConfirmPasswordVisible
                    ? 'mdi-eye-off-outline'
                    : 'mdi-eye-outline'
                  : null
              "
              :persistent-placeholder="true"
              placeholder="ตัวอย่าง : 12345678"
              @click:append-inner="
                fieldValues[field.name] && (isConfirmPasswordVisible = !isConfirmPasswordVisible)
              "
            />
            <small v-if="field.remark" class="text-error">
              {{ field.remark }}
            </small>
            <Tooltip v-if="field.tooltip" :title="field.tooltip" :content="field.tooltipContent" />
          </VCol>
        </template>

        <template v-if="field.type === 'checkmail' && shouldDisplayField(field)">
          <VCol :cols="field.col" :md="field.md ?? 6" :sm="field.sm ?? 6" class="mt-9 align-center">
            <VBtn
              color="error-200"
              rounded="xl"
              :disabled="field.disabled"
              style="color: black !important"
              @click="onSubmitNewEmail"
            >
              {{ field.label }}
            </VBtn>
          </VCol>
        </template>
        <template v-if="field.type === 'changepassword' && shouldDisplayField(field)">
          <VCol :cols="field.col" :md="field.md ?? 6" :sm="field.sm ?? 6" class="mt-9 align-center">
            <VBtn
              color="yellow-50"
              rounded="xl"
              style="color: black !important"
              @click="onSubmitChangePassword"
            >
              {{ field.label }}
            </VBtn>
          </VCol>
        </template>

        <template v-if="field.type === 'submit' && shouldDisplayField(field)">
          <VCol :cols="field.col" :md="field.md ?? 6" :sm="field.sm ?? 6" class="mt-6 align-center">
            <VBtn
              size="x-large"
              block
              color="navy-100"
              rounded="xl"
              style="color: white !important"
              @click="onFormSubmit"
            >
              {{ field.label }}
            </VBtn>
          </VCol>
        </template>
        <!-- Select Fields -->
        <template v-if="field.type === 'select' && shouldDisplayField(field)">
          <VCol :cols="field.col ?? 6" :md="field.md ?? 6" :sm="field.sm ?? 6">
            <label>
              {{ field.label }}:
              <small v-if="field.required" class="text-error">*</small>
            </label>
            <VAutocomplete
              v-model="fieldValues[field.name]"
              :item-title="field.title"
              :item-value="field.value"
              :readonly="field.disabled"
              :bg-color="field.disabled ? 'grey-secondary' : ''"
              :rules="field.rules"
              :persistent-placeholder="true"
              :placeholder="field.placeholder"
              :items="field.items"
              no-data-text="ไม่มีข้อมูล"
              density="default"
              :error-messages="getErrorMessages(field.name)"
              class="input-margin-top"
              @input="onFirstInput(field.name)"
            />
          </VCol>
        </template>
        <template v-if="field.type === 'file' && shouldDisplayField(field)">
          <VCol :cols="field.col ?? 6" :md="field.md ?? 6" :sm="field.sm ?? 6">
            <label>
              {{ field.label }}:
              <small v-if="field.required" class="text-error">*</small>
            </label>
            <VFileInput
              v-model="fieldValues[field.name]"
              :label="field.placeholder"
              :readonly="field.disabled"
              :disabled="field.disabled"
              :bg-color="field.disabled ? 'grey-secondary' : ''"
              :accept="field.accept"
              prepend-icon="mdi-upload"
              :rules="field.rules"
              density="default"
              class="input-margin-top"
            >
              <template #append-inner>
                <VIcon icon="mdi-paperclip" class="icon-pointer" />
              </template>
            </VFileInput>
          </VCol>
        </template>
        <template v-if="field.type === 'label' && shouldDisplayField(field)">
          <VCol :cols="field.col ?? 6" :md="field.md ?? 6" :sm="field.sm ?? 6">
            <label :class="field.className ?? ''">{{ field.label }}:</label>
          </VCol>
        </template>
        <!-- Date Fields -->
        <template v-if="field.type === 'date'">
          <VCol :cols="field.col ?? 6" :md="field.md ?? 6" :sm="field.sm ?? 6">
            <label>
              {{ field.label }}:
              <small v-if="field.required" class="text-error">*</small>
            </label>
            <DateTimePicker
              v-model="fieldValues[field.name]"
              :rules="field.rules"
              :error-messages="getErrorMessages(field.name)"
              class="input-margin-top"
            />
          </VCol>
        </template>
        <template v-if="field.type === 'datetime' && shouldDisplayField(field)">
          <VCol :cols="field.col ?? 6" :md="field.md ?? 6" :sm="field.sm ?? 6">
            <label>
              {{ field.label }}:
              <small v-if="field.required" class="text-error">*</small>
            </label>
            <DateTimePicker
              v-model="fieldValues[field.name]"
              :datetime-picker="true"
              :rules="field.rules"
              :error-messages="getErrorMessages(field.name)"
              class="input-margin-top"
            />
          </VCol>
        </template>
        <!-- Date Range Fields -->
        <template v-if="field.type === 'dateRange' && shouldDisplayField(field)">
          <VCol :cols="field.col ?? 6" :md="field.md ?? 6" :sm="field.sm ?? 6">
            <label>
              {{ field.label }}:
              <small v-if="field.required" class="text-error">*</small>
            </label>
            <div class="input-margin-bottom" />
            <DateTimePicker
              v-model="fieldValues[field.name]"
              range
              :rules="field.rules"
              :error-messages="getErrorMessages(field.name)"
              class="input-margin-top"
            />
          </VCol>
        </template>
        <template v-if="field.type === 'checkbox' && shouldDisplayField(field)">
          <VCol :cols="field.col ?? 6" :md="field.md ?? 6" :sm="field.sm ?? 6">
            <VCheckbox v-model="fieldValues[field.name]" :label="field.label" />
          </VCol>
        </template>
        <!-- Year Picker Fields -->
        <template v-if="field.type === 'dateYear' && shouldDisplayField(field)">
          <VCol :cols="field.col ?? 6" :md="field.md ?? 6" :sm="field.sm ?? 6">
            <label>
              {{ field.label }}:
              <small v-if="field.required" class="text-error">*</small>
            </label>
            <div class="input-margin-bottom" />
            <DateTimePicker
              v-model="fieldValues[field.name]"
              format="YYYY"
              year-picker
              class="px-2 my-2 input-margin-top"
              :rules="field.rules"
              :error-messages="getErrorMessages(field.name)"
            />
          </VCol>
        </template>

        <template v-if="field.type === 'dateYearRange' && shouldDisplayField(field)">
          <VCol :cols="field.col ?? 6" :md="field.md ?? 6" :sm="field.sm ?? 6">
            <label>
              {{ field.label }}:
              <small v-if="field.required" class="text-error">*</small>
            </label>
            <div class="input-margin-bottom" />
            <DateTimePicker
              v-model="fieldValues[field.name]"
              format="YYYY"
              year-picker
              range
              :rules="field.rules"
              :error-messages="getErrorMessages(field.name)"
            />
          </VCol>
        </template>
        <template v-if="field.type === 'emptyCol' && shouldDisplayField(field)">
          <VCol :cols="field.col ?? 6" :md="field.md ?? 6" :sm="field.sm ?? 6">
            <label>
              {{ field.label }}
              <small v-if="field.required" class="text-error">*</small>
            </label>
          </VCol>
        </template>
      </template>
    </VRow>
  </VCardText>
</template>

<style scoped>
.input-margin-top {
  margin-block-start: 5px;
}

.input-margin-bottom {
  margin-block-end: 5px;
}
</style>
