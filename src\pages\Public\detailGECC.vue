<script setup>
const router = useRouter()
import { useAxios } from '@/store/useAxios'
const callAxios = useAxios()

const imgLogo = ref('')
const GetSystemLogos = () => {
  callAxios.RequestGet('/OtherMaster/GetSystemLogos').then(response => {
    if (response.status == 200 && response.data.response[0].pathName) {
      imgLogo.value = localStorage.baseUrlImg + response.data.response[0].pathName
    }
  })
}

onMounted(() => {
  GetSystemLogos()
})

router.afterEach(() => {
  window.scrollTo(0, 0)
})
</script>

<template>
  <div>
    <HeaderPublic :logo="imgLogo" />
    <GECCPublic />
    <FooterPublic :logo="imgLogo" />
  </div>
</template>

<style lang="scss">
@use '@public/assets/css/style.css';
@use '@public/assets/css/skin/skin-2.css';
</style>

<route lang="yaml">
meta:
  layout: blank
  action: read
</route>
