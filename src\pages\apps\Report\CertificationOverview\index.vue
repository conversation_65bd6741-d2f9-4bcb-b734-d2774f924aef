<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { VCol } from 'vuetify/lib/components/index.mjs'
import BtnGoBack from '@/@core/components/button/BtnGoBack.vue'
import { breadcrumbItems } from '@/plugins/page/type'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'

// Importing images
import ChartBar from '@/@core/components/Chart/ChartBar.vue'
import ChartDoughnut from '@/@core/components/Chart/ChartDoughnut.vue'
import GoogleMapExpreries from '@/@core/components/Public/GoogleMapExpreries.vue'
import image2 from '@images/report/Advance.png'
import image3 from '@images/report/Balance.png'
import image1 from '@images/report/Excellent.png'

// Router and Axios instances
const router = useRouter()
const callAxios = useAxios()
const Swal = useSwal()

const rankOrder = ref([])

const SystemDashboardId = ref('c32c63d0-8beb-4479-89d7-f9d262d26024')

// Breadcrumbs data
const breadcrumbItems = [
  { title: 'รายงาน', disabled: false, to: '/apps/report/menu' },
  {
    title: 'Dashboard ภาพรวมการรับรองมาตรฐาน GECC',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const ProvinceName = ref<any[]>([])
const Province = ref(null)

const inProcess = ref(false)
const initialValue = ref(false)

const OrgStructure = ref(null)

// const OrgStructureGroupName = ref<any[]>([]);
const OrgStructureGroupName = ref([{ name: 'ทั้งหมด', orgGroupId: '' }])

const CerName = ref([])
const CertificateName = ref<any[]>([])

const year = ref([])

const statusChart = ref(true)
const statusSwitch = ref(true)

const statusChart2 = ref(true)
const statusSwitch2 = ref(true)

// Helper Function to Map API Data
const mapRankOrder = (data: any) => [
  {
    label: 'Excellent',
    sublabel: 'ระดับเป็นเลิศ',
    value: data.advancedLevel ?? 0,
    image: image1
  },
  {
    label: 'Advance',
    sublabel: 'ระดับก้าวหน้า',
    value: data.progressLevel ?? 0,
    image: image2
  },
  {
    label: 'Balance',
    sublabel: 'ระดับพื้นฐาน',
    value: data.basicLevel ?? 0,
    image: image3
  }
]

const defaultRankOrder = ref(mapRankOrder({ advancedLevel: 0, progressLevel: 0, basicLevel: 0 }))

const fetchReportData = async () => {
  Swal.fetchLoadingApi()
  inProcess.value = true

  const currentYearThai = new Date().getFullYear() + 543

  const [StartYear, EndYear] =
    Array.isArray(year.value) && year.value.length > 0
      ? year.value
      : [currentYearThai, currentYearThai]

  try {
    const params = new URLSearchParams({
      SystemDashboardId: SystemDashboardId.value?.toString() || ''
    })

    if (StartYear) params.append('StartYear', StartYear)
    if (EndYear) params.append('EndYear', EndYear)
    if (Province.value || Province.value === 0) params.append('Province', Province.value)

    if (OrgStructure.value || OrgStructure.value === 0)
      params.append('OrgStructureId', OrgStructure.value.toString())

    if (CerName.value !== null && CerName.value >= 0) {
      const certificateNameMap = ['ระดับพื้นฐาน', 'ระดับก้าวหน้า', 'ระดับเป็นเลิศ']

      const certificateName = certificateNameMap[CerName.value]
      if (certificateName) params.append('CertificateName', certificateName)
    }

    const response = await callAxios.RequestGet(
      `/Keyword/GetDashboardCertificationOverview?${params.toString()}`
    )

    if (response.status === 200) {
      const data = response.data.response

      if (Object.keys(data).length !== 0 && typeof data === 'object') {
        chartII.value = data.chartI
        chartIII.value = data.chartII
        chartbarI.value = data.chartbarI
        chartbarII.value = data.chartbarII
        DataMapGECC.value = data.mapGECC

        // อัปเดต Rank Order โดยอิงจากค่าเริ่มต้น
        rankOrder.value = mapRankOrder(data)
      } else {
        rankOrder.value = defaultRankOrder.value
        chartII.value = {}
        chartIII.value = {}
        chartbarI.value = {}
        chartbarII.value = {}
        DataMapGECC.value = {}
        Swal.AddConditionFailText('ไม่พบข้อมูล')
      }

      if (data.chartbarI.isActive) statusChart.value = true
      else if (data.chartI.isActive) statusChart.value = false

      if (data.chartbarII.isActive) statusChart2.value = true
      else if (data.chartII.isActive) statusChart2.value = false

      rankOrder.value = mapRankOrder(data)
    } else {
      throw new Error(`Unexpected response status: ${response.status}`)
    }
  } catch (error) {
    console.error('Error fetching data:', error)
    Swal.AddFail()
  } finally {
    inProcess.value = false
    initialValue.value = true
    Swal.close()
  }
}

const getDropdownItems = async () => {
  try {
    // Fetch data for GetDropDownSystemOrgStructure
    let response = await callAxios.RequestGet('/TransactionRegister/GetDropDownProvince')
    if (response.status === 200)
      ProvinceName.value = [
        { provinceName: 'ทั้งหมด', provinceCode: '' },
        ...response.data.response
      ]

    response = await callAxios.RequestGet('/TransactionRegister/GetDropDownCertificateName')
    if (response.status === 200) {
      CertificateName.value = [{ id: '', certificateName: 'ทั้งหมด' }, ...response.data.response]
    }
  } catch (error) {
    console.error('Error fetching dropdown items:', error)
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการโหลดข้อมูล')
  }
}

watch(statusSwitch, newVal => {
  statusChart.value = !statusChart.value
  updateTypeFirst(statusChart.value)
})

watch(statusSwitch2, newVal => {
  console.log('statusSwitch2', statusChart2.value)
  statusChart2.value = !statusChart2.value
  updateTypeSecond(statusChart2.value)
})

watch(year, (newVal, oldVal) => {
  console.log('Year updated:', { newVal, oldVal })
  fetchReportData()
})

watch(OrgStructure, (newVal, oldVal) => {
  console.log('OrgStructure updated:', { newVal, oldVal })
  fetchReportData()
})

watch(Province, async (newProvince, oldProvince) => {
  console.log('Province updated:', { newProvince, oldProvince })

  OrgStructure.value = ''
  OrgStructureGroupName.value = [{ name: 'ทั้งหมด', orgGroupId: '' }]

  if (newProvince) {
    try {
      const response = await callAxios.RequestGet(
        `/Keyword/GetDropDownGovermentAndOrgGroupByProvince?flag=false&ProvinceCode=${newProvince}`
      )

      if (response.status === 200) {
        OrgStructureGroupName.value = [
          { name: 'ทั้งหมด', orgGroupId: '' },
          ...response.data.response
        ]
        console.log('Updated OrgStructureGroupName:', OrgStructureGroupName.value)
      }
    } catch (error) {
      Swal.AddConditionFailText('เกิดข้อผิดพลาดในการโหลดข้อมูลศูนย์ราชการ')
    }
  } else {
    console.log('No province selected, OrgStructureGroupName cleared.')
  }

  // เรียกฟังก์ชันเพื่ออัปเดตข้อมูลรายงาน
  fetchReportData()
})

watch(CerName, (newVal, oldVal) => {
  console.log('CertificateName Update: ', { newVal, oldVal })
  fetchReportData()
})

onMounted(() => {
  fetchReportData()
  getDropdownItems()
})

// Chart data
const chartII = ref({
  header: '',
  isActive: true,
  donut: {
    series: [],
    label: []
  },
  title: '',
  summary: ''
})

const chartIII = ref({
  header: '',
  isActive: true,
  donut: {
    series: [],
    label: []
  },
  summary: ''
})

const chartbarI = ref({
  header: '',
  isActive: true,
  bar: {
    series: [],
    label: []
  },
  summary: ''
})

const chartbarII = ref({
  header: '',
  isActive: true,
  bar: {
    series: [],
    label: []
  },
  summary: ''
})

const DataMapGECC = ref([
  {
    OrgStructureName: '',
    lat: '',
    lng: '',
    detail: []
  }
])
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <VCard>
      <VRow class="pa-3">
        <VCol cols="3">
          <DateTimePicker
            v-model="year"
            format="YYYY"
            year-picker
            range
            no-data-text="ไม่มีข้อมูล"
            density="default"
            placeholder="พ.ศ."
            bg-color="primary"
            rounded
            :icon-style-flag="true"
          />
        </VCol>

        <VCol cols="3">
          <VAutocomplete
            v-model="Province"
            :items="ProvinceName"
            item-title="provinceName"
            item-value="provinceCode"
            no-data-text="ไม่มีข้อมูล"
            density="default"
            bg-color="primary"
            :persistent-placeholder="true"
            placeholder="จังหวัด :"
            rounded
          />
        </VCol>

        <VCol cols="3">
          <VAutocomplete
            v-model="OrgStructure"
            :items="OrgStructureGroupName"
            item-title="name"
            item-value="orgGroupId"
            no-data-text="ไม่มีข้อมูล"
            density="default"
            placeholder="สังกัดกระทรวง :"
            bg-color="primary"
            :persistent-placeholder="true"
            rounded
          />
        </VCol>

        <VCol cols="3">
          <VAutocomplete
            v-model="CerName"
            :items="CertificateName"
            item-title="certificateName"
            item-value="id"
            no-data-text="ไม่มีข้อมูล"
            density="default"
            placeholder="ระดับการรับรอง :"
            bg-color="primary"
            :persistent-placeholder="true"
            rounded
          />
        </VCol>
      </VRow>
    </VCard>

    <VRow>
      <!-- Left Side: Summary Cards and Data Table -->
      <VCol cols="12" md="5">
        <VCard class="mt-4">
          <VRow class="mb-4 mx-2 mt-2">
            <VCol v-for="(rank, index) in rankOrder" :key="index" cols="12" md="4" sm="4">
              <VCard class="d-flex flex-column align-center text-center py-6">
                <!-- Image and Labels Row -->
                <VRow class="align-center justify-center">
                  <VCol cols="5" md="12" class="text-center">
                    <img :src="rank.image" alt="Icon" />
                  </VCol>
                  <VCol cols="7" md="12" class="text-center">
                    <div class="text-h6 font-weight-bold">
                      {{ rank.label }}
                    </div>
                    <div class="text-subtitle1">
                      {{ rank.sublabel }}
                    </div>
                  </VCol>
                </VRow>
                <!-- Value Row -->
                <VRow>
                  <VCol cols="12" class="text-end">
                    <div class="text-h3">
                      {{ rank.value }}
                    </div>
                  </VCol>
                </VRow>
              </VCard>
            </VCol>
          </VRow>
          <VCol cols="12" md="12">
            <GoogleMapExpreries height="760px" :data-map-gecc="DataMapGECC" />
          </VCol>
        </VCard>
      </VCol>

      <!-- Right Side: Application status Cards -->
      <VCol cols="12" md="7">
        <VCard class="mb-6 mt-4">
          <template v-if="statusChart">
            <ChartDoughnut :chart-data="chartII" />
          </template>
          <template v-else>
            <ChartBar :chart-data="chartbarI" />
          </template>
        </VCard>
        <VCard class="mb-6 mt-5">
          <template v-if="statusChart2">
            <ChartDoughnut :chart-data="chartIII" seltioncolor="alternative" />
          </template>
          <template v-else>
            <ChartBar :chart-data="chartbarII" seltioncolor="alternative" />
          </template>
        </VCard>
      </VCol>
    </VRow>
    <div class="d-flex align-center justify-end flex-wrap gap-4">
      <BtnGoBack :chart-data="chartbarI" />
    </div>
  </div>
</template>

<style scoped>
::v-deep(.v-input input::placeholder) {
  color: white !important;
}

.card-container {
  position: relative;
  overflow: visible;
}

.stat-col {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.text-h3 {
  margin-block-end: 0;
  margin-block-start: 0;
}

.v-col.v-col-12.text-end {
  padding: initial;
}

.overlapping-image {
  position: relative;
  block-size: 150px;
  inline-size: 150px;
  inset-block-start: -35px;
  margin-block: -20px -50px;
  object-fit: contain;
}

.rank-image {
  block-size: 70px;
  inline-size: 70px;
  margin-inline-end: 10px;
  object-fit: contain;
}

.hover-card {
  transition: all 0.3s ease;
}

.hover-card.hover-active {
  border: 2px solid #42a5f5;
  box-shadow: 0 4px 15px rgba(66, 165, 245, 50%);
  transform: scale(1.02);
}

@media (max-width: 1600px) and (min-width: 1403px) {
  .overlapping-image {
    block-size: auto;
    inline-size: 100%;
    margin-block-start: -20px;
    max-inline-size: 60px;
  }
}

@media (max-width: 1599px) and (min-width: 961px) {
  .overlapping-image {
    block-size: 111px;
    inline-size: 111px;
    margin-block-start: -20px;
    max-inline-size: 60px;
  }
}

@media (max-width: 1060px) and (min-width: 960px) {
  .text-h6 {
    font-size: 17px !important;
  }
  .text-subtitle1 {
    font-size: 14px !important;
  }
}

@media (max-width: 710px) and (min-width: 683px) {
  .text-subtitle1 {
    font-size: 15px !important;
  }
}

@media (max-width: 682px) and (min-width: 600px) {
  .text-h6 {
    font-size: 17px !important;
  }
  .text-subtitle1 {
    font-size: 13px !important;
  }
}
</style>
