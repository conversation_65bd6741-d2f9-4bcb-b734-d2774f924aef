<script setup>
import { useAxios } from '@/store/useAxios'
import * as XLSX from 'xlsx'
import jsPDF from 'jspdf'
import 'jspdf-autotable'
const callAxios = useAxios()
const search = ref('')
const items = ref([])
const listProvince = ref([])
const listCheckStatus = ref([
  'เลือกทั้งหมด',
  'อยู่ในระยะการรับรอง',
  'ครบอายุการรับรอง',
  'เคยสมัครแต่ไม่ผ่าน (รายปี)',
  'เคยสมัครแต่ไม่ผ่าน'
])
const listLevel = ref([
  'เลือกทั้งหมด',
  'ระดับก้าวหน้า',
  'ระดับเป็นเลิศ',
  'ระดับพื้นฐาน',
  'สัญลักษณ์เก่า'
])
const listStatusName = ref([
  'เลือกทั้งหมด',
  'ผ่านการตรวจประเมิน',
  'ผ่านการรักษามาตรฐาน',
  'ไม่ผ่านการตรวจคัดกรองเอกสาร',
  'ไม่ผ่านการตรวจประเมิน'
])
const form = ref({
  dateRange: [new Date().getFullYear(), new Date().getFullYear() + 3],
  provinceId: -1,
  checkStatus: 'เลือกทั้งหมด',
  level: 'เลือกทั้งหมด',
  statusName: 'เลือกทั้งหมด'
})
const columns = ref([
  {
    field: 'index',
    header: 'ลำดับ',
    headerStyle: 'background-color:#fad41a;'
  },
  {
    field: 'fiscalYear',
    header: 'พ.ศ.',
    sortable: true,
    headerStyle: 'background-color:#fad41a;'
  },
  {
    field: 'orgStructureName',
    header: 'ชื่อหน่วยงาน',
    sortable: true,
    headerStyle: 'background-color:#fad41a;'
  },
  {
    field: 'fiscalYear',
    header: 'ปีที่ได้รับการรับรอง',
    sortable: true,
    headerStyle: 'background-color:#fad41a;'
  },
  {
    field: 'geccStandardStatusName',
    header: 'สถานะรับรอง',
    sortable: true,
    headerStyle: 'background-color:#fad41a;'
  },
  {
    field: 'checkStatus',
    header: 'ผลการตรวจ',
    sortable: true,
    headerStyle: 'background-color:#fad41a;'
  },
  {
    field: 'certificateLogo',
    header: 'ระดับ',
    sortable: true,
    headerStyle: 'background-color:#fad41a;'
  }
])
function checkConditionSelectAll(value) {
  if (value == 'เลือกทั้งหมด') {
    return null
  } else {
    return value
  }
}
const GetDashboard = () => {
  callAxios
    .RequestGet(
      `/OtherMaster/GetDashboard?StartYear=${form.value.dateRange[0] + 543}&EndYear=${form.value.dateRange[1] + 543}&ProvinceId=${form.value.provinceId}&StatusName=${checkConditionSelectAll(form.value.statusName)}&CheckStatus=${checkConditionSelectAll(form.value.checkStatus)}&Level=${checkConditionSelectAll(form.value.level)}`
    )
    .then(response => {
      if (response.status == 200) {
        items.value = response.data.response
      }
    })
}
const GetProvinces = () => {
  callAxios.RequestGet(`/OtherMaster/GetProvinces`).then(response => {
    if (response.status == 200) {
      listProvince.value = response.data.response.sort(function (a, b) {
        if (a.provinceName < b.provinceName) {
          return -1
        }
        if (a.provinceName > b.provinceName) {
          return 1
        }
        return 0
      })
      listProvince.value = listProvince.value.filter(x => x.provinceId !== 1)
      listProvince.value.unshift({
        provinceId: -1,
        provinceName: 'เลือกทั้งหมด'
      })
    }
  })
}

const exportToExcel = async () => {
  const sheet = await XLSX.utils.json_to_sheet(items.value)
  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, sheet, 'Table')
  return XLSX.writeFile(workbook, 'ภาพรวมตาราง.xlsx')
}

watch(form.value, () => {
  if (form.value) {
    GetDashboard()
  }
})

onMounted(() => {
  GetProvinces()
  GetDashboard()
})
</script>
<template>
  <div>
    <VCard color="grey-secondary">
      <VCardText>
        <VRow class="mb-2" align="center" justify="center">
          <VCol cols="12" md="2">
            <p class="mb-0">ปี พ.ศ.</p>
            <DateTimePicker
              bg-color="blue-400"
              themeDark
              placeholder="ปี พ.ศ."
              format="YYYY"
              range
              yearPicker
              v-model="form.dateRange"
            />
          </VCol>
          <VCol cols="12" md="2">
            <p class="mb-0">จังหวัด</p>
            <VAutocomplete
              bg-color="error"
              placeholder="จังหวัด"
              :items="listProvince"
              item-title="provinceName"
              item-value="provinceId"
              v-model="form.provinceId"
            />
          </VCol>
          <VCol cols="12" md="2">
            <p class="mb-0">ผลการตรวจ</p>
            <VAutocomplete
              bg-color="blue-400"
              :items="listCheckStatus"
              v-model="form.checkStatus"
            />
          </VCol>
          <VCol cols="12" md="2">
            <p class="mb-0">ระดับ</p>
            <VAutocomplete
              bg-color="blue-400"
              placeholder="ระดับ"
              :items="listLevel"
              v-model="form.level"
            />
          </VCol>
          <VCol cols="12" md="2">
            <p class="mb-0">สถานะรับรอง</p>

            <VAutocomplete
              bg-color="blue-400"
              placeholder="สถานะรับรอง"
              :items="listStatusName"
              v-model="form.statusName"
            />
          </VCol>
        </VRow>
        <VSpacer />
      </VCardText>
      <VCardText>
        <VRow justify="start" align="center">
          <VCol cols="12" md="3">
            <VTextField
              bg-color="white"
              label="ค้นหา"
              placeholder="กรอกข้อมูลที่ต้องการค้นหา"
              v-model="search"
            />
          </VCol>
          <VCol cols="12" md="2">
            <div class="demo-space-x">
              <VBtn color="success" @click="exportToExcel">Export Excel</VBtn>
            </div>
          </VCol>
        </VRow>
        <VSpacer />
        <AppDataTable
          stripedRows
          class="mt-4"
          :headerNo="false"
          :filters="search"
          :columns="columns"
          :value="items"
        >
          <template #index="slotProps">{{ slotProps.index + 1 }}.</template>
          <template #certificateLogo="slotProps">
            <div class="text-center">
              <p>{{ slotProps.data.certificateLogo }}</p>
              <VImg height="50" :src="slotProps.data.valueFiscalYear" />
            </div>
          </template>
        </AppDataTable>
      </VCardText>
    </VCard>
  </div>
</template>
