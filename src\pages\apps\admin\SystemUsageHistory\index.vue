<script setup lang="ts">
import FiltersAPI from '@/components/FiltersAPI.vue'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import type { IGetLogRes } from '@interfaces/UserManagementInterface'

const callAxios = useAxios()
const Swal = useSwal()

const currentPage: Ref<number> = ref(1)
const rowPerPage: Ref<number> = ref(20)
const totalRecords: Ref<number> = ref(0)
const ListItem = ref<IGetLogRes[]>([])

const breadcrumbItems = [
  {
    title: 'ผู้ดูแลระบบ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'ประวัติการใช้งาน',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const formatDateToAPI = (date: Date): string => {
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')

  return `${year}-${month}-${day}`
}

const today = new Date()

const listFilter = reactive([
  {
    name: 'selectedDate',
    type: 'dateRange',
    label: 'วันที่ (เริ่มต้น-สิ้นสุด)',
    default: '',
    title: '',
    value: '',
    items: [],
    placeholder: 'วันที่ (เริ่มต้น-สิ้นสุด)'
  },
  {
    name: 'searchSystem',
    type: 'select',
    label: 'ระบบ',
    title: 'systemName',
    value: 'systemName',
    items: []
  },
  {
    name: 'searchWord',
    type: 'text',
    label: 'คำค้น',
    placeholder: 'ระบุ IP Address/ประวัติ/ผู้ใช้งาน'
  },
  {
    type: 'emptyCol'
  }
])

const filter = reactive({
  selectedDate: '',
  searchWord: '',
  searchSystem: 'ทั้งหมด'
})

const listFields = ref([
  {
    field: 'no',
    header: 'ลำดับที่',
    sortable: false,
    style: { textAlign: 'center' }
  },
  {
    field: 'actionDate',
    header: 'วันที่ / เวลา',
    sortable: true
  },
  {
    field: 'ipAddress',
    header: 'IP Address',
    sortable: true
  },
  {
    field: 'event',
    header: 'ประวัติ',
    sortable: true
  },
  {
    field: 'systemName',
    header: 'ระบบ',
    sortable: true
  },
  {
    field: 'fullName',
    header: 'ผู้ใช้งาน',
    sortable: true
  }
])

const onPageChange = (event: { page: number; rows: number }) => {
  currentPage.value = event.page + 1
  rowPerPage.value = event.rows
  GetList()
}

const adjustYear = dateString => {
  const [year, month, day] = dateString.split('-')

  return `${Number(year) + 543}-${month}-${day}`
}

const getSystemListItems = async () => {
  try {
    const response = await callAxios.RequestGet('/OtherMaster/GeSystemNameDDL')
    if (response.status === 200)
      listFilter[1].items = [{ systemName: 'ทั้งหมด' }, ...response.data.response]
  } catch (error) {
    Swal.callCatch()
  }
}

const GetList = async () => {
  console.log('startDate', filter.selectedDate)
  try {
    const queryParams = new URLSearchParams({
      Page: currentPage.value.toString(),
      PageSize: rowPerPage.value.toString()
    })

    if (filter.selectedDate) {
      const [DateStart, DateEnd] = filter.selectedDate

      queryParams.append('StartDate', adjustYear(DateStart))
      queryParams.append('EndDate', adjustYear(DateEnd))
    }

    if (filter.searchSystem !== 'ทั้งหมด') queryParams.append('SystemName', filter.searchSystem)

    if (filter.searchWord) queryParams.append('Keyword', filter.searchWord)

    const response = await callAxios.RequestGet(`/OtherMaster/GetLogs?${queryParams.toString()}`)

    if (response.status === 200) {
      totalRecords.value = response.data.count
      ListItem.value = response.data.response as IGetLogRes[]
    } else {
      Swal.AddConditionFailText('ไม่สามารถโหลดข้อมูลได้')
    }
  } catch (error) {
    console.error('Error fetching list:', error)
    Swal.callCatch()
  }
}

onMounted(async () => {
  GetList()
  getSystemListItems()
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <!-- {{ filter }} -->

    <FiltersAPI v-model="filter" :fields="listFilter" @submit="GetList" />

    <VCard>
      <AppDataTableAPI
        :header-no="false"
        :total-records="totalRecords"
        :columns="listFields"
        :value="ListItem"
        @page="onPageChange"
      />
    </VCard>
  </div>
</template>
