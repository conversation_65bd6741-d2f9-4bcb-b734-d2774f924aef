import type { AxiosError, AxiosInstance, AxiosResponse } from 'axios'
import { defineStore } from 'pinia'
import { inject, ref } from 'vue'
import { useSwal } from '@/store/useSwal'

export const useAuth = defineStore('useAuth', () => {
  const Swal = useSwal()
  const ListMenu = ref([])
  const axios = inject<AxiosInstance>('axios')!

  const Permission = ref({
    isInsert: false,
    isUpdate: false,
    isDelete: false,
    isAdmin: false,
    isAudit: false,
    isOfficer: false,
    isUser: false
  })

  const LogoBackoffice = ref('')
  const LogoLanding = ref('')
  const newsData = ref([])
  const NavbarItem = ref([])
  const FooterMenuItem = ref([])
  const banner = ref('')

  const menuBanner = ref(
    [] as Array<{
      text: string
      image: string
      url: string
      contentId?: string
    }>
  )

  const selectedTemplateColor = ref('1')
  const StatusDashboard = ref([])
  const FooterData = ref([])
  const themeMode = ref('')
  const themeColor = ref('')

  function getTemplateColor(templateId: string): string {
    const templateColors: { [key: string]: string } = {
      1: 'color-theme-1',
      2: 'color-theme-2',
      3: 'color-theme-3'
    }

    return templateColors[templateId] || templateColors['1']
  }

  function setTemplateColor(color: string) {
    selectedTemplateColor.value = color
    localStorage.setItem('selectedTemplateColor', color)

    const selectedClass = getTemplateColor(color)

    document.documentElement.classList.remove('color-theme-1', 'color-theme-2', 'color-theme-3')

    document.documentElement.classList.add(selectedClass)

    console.log('Template Color Updated and Applied to <html>: ', selectedClass)
  }

  function initializeTheme() {
    const savedColor = localStorage.getItem('selectedTemplateColor') || '1'

    setTemplateColor(savedColor)
  }

  async function fetchMenu(): Promise<AxiosResponse> {
    return new Promise((resolve, reject) => {
      axios
        .get('/UserManagement/GetSystemMenus')
        .then((response: AxiosResponse) => {
          ListMenu.value = response.data.response
          resolve(response)
        })
        .catch((error: AxiosError) => reject(error))
    })
  }

  async function fetchCurrentPersonalInfo(): Promise<AxiosResponse> {
    const userId = localStorage.getItem('userId')

    return axios.get(`/HumanResource/GetPersonalInfoByUserId?UserId=${userId}`)
  }

  async function fetchInformationUser(id: number): Promise<AxiosResponse> {
    return new Promise((resolve, reject) => {
      axios
        .get(`/HumanResource/GetPersonalDataByPersonalId?PersonalId=${id}`)
        .then((response: AxiosResponse) => resolve(response))
        .catch((error: AxiosError) => reject(error))
    })
  }

  async function CheckInLocation(location: {
    latitude: number
    longitude: number
  }): Promise<AxiosResponse> {
    const request = {
      latitude: location.latitude,
      longitude: location.longitude
    }

    return new Promise((resolve, reject) => {
      axios
        .post('/HumanResource/PersonalCheckIn', request)
        .then((response: AxiosResponse) => resolve(response))
        .catch((error: AxiosError) => reject(error))
    })
  }

  async function CheckInStatusLocation(): Promise<AxiosResponse> {
    return new Promise((resolve, reject) => {
      axios
        .get('/HumanResource/PersonaIsCheckIn')
        .then((response: AxiosResponse) => resolve(response))
        .catch((error: AxiosError) => reject(error))
    })
  }

  async function authenticationLogin(request: {
    IdCard: string
    password: string
  }): Promise<AxiosResponse> {
    return new Promise((resolve, reject) => {
      axios
        .post('/Authenticate/login', request)
        .then((response: AxiosResponse) => {
          if (response.status === 200) {
            Swal.ConditionSuccessText('เข้าสู่ระบบสำเร็จ')
            resolve(response)
          }
        })
        .catch((error: AxiosError) => {
          if (error.response?.status === 428) {
            resolve(error.response)
          } else {
            Swal.AddConditionFailText('เข้าสู่ระบบไม่สำเร็จ อีเมลหรือรหัสผ่านไม่ถูกต้อง')
            console.error(error)
            reject(error)
          }
        })
    })
  }

  return {
    Swal,
    ListMenu,
    Permission,
    LogoBackoffice,
    LogoLanding,
    newsData,
    NavbarItem,
    FooterMenuItem,
    banner,
    menuBanner,
    selectedTemplateColor,
    StatusDashboard,
    FooterData,
    themeMode,
    themeColor,
    getTemplateColor,
    setTemplateColor,
    initializeTheme,
    fetchMenu,
    fetchCurrentPersonalInfo,
    fetchInformationUser,
    CheckInLocation,
    CheckInStatusLocation,
    authenticationLogin
  }
})
