<script setup>
import { useRouter } from 'vue-router'
import Filters from '@core/components/Filters.vue'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'

const router = useRouter()
const callAxios = useAxios()
const Swal = useSwal()

const breadcrumbItems = [
  {
    title: 'ผู้ดูแลระบบ',
    disabled: false,
    to: '/pagemain'
  },
  {
    title: 'จัดการงานบริการ',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const filter = ref({
  searchQuery: null
})

const listFilter = reactive([
  {
    name: 'searchQuery',
    type: 'text',
    label: 'ค้นหา'
  }
])

const listFields = ref([
  {
    field: 'recordDate',
    header: 'วันที่บันทึก',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'provinceName',
    header: 'จังหวัด',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'orgStructureName',
    header: 'ชื่อหน่วยงาน',
    sortable: true
  },
  {
    field: 'total',
    header: 'จำนวนงานบริการ',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'options',
    header: 'การจัดการ',
    style: { textAlign: 'center' }
  }
])

const isDialogVisible01 = ref(false)

const onFormReset = () => {
  isDialogVisible01.value = false
  form.value = structuredClone(toRaw(formRaw.value))
}

const GetList = () => {
  callAxios.RequestGet('/SystemMaster/GetSystemServiceList').then(response => {
    console.log(response)
    if (response.status == 200) ListItem.value = response.data.response
  })
}

const ListItem = ref(GetList())

const ApproveDelete = (ValID1, ValID2) => {
  const endpoint = `/SystemMaster/DeleteSystemMainService?ProvinceId=${ValID1}&OrgStructureId=${ValID2}`

  Swal.ApproveDelete(endpoint).then(response => {
    if (response) GetList()
  })
}
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <Filters :fields="listFilter" :model-value="filter" />

    <VCard>
      <VCardText>
        <div class="d-flex align-center flex-wrap gap-4">
          <BtnInsert
            :to="{ name: 'apps-admin-ManageServiceWork-Add' }"
            prepend-icon="mdi-plus"
            color="success-200"
          >
            เพิ่ม
          </BtnInsert>
        </div>
      </VCardText>
      <AppDataTable :filters="filter.searchQuery" :columns="listFields" :value="ListItem">
        <template #options="slotProps">
          <div class="text-center">
            <IconBtn
              :to="{
                name: 'apps-admin-ManageServiceWork-Add',
                query: {
                  provinceId: slotProps.data.provinceId,
                  orgStructureId: slotProps.data.orgStructureId
                }
              }"
            >
              <VIcon icon="mdi-eye-outline" />
              <VTooltip open-delay="500" location="top" activator="parent">ดูข้อมูล</VTooltip>
            </IconBtn>
            <IconDelete
              @click="ApproveDelete(slotProps.data.provinceId, slotProps.data.orgStructureId)"
            />
          </div>
        </template>
      </AppDataTable>
    </VCard>
  </div>
</template>
