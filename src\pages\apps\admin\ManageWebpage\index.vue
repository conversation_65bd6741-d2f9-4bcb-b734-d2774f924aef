<script setup lang="ts">
import { ref } from 'vue'
import Tab2 from '../components/tabmenu/bannerMenuTab.vue'
import Tab1 from '../components/tabmenu/navbarMenuTab.vue'

const selectedTab = ref(0)

const tabs = [
  {
    title: 'เมนู Navbar'
  },
  {
    title: 'เมนูใต้ Banner'
  }
]

const breadcrumbItems = [
  {
    title: 'ผู้ดูแลระบบ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'จัดการเมนู Webpage',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const router = useRouter()

function goBack() {
  router.go(-1)
}
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <VRow>
      <VCol cols="12" md="12" lg="12">
        <VTabs v-model="selectedTab" color="#2A9D8F" class="custom-tabs">
          <VTab v-for="(tab, index) in tabs" :key="index">
            <span>{{ tab.title }}</span>
          </VTab>
        </VTabs>
        <VDivider />
        <VWindow v-model="selectedTab" class="disable-tab-transition">
          <VWindowItem :value="0">
            <Tab1 />
          </VWindowItem>

          <VWindowItem :value="1">
            <Tab2 />
          </VWindowItem>
        </VWindow>
      </VCol>
    </VRow>
  </div>
</template>

<style scoped>
.custom-tabs .v-tab {
  text-transform: none;
}
</style>
