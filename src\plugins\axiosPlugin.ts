import type { AxiosError, AxiosInstance, AxiosResponse } from 'axios'
import Swal from 'sweetalert2'
import type { App } from 'vue'
import axios from '@axios'
import router from '@/router'

export default function install(app: App) {
  const baseURL: string = import.meta.env.VITE_BASE_URL as string

  const Axios: AxiosInstance = axios.create({
    baseURL,
    headers: {
      'X-Custom-Header': 'foobar',
      'Content-Type': 'application/json;charset=UTF-8',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': '*'
    }
  })

  Axios.interceptors.request.use(config => {
    const token = localStorage.getItem('token')

    if (token) {
      config.headers = config.headers || {}
      config.headers.Authorization = token ? `Bearer ${token}` : ''
    }

    return config
  })

  Axios.interceptors.response.use(
    (response: AxiosResponse) => response,
    async (error: AxiosError) => {
      if (error.response) {
        if (error.response.status === 401 && router.currentRoute.value.path !== '/apps/login') {
          let textAlert = 'เซสซันหมด有期 โปรดเข้าสู่ระบบครั้ง'
          if (router.currentRoute.value.query?.by === 'public') textAlert = 'เข้าสู่ระบบเพื่อใช้งาน'

          await Swal.fire({
            icon: 'error',
            title: 'แจ้งเตือน',
            text: textAlert,
            showCancelButton: false,
            confirmButtonText: 'ตกลง',
            allowOutsideClick: false
          }).then(async result => {
            if (result.isConfirmed) {
              localStorage.removeItem('token')
              localStorage.removeItem('userAbilities')
              localStorage.removeItem('username')
              window.location.href = '/'
            }
          })
        }
      }

      return Promise.reject(error)
    }
  )

  app.config.globalProperties.$http = Axios
  app.config.globalProperties.$axios = Axios
  app.provide('axios', Axios)
}
