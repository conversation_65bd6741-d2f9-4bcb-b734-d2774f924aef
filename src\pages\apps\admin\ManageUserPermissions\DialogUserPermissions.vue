<script setup lang="ts">
import { computed, reactive, ref, toRaw, watch } from 'vue'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import type { FormRoles } from '@interfaces/FormInterface'
import type { IGetSystemRolesRes } from '@interfaces/UserManagementInterface'
import {
  booleanValidator,
  enNumberValidator,
  integerpositiveValidator,
  requiredValidator
} from '@validators'

// Props and Emits
const props = defineProps({
  modelValue: Boolean,
  isAdd: Boolean,
  isEdit: Boolean,
  isView: Boolean,
  viewId: String,
  editId: String
})

const emit = defineEmits(['update:modelValue', 'update'])

const callAxios = useAxios()
const Swal = useSwal()

const refVForm: Ref<any> = ref(null)
const viewId = computed(() => props.viewId)
const editId = computed(() => props.editId)

const form = reactive<FormRoles>({
  roleId: '',
  permissionName: '',
  permissionCode: '',
  timeOut: 0,
  isActive: true
})

const createFormData = () => {
  return {
    roleId: form.roleId,
    name: form.permissionName,
    code: form.permissionCode,
    timeOut: form.timeOut,
    IsActive: form.isActive
  }
}

// Default Form Values for Reset
const formDefault = structuredClone(toRaw(form))

const resetForm = () => {
  Object.assign(form, structuredClone(formDefault))
}

const closeDialog = async () => {
  if (props.isView) {
    resetForm()
    isDialogVisible.value = false

    return
  }
  const response = await Swal.ApproveCancel()
  if (response) {
    resetForm()
    isDialogVisible.value = false
  }
}

// ควรเพิ่ม try-catch ให้ครอบคลุมมากขึ้น
const onFormSubmit = async () => {
  try {
    if (!refVForm.value) return
    const { valid } = await refVForm.value.validate()

    if (valid) {
      if (props.isAdd) await AddForm()
      else if (props.isEdit) await EditForm()
      else throw new Error('Invalid state')
    }
  } catch (error) {
    Swal.AddConditionFailText('ไม่สามารถดำเนินการได้: ไม่มีสถานะที่ถูกต้อง')
  }
}

const AddForm = async () => {
  const confirmed = await Swal.ApproveConfirm()
  if (!confirmed) return

  const formData = createFormData()
  try {
    const response = await callAxios.RequestPost('/UserManagement/AddSystemRole', formData)

    if (response.status === 200) {
      resetForm()
      isDialogVisible.value = false
      emit('update')
    } else {
      Swal.AddFail()
    }
  } catch {
    Swal.AddConditionFailText('กรุณากรอกข้อมูลให้ถูกต้อง')
  }
}

const EditForm = async () => {
  const confirmed = await Swal.ApproveConfirm()
  if (!confirmed) return

  const formData = createFormData()
  try {
    const response = await callAxios.RequestPut(
      `/UserManagement/UpdateSystemRole?RoleId=${editId.value}`,
      formData
    )

    if (response.status === 200) {
      resetForm()
      isDialogVisible.value = false
      emit('update')
    } else {
      Swal.EditConditionFail()
    }
  } catch (error) {
    Swal.EditFail()
  }
}

const ViewForm = async () => {
  try {
    resetForm()

    const response = await callAxios.RequestGetById(
      '/UserManagement/GetSystemRoleById',
      `?RoleId=${viewId.value}`
    )

    if (response.status === 200) {
      const RoleData: IGetSystemRolesRes = response.data.response

      form.roleId = RoleData.roleId
      form.permissionName = RoleData.name
      form.permissionCode = RoleData.code
      form.timeOut = RoleData.timeOut
      form.isActive = RoleData.isActive
    }
  } catch (error) {
    Swal.ViewFail()
  }
}

const isDialogVisible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value)
})

watch(
  () => isDialogVisible.value,
  async newVal => {
    if (newVal && props.isView) await ViewForm()
    else if (newVal && props.isEdit) await ViewForm()
  }
)

const setTitle = computed(() => {
  if (props.isView) return 'ดูข้อมูล'
  if (props.isEdit) return 'แก้ไขรายการ'

  return 'เพิ่มจัดการสิทธิการใช้งาน'
})

const listStatus = ref([
  { id: true, name: 'ใช้งาน' },
  { id: false, name: 'ไม่ใช้งาน' }
])
</script>

<template>
  <VDialog v-model="isDialogVisible" persistent class="v-dialog-sm" no-click-animation scrollable>
    <VCard :title="setTitle">
      <DialogCloseBtn variant="text" size="small" @click="closeDialog" />

      <VCardText>
        <VForm ref="refVForm" @submit.prevent="onFormSubmit">
          <VRow class="mb-4 mx-2 mt-3 justify-center">
            <VCol cols="12" md="10">
              <VRow>
                <VCol cols="12">
                  <label>
                    ชื่อสิทธิ:
                    <small class="text-error">*</small>
                  </label>
                  <VTextField
                    v-model="form.permissionName"
                    :rules="[requiredValidator]"
                    placeholder="ผู้ดูแลระบบ"
                    :disabled="props.isView"
                    :counter="100"
                    :maxlength="100"
                    density="comfortable"
                  />
                </VCol>

                <VCol cols="12">
                  <label>
                    รหัสสิทธิ:
                    <small class="text-error">*</small>
                  </label>
                  <VTextField
                    v-model="form.permissionCode"
                    :rules="[enNumberValidator]"
                    placeholder="ผู้ดูแลระบบ"
                    :disabled="props.isEdit || props.isView"
                    :counter="10"
                    :maxlength="10"
                    density="comfortable"
                  />
                </VCol>

                <VCol cols="12">
                  <label>
                    Timeout (นาที):
                    <small class="text-error">*</small>
                  </label>
                  <VTextField
                    v-model="form.timeOut"
                    :rules="[requiredValidator, integerpositiveValidator]"
                    placeholder="ผู้ดูแลระบบ"
                    :disabled="props.isView"
                    :counter="3"
                    :maxlength="3"
                    density="comfortable"
                  />
                </VCol>

                <VCol cols="12">
                  <label>
                    สถานะ:
                    <small class="text-error">*</small>
                  </label>
                  <VAutocomplete
                    v-model="form.isActive"
                    :items="listStatus"
                    item-title="name"
                    item-value="id"
                    :rules="[booleanValidator]"
                    placeholder="ระบุสถานะ"
                    :disabled="props.isView"
                    class="no-select"
                  />
                </VCol>
              </VRow>
            </VCol>
          </VRow>

          <div class="d-flex flex-wrap justify-center mt-5">
            <div class="demo-space-x">
              <template v-if="!props.isView">
                <VBtn
                  color="blue-600"
                  rounded="xl"
                  prepend-icon="mdi-content-save"
                  @click="onFormSubmit"
                >
                  บันทึก
                </VBtn>
                <VBtn
                  color="error-300"
                  rounded="xl"
                  prepend-icon="mdi-close-circle"
                  @click="closeDialog"
                >
                  ยกเลิก
                </VBtn>
              </template>

              <template v-else>
                <VBtn color="grey-800" rounded="xl" prepend-icon="mdi-close" @click="closeDialog">
                  ปิด
                </VBtn>
              </template>
            </div>
          </div>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style scoped>
.no-select {
  user-select: none;
}
</style>
