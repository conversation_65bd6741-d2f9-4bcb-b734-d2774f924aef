<script setup lang="ts">
import { useRouter } from 'vue-router'
import { VBtn, VCard, VCardTitle } from 'vuetify/lib/components/index.mjs'
import { breadcrumbItems } from '@/plugins/page/type'
import type { Filter } from '@/plugins/page/type'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'

// import AddApplicationRounds from '../components/dialog/AddApplicationRounds.vue';

const router = useRouter()
const callAxios = useAxios()
const Swal = useSwal()

const isAdd = ref(false)
const isEdit = ref(false)
const isView = ref(false)

const isDialogVisible = ref(false)

const openViewDialog = (id: string) => {
  isView.value = true
  isEdit.value = false
  isAdd.value = false
  isDialogVisible.value = true
  editId.value = id
}

const openEditDialog = (id: string) => {
  isEdit.value = true
  isView.value = false
  isAdd.value = false
  isDialogVisible.value = true
  editId.value = id
}

const breadcrumbItems = [
  {
    title: 'รับรองมาตรฐาน GECC',
    disabled: false,
    to: '/apps/gecc/menu'
  },
  {
    title: 'บันทึกผลการตรวจประเมิน',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const listFilter: Filter[] = ref([
  {
    name: 'searchYear',
    type: 'dateYearRange',
    label: 'ปี (เริ่มต้น-สิ้นสุด)',
    default: '',
    title: '',
    value: '',
    items: [],
    placeholder: 'ระบุ ปี/ชื่อรอบการเปิดรับสมัคร'
  },
  {
    name: 'searchNumber',
    type: 'select',
    label: 'เลขที่ใบสมัคร',
    default: '',
    title: 'Number',
    value: 'NumberId',
    items: [{ Number: 'ทั้งหมด', NumberId: '' }]
  },
  {
    name: 'searchGECC',
    type: 'select',
    label: 'ศูนย์ราชการสะดวก',
    default: '',
    title: 'GECC',
    value: 'GECCId',
    items: [{ GECC: 'ทั้งหมด', GECCId: '' }]
  },
  {
    name: 'searchGovernment',
    type: 'select',
    label: 'ชื่อส่วนราชการ (เจ้าภาพหลัก)',
    default: '',
    title: 'Government',
    value: 'GovernmentId',
    items: [{ Government: 'ทั้งหมด', GovernmentId: '' }]
  },
  {
    name: 'searchAgency',
    type: 'select',
    label: 'หน่วยงาน',
    default: '',
    title: 'Agency',
    value: 'AgencyId',
    items: [{ Agency: 'ทั้งหมด', AgencyId: '' }]
  },
  {
    name: 'searchSubcommittee',
    type: 'select',
    label: 'คณะอนุกรรมการ',
    default: '',
    title: 'Subcommittee',
    value: 'SubcommitteeId',
    items: [{ Subcommittee: 'ทั้งหมด', SubcommitteeId: '' }]
  },
  {
    name: 'searchProvince',
    type: 'select',
    label: 'จังหวัด',
    default: '',
    title: 'Province',
    value: 'ProvinceId',
    items: [{ Province: 'ทั้งหมด', ProvinceId: '' }]
  },
  {
    name: 'searchDistrict',
    type: 'select',
    label: 'อำเภอ/เขต',
    default: '',
    title: 'District',
    value: 'DistrictId',
    items: [{ District: 'ทั้งหมด', DistrictId: '' }]
  },
  {
    name: 'searchSubdistrict',
    type: 'select',
    label: 'ตำบล/แขวง',
    default: '',
    title: 'Subdistrict',
    value: 'SubdistrictId',
    items: [{ Subdistrict: 'ทั้งหมด', SubdistrictId: '' }]
  },
  {
    name: 'searchStatus',
    type: 'select',
    label: 'สถานะการสมัคร',
    default: '',
    title: 'StatusName',
    value: 'StatusId',
    items: [
      { StatusName: 'ร่าง', StatusId: '0' },
      { StatusName: 'ส่งใบสมัครแล้ว', StatusId: '1' },
      { StatusName: 'อุทธรณ์การส่งใบสมัคร', StatusId: '2' },
      { StatusName: 'ผ่านการตรวจคัดกรองเอกสาร', StatusId: '3' },
      { StatusName: 'ไม่ผ่านการตรวจคัดกรองเอกสาร', StatusId: '4' },
      { StatusName: 'อุทธรณ์การตรวจคัดกรองเอกสาร', StatusId: '5' },
      { StatusName: 'ผ่านการประเมินรับรองมาตรฐาน GECC', StatusId: '6' },
      { StatusName: 'ยกเลิกใบสมัคร', StatusId: '7' },
      { StatusName: 'ยกเลิกการตรวจประเมินพื้นที่', StatusId: '8' }
    ]
  }
])

const filter = reactive({
  searchRoundName: '',
  searchStatus: ''
})

const listFields = ref([
  {
    field: 'applicationNumber',
    header: 'เลขที่ใบสมัคร',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'centerName',
    header: 'ชื่อศูนย์ราชการสะดวก',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'province',
    header: 'จังหวัด',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'district',
    header: 'อำเภอ/เขต',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'subDistrict',
    header: 'ตำบล/แขวง',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'applicationStatus',
    header: 'สถานะการสมัคร',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'documentScreeningStatus',
    header: 'สถานะการตรวจคัดกรองเอกสาร',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'documentScreeningResult',
    header: 'ผลการตรวจคัดกรองเอกสาร',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'fieldAssessmentStatus',
    header: 'สถานะการตรวจประเมินในพื้นที่',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'fieldAssessmentResult',
    header: 'ผลการตรวจประเมินในพื้นที่',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'lastEditedDate',
    header: 'วันที่แก้ไข',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'lastEditedBy',
    header: 'ชื่อผู้แก้ไข',
    sortable: true,
    style: { textAlign: 'center' }
  },
  {
    field: 'options',
    header: 'การจัดการ',
    sortable: false,
    style: { textAlign: 'center' }
  }
])

const ListItem = ref([
  {
    applicationNumber: 'APP-001',
    centerName: 'ศูนย์ราชการสะดวก กรุงเทพฯ',
    province: 'กรุงเทพมหานคร',
    district: 'เขตพญาไท',
    subDistrict: 'แขวงสามเสนใน',
    applicationStatus: 'กำลังดำเนินการ',
    documentScreeningStatus: 'รอการตรวจสอบ',
    documentScreeningResult: '23 | 23 | 04 | 12',
    fieldAssessmentStatus: 'กำลังตรวจประเมิน',
    fieldAssessmentResult: 'ผ่านการตรวจประเมิน',
    lastEditedDate: '2024-10-15',
    lastEditedBy: 'ผู้ใช้ระบบ A'
  },
  {
    applicationNumber: 'APP-002',
    centerName: 'ศูนย์ราชการสะดวก เชียงใหม่',
    province: 'เชียงใหม่',
    district: 'อำเภอเมือง',
    subDistrict: 'ตำบลศรีภูมิ',
    applicationStatus: 'รอการอนุมัติ',
    documentScreeningStatus: 'ตรวจสอบแล้ว',
    documentScreeningResult: '12 | 8 | 38 | 28',
    fieldAssessmentStatus: 'รอการตรวจประเมิน',
    fieldAssessmentResult: 'ยังไม่ตรวจประเมิน',
    lastEditedDate: '2024-10-12'
  },
  {
    applicationNumber: 'APP-003',
    centerName: 'ศูนย์ราชการสะดวก ภูเก็ต',
    province: 'ภูเก็ต',
    district: 'อำเภอเมือง',
    subDistrict: 'ตำบลตลาดใหญ่',
    applicationStatus: 'เสร็จสมบูรณ์',
    documentScreeningStatus: 'ตรวจสอบแล้ว',
    documentScreeningResult: '12 | 8 | 38 | 28',
    fieldAssessmentStatus: 'ตรวจประเมินแล้ว',
    fieldAssessmentResult: 'ผ่านการตรวจประเมิน',
    lastEditedDate: '2024-09-25',
    lastEditedBy: 'ผู้ใช้ระบบ C'
  },
  {
    applicationNumber: 'APP-003',
    centerName: 'ศูนย์ราชการสะดวก ภูเก็ต',
    province: 'ภูเก็ต',
    district: 'อำเภอเมือง',
    subDistrict: 'ตำบลตลาดใหญ่',
    applicationStatus: 'เสร็จสมบูรณ์',
    documentScreeningStatus: 'ตรวจสอบแล้ว',
    documentScreeningResult: '12 | 8 | 38 | 28',
    fieldAssessmentStatus: 'ตรวจประเมินแล้ว',
    fieldAssessmentResult: 'ผ่านการตรวจประเมิน',
    lastEditedDate: '2024-09-25'
  }
])
</script>

<template>
  <VCard>
    <VCardText class="d-flex align-center justify-center">
      <VCardTitle class="text-h5 text-primary">
        📢 ประกาศผลการตรวจประเมินในพื้นที่ ตั้งแต่บัดนี้ จนถึงวันที่ 30 พฤศจิกายน 2567 📢
      </VCardTitle>
    </VCardText>
  </VCard>
  <VBreadcrumbs :items="breadcrumbItems">
    <template #divider>
      <VIcon icon="mdi-chevron-right" />
    </template>
  </VBreadcrumbs>
  <FiltersAPI v-model="filter" :fields="listFilter" />
  <VCard>
    <VCardText>
      <div class="align-center flex-wrap gap-4">
        <VBtn class="mt-2" prepend-icon="mdi-content-save" color="blue-600" rounded="xl">
          ประกาศผลในระบบ
        </VBtn>
        <h4 class="text-blue-800 mt-5">
          หมายเหตุ : ช่องคะแนนแบ่งตามเกณฑ์ประเมิน = Salf | Quality (Like) | Quality (Smile) | Result
        </h4>
      </div>
    </VCardText>
    <AppDataTableAPI :columns="listFields" :value="ListItem">
      <template #documentScreeningResult="slotProps">
        <div class="text-center">
          <div class="text-center">{{ slotProps.data.documentScreeningResult }}.</div>
          <IconBtn>
            <VIcon icon="mdi-eye-outline" />
            <VTooltip open-delay="500" location="top" activator="parent">ดูข้อมูล</VTooltip>
          </IconBtn>
          <!--
            <IconBtn>
            <VIcon icon="mdi-pencil-outline" />
            <VTooltip open-delay="500" location="top" activator="parent">
            แก้ไข
            </VTooltip>
            </IconBtn>
          -->
        </div>
      </template>
      <template #lastEditedBy="slotProps">
        <VBtn
          v-if="!slotProps.data.lastEditedBy"
          rounded="xl"
          color="btn-add"
          @click="isDialogVisible = true"
        >
          บันทึกผล
        </VBtn>
      </template>
      <template #options="slotProps">
        <div class="text-center">
          <IconBtn class="mt-2" prepend-icon="mdi-note-text-outline" rounded="xl">
            <VIcon icon="mdi-note-text-outline" />
            <VTooltip open-delay="500" location="top" activator="parent">ดาวน์โหลด</VTooltip>
          </IconBtn>
          <!--
            <IconBtn @click="openAddDialog" class="mt-2" prepend-icon="mdi-plus-box-multiple" color="btn-add" rounded="xl">
            <VIcon icon="mdi-download" />
            <VTooltip open-delay="500" location="top" activator="parent">
            ดาวน์โหลด
            </VTooltip>
            </IconBtn>
          -->
          <!--
            <IconBtn>
            <VIcon icon="mdi-printer-outline" />
            <VTooltip open-delay="500" location="top" activator="parent">
            พิมพ์แบบฟอร์ม
            </VTooltip>
            </IconBtn>
            <IconBtn>
            <VIcon icon="mdi-pencil-outline" />
            <VTooltip open-delay="500" location="top" activator="parent">
            แก้ไข
            </VTooltip>
            </IconBtn>
            <VIcon icon="mdi-file-document-edit-outline" tooltip="ดูประวัติ" />
            <VTooltip open-delay="500" location="top" activator="parent">
            กรอกข้อมูล
            </VTooltip>
            <IconBtn>
            <VIcon icon="mdi-eye-outline" />
            <VTooltip open-delay="500" location="top" activator="parent">
            ดูข้อมูล
            </VTooltip>
            </IconBtn>
            <IconBtn>
            <VIcon icon="mdi-delete-outline" />
            <VTooltip open-delay="500" location="top" activator="parent">
            ลบข้อมูล
            </VTooltip>
            </IconBtn>
            <IconBtn>
            <VIcon icon="mdi-file-clock-outline" />
            <VTooltip open-delay="500" location="top" activator="parent">
            Transectionของใบสมัคร
            </VTooltip>
            </IconBtn>
          -->
        </div>
      </template>
    </AppDataTableAPI>
  </VCard>
  <DialogSave v-model="isDialogVisible" />
</template>
