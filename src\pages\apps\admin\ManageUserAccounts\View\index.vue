<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import FormFile from '../FormFile.vue'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import {
  decimalNumberValidator,
  integerpositiveValidator,
  passwordValidator,
  requiredValidator
} from '@validators'

// Dependencies
const callAxios = useAxios()
const Swal = useSwal()
const route = useRoute()
const router = useRouter()

// Extract query parameters
const id = ref(route.query.id || '')

const isEdit = ref(id)

const breadcrumbItems = ref([
  {
    title: 'ผู้ดูแลระบบ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'จัดการบัญชีผู้ใช้งาน',
    disabled: false,
    to: '/apps/admin/ManageUserAccounts',
    active: true,
    activeClass: 'text-info'
  },
  {
    title: 'ดูข้อมูล',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
])

// Reactive State
const refVForm = ref(null)

const form = reactive({
  email: '',
  oldPassword: '',
  password: '',
  govermentId: null,
  confirmPassword: '',
  departmentParrent: null,
  departmentParrentText: null,
  departmentChildLists: null,
  departmentChildListsText: null,
  governmentText: null,
  govermentMain: null,
  officeName: null,
  branchName: null,
  transactionServiceType: null,
  location: null,
  province: null,
  district: null,
  subdistrict: null,
  postalCode: null,
  latitude: null,
  longitude: null,
  status: null,
  userRole: null,
  prefix: null,
  firstName: null,
  lastName: null,
  position: null,
  isAudit: null
})

const isDisabledDefault = ref(true)
const isDisplayUserRole = ref(false)
const isDisplayCommitteeRole = ref(false)
const isDisabledChangePassword = ref(false)

// Dynamic Form Fields
const listInput = reactive([
  {
    name: 'email',
    type: 'text',
    label: 'ชื่อผู้ใช้งาน (อีเมลหน่วยงาน)',
    placeholder: 'ตัวอย่าง : <EMAIL>',
    col: 12,
    md: 6,
    required: true,
    rules: [requiredValidator],
    counter: 200,
    disabled: computed(() => isDisabledDefault.value)
  },
  {
    name: 'oldPassword',
    type: 'password',
    label: 'รหัสผ่าน(เดิม)',
    placeholder: 'ตัวอย่าง : 12345678',
    col: 12,
    md: 6,
    required: true,
    rules: [requiredValidator],
    counter: 20,
    disabled: computed(() => !isDisabledChangePassword.value),
    display: computed(() => isDisabledChangePassword.value)
  },
  {
    label: '',
    type: 'emptyCol',
    col: 12,
    md: 6,
    display: computed(() => isDisabledChangePassword.value)
  },
  {
    name: 'password',
    type: 'password',
    label: 'รหัสผ่าน',
    placeholder: 'ตัวอย่าง : 12345678',
    col: 12,
    md: 6,
    required: true,
    rules: [passwordValidator],
    counter: 20,
    disabled: computed(() => !isDisabledChangePassword.value),
    display: computed(() => isDisabledChangePassword.value),
    remark: 'ดูเงื่อนไขการกำหนดรหัสผ่าน',
    tooltip: 'เงื่อนไขในการกำหนดรหัสผ่าน',
    tooltipContent: `1. มีความยาว 8-12 ตัวอักษร<br />
      2. ตัวอักษรตัวแรกเป็นตัวพิมพ์ใหญ่ (A-Z)<br />
      3. ตัวอักษรถัดไปสามารถเป็น a-z อย่างน้อย 1 ตัวอักษร อักษรพิเศษ(#,?,!,@,$,%,^,&,*,-) อย่างน้อย 1 ตัวอักษร และตัวเลข (0-9) อย่างน้อย 1 ตัวอักษร <br />`
  },
  {
    name: 'confirmPassword',
    type: 'password',
    label: 'ยืนยันรหัสผ่าน',
    placeholder: 'ตัวอย่าง : 12345678',
    col: 12,
    md: 6,
    required: true,
    rules: [requiredValidator],
    counter: 20,
    disabled: computed(() => !isDisabledChangePassword.value),
    display: computed(() => isDisabledChangePassword.value)
  },
  {
    name: 'userRole',
    type: 'select',
    label: 'สิทธิการใช้งาน',
    placeholder: 'ตัวอย่าง : คณะกรรมการ',
    col: 12,
    md: 6,
    required: true,
    rules: [requiredValidator],
    counter: 200,
    display: computed(() => isDisplayUserRole.value || isDisplayCommitteeRole.value),
    disabled: computed(() => isDisabledDefault.value),
    title: 'name',
    value: 'id'
  },
  {
    name: 'status',
    type: 'select',
    label: 'สถานะการใช้งาน',
    placeholder: 'ตัวอย่าง : ใช้งาน',
    col: 12,
    md: 6,
    required: true,
    display: computed(() => isDisplayUserRole.value || isDisplayCommitteeRole.value),
    disabled: computed(() => isDisabledDefault.value),
    title: 'status',
    value: 'statusId',
    items: [
      { status: 'ใช้งาน', statusId: 'ใช้งาน' },
      { status: 'ไม่ใช้งาน', statusId: 'ไม่ใช้งาน' }
    ],
    rules: [requiredValidator]
  },
  {
    name: 'prefix',
    type: 'select',
    label: 'คำนำหน้า',
    placeholder: 'ตัวอย่าง : นาย',
    title: 'name',
    value: 'id',
    items: [],
    col: 12,
    md: 2,
    required: true,
    display: computed(() => isDisplayUserRole.value || isDisplayCommitteeRole.value),
    disabled: computed(() => isDisabledDefault.value),
    rules: [requiredValidator]
  },
  {
    name: 'firstName',
    type: 'text',
    label: 'ชื่อ',
    placeholder: 'ตัวอย่าง : สมศักดิ์',
    col: 12,
    md: 4,
    required: true,
    display: computed(() => isDisplayUserRole.value || isDisplayCommitteeRole.value),
    disabled: computed(() => isDisabledDefault.value),
    rules: [requiredValidator],
    counter: 50
  },
  {
    name: 'lastName',
    type: 'text',
    label: 'นามสกุล',
    placeholder: 'ตัวอย่าง : คงผาศักดิ์',
    col: 12,
    md: 6,
    title: 'name',
    value: 'id',
    items: [],
    required: true,
    display: computed(() => isDisplayUserRole.value || isDisplayCommitteeRole.value),
    disabled: computed(() => isDisabledDefault.value),
    rules: [requiredValidator],
    counter: 50
  },
  {
    name: 'position',
    type: 'text',
    label: 'ตำแหน่ง',
    placeholder: 'โปรดกรอกข้อมูล',
    title: 'name',
    value: 'id',
    items: [],
    required: true,
    display: computed(() => isDisplayUserRole.value || isDisplayCommitteeRole.value),
    disabled: computed(() => isDisabledDefault.value),
    rules: [requiredValidator],
    counter: 100
  },
  {
    name: 'departmentParrent',
    type: 'select',
    label: 'กระทรวง/หน่วยงาน',
    placeholder: 'ตัวอย่าง: กระทรวงพาณิชย์',
    title: 'name',
    value: 'id',
    items: [],
    required: true,
    display: computed(() => isDisplayUserRole.value || isDisplayCommitteeRole.value),
    disabled: true,
    rules: [requiredValidator]
  },
  {
    name: 'departmentParrentText',
    type: 'text',
    label: 'กระทรวง/หน่วยงาน อื่น ๆ (ระบุ)',
    placeholder: 'โปรดกรอกข้อมูล',
    title: 'name',
    value: 'id',
    items: [],
    required: true,
    disabled: true,
    display: false,
    rules: [requiredValidator]
  },
  {
    name: 'departmentChildLists',
    type: 'select',
    label: 'กรม/หน่วยงาน',
    placeholder: 'ตัวอย่าง: อื่น ๆ',
    title: 'name',
    value: 'id',
    items: [],
    required: true,
    display: computed(() => isDisplayUserRole.value || isDisplayCommitteeRole.value),
    disabled: true,
    rules: [requiredValidator]
  },
  {
    name: 'departmentChildListsText',
    type: 'text',
    label: 'กรม/หน่วยงาน อื่น ๆ (ระบุ)',
    placeholder: 'โปรดกรอกข้อมูล',
    counter: 100,
    required: true,
    display: false,
    disabled: true,
    rules: [requiredValidator]
  },
  {
    name: 'transactionServiceType',
    type: 'select',
    label: 'ด้านตามลักษณะการให้บริการ',
    placeholder: 'ตัวอย่าง: ด้านคุณภาพ',
    col: 12,
    md: 12,
    required: true,
    title: 'serviceTypeName',
    value: 'transactionServiceTypeId',
    rules: [requiredValidator],
    disabled: true,
    display: computed(() => isDisplayUserRole.value)
  },
  {
    name: 'governmentText',
    type: 'text',
    label: 'ศูนย์ราชการสะดวก',
    placeholder: 'ตัวอย่าง: ศูนย์บริการประชาชน',
    col: 12,
    md: 12,
    required: true,
    rules: [requiredValidator],
    counter: 200,
    display: computed(() => isDisplayUserRole.value),
    disabled: true
  },
  {
    name: 'govermentMain',
    type: 'select',
    label: 'การบริหารราชการ',
    placeholder: 'ตัวอย่าง: ส่วนกลาง',
    title: 'name',
    value: 'id',
    items: [],
    required: true,
    display: computed(() => isDisplayUserRole.value),
    disabled: true,
    rules: [requiredValidator]
  },
  {
    name: 'officeName',
    type: 'text',
    label: 'สำนัก/กอง',
    placeholder: 'ตัวอย่าง: กองกลาง',
    counter: 200,
    required: true,
    display: computed(() => isDisplayUserRole.value),
    disabled: true,
    rules: [requiredValidator],
    remark: 'ถ้าไม่มีให้ใส่เครื่องหมาย -'
  },
  {
    name: 'branchName',
    type: 'text',
    label: 'ชื่อหน่วยงาน/สาขา',
    placeholder: 'ตัวอย่าง: สำนักงานปลัดกระทรวง',
    col: 12,
    md: 12,
    required: true,
    display: computed(() => isDisplayUserRole.value),
    disabled: true,
    rules: [requiredValidator]
  },
  {
    name: 'location',
    type: 'text',
    label: 'สถานที่ตั้ง/ให้บริการ',
    placeholder: 'ตัวอย่าง: 563 ถนนนนทบุรี',
    col: 12,
    md: 12,
    required: true,
    display: computed(() => isDisplayUserRole.value),
    disabled: true,
    rules: [requiredValidator]
  },
  {
    name: 'province',
    type: 'select',
    label: 'จังหวัด',
    placeholder: 'ตัวอย่าง: นนทบุรี',
    title: 'name',
    value: 'id',
    items: [],
    required: true,
    display: computed(() => isDisplayUserRole.value),
    disabled: true,
    rules: [requiredValidator]
  },
  {
    name: 'district',
    type: 'select',
    label: 'เขต/อำเภอ',
    placeholder: 'ตัวอย่าง: เมืองนนทบุรี',
    title: 'name',
    value: 'id',
    items: [],
    required: true,
    display: computed(() => isDisplayUserRole.value),
    disabled: true,
    rules: [requiredValidator]
  },
  {
    name: 'subdistrict',
    type: 'select',
    label: 'แขวง/ตำบล',
    placeholder: 'ตัวอย่าง: บางกระสอ',
    title: 'name',
    value: 'id',
    items: [],
    required: true,
    display: computed(() => isDisplayUserRole.value),
    disabled: true,
    rules: [requiredValidator]
  },
  {
    name: 'postalCode',
    type: 'text',
    label: 'รหัสไปรษณีย์',
    placeholder: 'ตัวอย่าง: 11000',
    counter: 5,
    required: true,
    display: computed(() => isDisplayUserRole.value),
    disabled: true,
    rules: [requiredValidator, integerpositiveValidator]
  },
  {
    name: 'latitude',
    type: 'text',
    label: 'ละติจูด',
    placeholder: 'ตัวอย่าง: 13.8827305',
    counter: 20,
    required: true,
    display: computed(() => isDisplayUserRole.value),
    disabled: true,
    rules: [requiredValidator, decimalNumberValidator]
  },
  {
    name: 'longitude',
    type: 'text',
    label: 'ลองจิจูด',
    placeholder: 'ตัวอย่าง: 100.4842045',
    counter: 20,
    required: true,
    display: computed(() => isDisplayUserRole.value),
    disabled: true,
    rules: [requiredValidator, decimalNumberValidator]
  }
])

const getDropdownItems = async () => {
  try {
    // Fetch data for คำนำหน้า
    let response = await callAxios.RequestGet('/OtherMaster/SystemTitleMasterLists')
    if (response.status === 200) {
      const Prefix = listInput.find(filter => filter.name === 'prefix')
      if (Prefix) Prefix.items = [...response.data.response]
    }

    // Fetch data for สิทธิการใช้งาน
    response = await callAxios.RequestGet('/OtherMaster/GetRolesList')
    if (response.status === 200) {
      const UserRole = listInput.find(filter => filter.name === 'userRole')
      if (UserRole) UserRole.items = [...response.data.response]
    }

    // Fetch data for กระทรวง/หน่วยงาน
    response = await callAxios.RequestGet(
      `/OtherMaster/DepartmentParrentLists?id=${form.departmentParrent}`
    )
    if (response.status === 200) {
      const DepartmentParrent = listInput.find(filter => filter.name === 'departmentParrent')

      if (DepartmentParrent) DepartmentParrent.items = [...response.data.response]
    }

    // Fetch data for การบริหารราชการ
    response = await callAxios.RequestGet('/OtherMaster/GovermentLists')
    if (response.status === 200) {
      const Government = listInput.find(filter => filter.name === 'govermentMain')

      if (Government) {
        Government.items = [...response.data.response]

        const matchingItem = Government.items.find(item => item.id === form.govermentMain)

        if (matchingItem) form.govermentMain = matchingItem.id
      }
    }

    // Fetch data for GetDropDownSystemOrgStructure
    response = await callAxios.RequestGet('/TransactionRegister/GetDropDownSystemOrgStructure')
    if (response.status === 200) {
      const Agency = listInput.find(filter => filter.name === 'Agency')
      if (Agency) {
        Agency.items = [{ name: 'ทั้งหมด', orgStructureId: '' }, ...response.data.response]
      }
    }

    // Fetch data for GetDropDownCertificateName
    response = await callAxios.RequestGet('/TransactionRegister/GetDropDownCertificateName')
    if (response.status === 200) {
      const CertificationLevel = listInput.find(filter => filter.name === 'CertificationLevel')

      if (CertificationLevel) {
        CertificationLevel.items = [
          { certificateName: 'ทั้งหมด', subdistrictName: '' },
          ...response.data.response
        ]
      }
    }

    // Fetch data for ด้านตามลักษณะการให้บริการ
    response = await callAxios.RequestGet('/OtherMaster/ServiceTypeList')
    if (response.status === 200) {
      const TransactionServiceType = listInput.find(
        filter => filter.name === 'transactionServiceType'
      )

      if (TransactionServiceType) TransactionServiceType.items = [...response.data.response]
    }

    // Fetch data for GetDropDownProvince
    response = await callAxios.RequestGet('/OtherMaster/ProvinceLists')
    if (response.status === 200) {
      const Province = listInput.find(filter => filter.name === 'province')
      if (Province) Province.items = [...response.data.response]
    }
  } catch (error) {
    console.error('Error fetching dropdown items:', error)
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการโหลดข้อมูล')
  }
}

// Fetch กรม/หน่วยงาน
const fetchDepartmentChildLists = async (departmentParrentId: number) => {
  if (!departmentParrentId) return

  // filter อื่นๆ ก่อนส่งไปที่ api
  if (form.departmentChildLists == null && departmentParrentId == 1) {
    form.departmentChildLists = ''
    departmentParrentId = ''
  }

  try {
    const response = await callAxios.RequestGetById(
      '/OtherMaster/DepartmentChildLists',
      `?OrgGroupId=${departmentParrentId}&Id=${form.departmentChildLists}`
    )

    if (response.status === 200) {
      const DepartmentChildLists = listInput.find(filter => filter.name === 'departmentChildLists')

      if (DepartmentChildLists) DepartmentChildLists.items = [...response.data.response]

      if (DepartmentChildLists.items.length === 1)
        form.departmentChildLists = DepartmentChildLists.items[0].id // Assuming the items have an `id` property
    }
  } catch (error) {
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการโหลดข้อมูลอำเภอ')
  }
}

// Fetch Districts Based on Province
const fetchDistricts = async (provinceId: number) => {
  if (!provinceId) return

  try {
    const response = await callAxios.RequestGetById(
      '/OtherMaster/DistrictLists',
      `?Province=${provinceId}`
    )

    if (response.status === 200) {
      const District = listInput.find(filter => filter.name === 'district')
      if (District) District.items = [...response.data.response]
    }
  } catch (error) {
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการโหลดข้อมูลอำเภอ')
  }
}

// Fetch Subdistricts Based on District and Province
const fetchSubdistricts = async (districtId: number, provinceId: number) => {
  if (!districtId || !provinceId) return

  try {
    const response = await callAxios.RequestGetById(
      '/OtherMaster/SubDistrictLists',
      `?District=${districtId}&Province=${provinceId}`
    )

    if (response.status === 200) {
      const subDistrictInput = listInput.find(filter => filter.name === 'subdistrict')

      if (subDistrictInput) {
        subDistrictInput.items = [...response.data.response]

        if (!isEdit.value) {
          const FirstSubdistrictWithPostalCode = subDistrictInput.items.find(item => item.postCode)

          if (FirstSubdistrictWithPostalCode)
            form.postalCode = FirstSubdistrictWithPostalCode.postCode
          else form.subdistrict = null

          // form.postalCode = null;
        } else {
          const SubdistrictWithPostalCode = subDistrictInput.items.find(
            item => item.id === form.subdistrict
          )

          if (SubdistrictWithPostalCode) form.postalCode = SubdistrictWithPostalCode.postCode
          else form.subdistrict = null

          // form.postalCode = null;
        }
      }
    }
  } catch (error) {
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการโหลดข้อมูลตำบล')
  }
}

const isCheckProvince = ref()
let isProgrammaticUpdate = false

const GetPostcode = async (postalCode: string) => {
  if (!postalCode || postalCode.length !== 5) return

  try {
    const response = await callAxios.RequestGet(`/OtherMaster/PostcodeLists?PostCode=${postalCode}`)

    if (response.status === 200 && response.data.response) {
      const { provinceCode, districtCode } = response.data.response

      isProgrammaticUpdate = true // ตั้งค่าสถานะก่อนอัปเดต

      // Update province and district in the form
      const Province = listInput.find(field => field.name === 'province')
      const District = listInput.find(field => field.name === 'district')

      if (Province && District) {
        isCheckProvince.value = provinceCode
        form.province = provinceCode
        form.district = districtCode

        // Trigger dropdown updates if needed
        fetchDistricts(provinceCode)
        fetchSubdistricts(districtCode, provinceCode)
      }
    } else {
      Swal.AddConditionFailText('ไม่พบข้อมูลรหัสไปรษณีย์')
    }
  } catch (error) {
    console.error('Error fetching postcode data:', error)
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการโหลดข้อมูลรหัสไปรษณีย์')
  }
}

const isCheckDepartmentParrent = ref()
const isCheckSubdistrict = ref()

let isInitializing = true

const fetchFormData = async () => {
  if (id.value) {
    try {
      const response = await callAxios.RequestGetById(
        '/UserManagement/GetSystemPersonalInfoById',
        `?Id=${id.value}`
      )

      if (response.status === 200 && response.data.response) {
        const data = response.data.response

        // Map response data to form fields
        form.email = data.email || null
        form.prefix = data.title || null
        form.firstName = data.firstname || null
        form.lastName = data.lastname || null
        form.position = data.positionName || null
        form.governmentText = data.govermentName || null
        if (data.orgGroupId) {
          form.departmentParrent = data.orgGroupId || null
          isCheckDepartmentParrent.value = form.departmentParrent
        }
        form.govermentId = data.govermentId || null
        if (data.systemGovermentId) form.govermentMain = data.systemGovermentId || null

        form.departmentChildLists = data.orgStructureId || null
        form.officeName = data.office || null
        form.branchName = data.branch || null
        form.location = data.address || null
        if (data.provinceCode) {
          form.province = null // รีเซ็ต province ชั่วคราว
          form.province = data.provinceCode // ตั้งค่า province จริง
        }

        form.district = data.districtCode || null
        if (data.provinceCode && data.districtCode) {
          await fetchDistricts(data.provinceCode)
          await fetchSubdistricts(data.districtCode, data.provinceCode)
        }
        if (data.subDistrictCode) {
          form.subdistrict = data.subDistrictCode
          isCheckSubdistrict.value = data.subDistrictCode
        }
        form.latitude = data.latitude || null
        form.longitude = data.longitude || null
        form.postalCode = data.postCode || null

        form.status = data.isActive === true ? 'ใช้งาน' : 'ไม่ใช้งาน'
        form.userRole = data.role || null
        form.isAudit = data.isAudit
        form.transactionServiceType = data.transactionServiceTypeId || null
      }
    } catch (error) {
      Swal.AddConditionFailText('ไม่สามารถโหลดข้อมูลได้')
    }
    if (form.isAudit) {
      isDisplayCommitteeRole.value = true
      isDisplayUserRole.value = false
    } else {
      isDisplayUserRole.value = true
      isDisplayCommitteeRole.value = false
    }
    isInitializing = false
  }
}

// computed properties สำหรับแปลงค่าจาก string เป็น number
const latitudeAsNumber = computed(() => {
  return form.latitude ? parseFloat(form.latitude) : 0
})

const longitudeAsNumber = computed(() => {
  return form.longitude ? parseFloat(form.longitude) : 0
})

// Form Submission
const createFormData = () => {
  if (form.governmentText == null) form.governmentText = ''
  if (form.departmentParrentText == null) form.departmentParrentText = ''
  if (form.departmentChildListsText == null) form.departmentChildListsText = ''
  if (form.password == '') form.password = '******'

  return {
    email: form.email,
    password: form.password,
    title: form.prefix,
    firstName: form.firstName,
    lastName: form.lastName,
    positionName: form.position,
    govermentId: form.govermentId,
    role: form.userRole || '',
    govermentName: form.governmentText,
    orgGroupId: form.departmentParrent,
    orgGroupName: form.departmentParrentText,
    orgStructureId: form.departmentChildLists,
    orgStructureName: form.departmentChildListsText,
    systemGovermentId: form.govermentMain,
    office: form.officeName,
    branch: form.branchName,
    transactionServiceTypeId: form.transactionServiceType,
    address: form.location,
    provinceCode: form.province,
    districtCode: form.district,
    postCode: form.postalCode,
    subDistrictCode: form.subdistrict,
    latitude: form.latitude,
    longitude: form.longitude,
    isActive: form.status === 'ใช้งาน', // Convert "ใช้งาน" to true
    isAudit: form.isAudit
  }
}

const onFormSubmit = async () => {
  try {
    const confirmed = await Swal.ApproveConfirm()
    if (!confirmed) return

    if (id.value) {
      // If password needs to be changed
      if (form.oldPassword && form.password && form.confirmPassword) {
        if (form.password !== form.confirmPassword) {
          Swal.AddConditionFailText('รหัสผ่านและยืนยันรหัสผ่านไม่ตรงกัน')

          return
        }

        const passwordChangeData = {
          passwordOld: form.oldPassword,
          password: form.password,
          id: id.value
        }

        // Call the API to change the password
        const passwordResponse = await callAxios.RequestPut(
          '/Authenticate/ChangePasswordPersonalInfo',
          passwordChangeData
        )

        if (passwordResponse.status !== 200) {
          Swal.AddConditionFailText('เกิดข้อผิดพลาดในการเปลี่ยนรหัสผ่าน')

          return
        }
      }
      const formData = createFormData()

      const nameResponse = await callAxios.RequestPut(
        `/Authenticate/UpdateSystemPersonalInfo?Id=${id.value}`,
        formData
      )

      if (nameResponse.status !== 200) {
        Swal.AddConditionFailText('เกิดข้อผิดพลาดในการเปลี่ยนชื่อ')

        return
      } else {
        Swal.AddSuccess()
        router.push({ name: 'apps-admin-ManageUserAccounts' })
      }
    } else {
      // Prepare the main form data
      const formData = createFormData()

      const apiEndpoint = isEdit.value
        ? `/Authenticate/UpdateSystemPersonalInfo?Id=${id.value}`
        : '/Authenticate/AddSystemPersonalInfo'

      // Submit the main form data
      const response = await callAxios.RequestPut(apiEndpoint, formData)

      if (response.status === 200) {
        Swal.AddSuccess()
        router.push({ name: 'apps-admin-ManageUserAccounts' })
      } else {
        Swal.AddConditionFailText('เกิดข้อผิดพลาดในการบันทึกข้อมูล')
      }
    }
  } catch (error) {
    console.error('Error during form submission:', error)
    Swal.AddConditionFailText('เกิดข้อผิดพลาดในการบันทึกข้อมูล')
  }
}

const formDefault = structuredClone(toRaw(form))

const resetForm = () => {
  Object.assign(form, structuredClone(formDefault))
}

const onFormReset = async () => {
  const response = await Swal.ApproveCancel()
  if (!response) return
  if (!isEdit.value) resetForm()
  router.push({ name: 'apps-admin-ManageUserAccounts' })
}

// Watchers สิทธิการใช้งาน
watch(
  () => form.userRole,
  async newUserRole => {
    const UserRole = listInput.find(filter => filter.name === 'userRole')
    const RoleName = UserRole.items.find(item => item.id === newUserRole)

    if (RoleName.name.includes('กรรมการ')) {
      form.isAudit = true
      isDisplayCommitteeRole.value = true
      isDisplayUserRole.value = false
    } else {
      form.isAudit = false
      isDisplayUserRole.value = true
      isDisplayCommitteeRole.value = false
    }
  }
)

// Watchers to fetch data กระทรวง/หน่วยงาน
watch(
  () => form.departmentParrent,
  async newDepartmentParrent => {
    if (newDepartmentParrent && isCheckDepartmentParrent.value !== newDepartmentParrent) {
      form.departmentChildLists = null
      fetchDepartmentChildLists(newDepartmentParrent)
    } else {
      fetchDepartmentChildLists(newDepartmentParrent)
    }

    const DepartmentParrentText = listInput.find(filter => filter.name === 'departmentParrentText')

    if (DepartmentParrentText) {
      if (newDepartmentParrent === '1') DepartmentParrentText.display = true
      else DepartmentParrentText.display = false
    }
  }
)

// Watchers to fetch data กรม/หน่วยงาน
watch(
  () => form.departmentChildLists,
  async newDepartmentChildLists => {
    const DepartmentChildListsText = listInput.find(
      filter => filter.name === 'departmentChildListsText'
    )

    if (DepartmentChildListsText) {
      if (newDepartmentChildLists === '1') DepartmentChildListsText.display = true
      else DepartmentChildListsText.display = false
    }
  }
)

const setupEditFormWatchers = () => {
  // Watch จังหวัด
  watch(
    () => form.province,
    async newProvince => {
      if (newProvince && form.postalCode && isCheckProvince.value === newProvince) {
        form.subdistrict = null
      } else if (newProvince) {
        form.district = null
        form.subdistrict = null
        form.postalCode = null
        await fetchDistricts(newProvince)
      } else if (newProvince && form.postalCode && form.district) {
        form.district = null
        form.subdistrict = null
        await fetchDistricts(newProvince)
      } else {
        form.district = null
        form.subdistrict = null
        if (newProvince) await fetchDistricts(newProvince)
      }
    }
  )

  // Watch อำเภอ
  watch(
    () => form.district,
    newDistrict => {
      form.subdistrict = null
      if (newDistrict && form.province) fetchSubdistricts(newDistrict, form.province)
    }
  )

  watch(
    () => form.subdistrict,
    async newSubDistrict => {
      if (newSubDistrict) {
        const subDistrictInput = listInput.find(filter => filter.name === 'subdistrict')

        const selectedSubDistrict = subDistrictInput.items.find(item => item.id === newSubDistrict)

        if (selectedSubDistrict) form.postalCode = selectedSubDistrict.postCode // อัปเดตค่า postCode
      }
    }
  )

  // Watch รหัสไปรษณีย์
  watch(
    () => form.postalCode,
    newPostalCode => {
      if (newPostalCode && newPostalCode.length === 5) GetPostcode(newPostalCode)
    }
  )
}

const setupAddFormWatchers = () => {
  // Watch จังหวัด
  watch(
    () => form.province,
    async newProvince => {
      if (newProvince && form.postalCode && isCheckProvince.value === newProvince) {
        form.subdistrict = null
      } else if (newProvince) {
        form.district = null
        form.subdistrict = null
        form.postalCode = null
        await fetchDistricts(newProvince)
      } else if (newProvince && form.postalCode && form.district) {
        form.district = null
        form.subdistrict = null
        await fetchDistricts(newProvince)
      } else {
        form.district = null
        form.subdistrict = null
        if (newProvince) await fetchDistricts(newProvince)
      }
    }
  )

  // Watch อำเภอ
  watch(
    () => form.district,
    newDistrict => {
      form.subdistrict = null
      if (newDistrict && form.province) fetchSubdistricts(newDistrict, form.province)
    }
  )

  // Watch รหัสไปรษณีย์
  watch(
    () => form.postalCode,
    newPostalCode => {
      if (newPostalCode && newPostalCode.length === 5) GetPostcode(newPostalCode)
    }
  )
}

onMounted(async () => {
  if (isEdit.value) {
    await fetchFormData() // โหลดข้อมูลสำหรับ edit form
    setupEditFormWatchers()
  } else {
    setupAddFormWatchers()
  }
  getDropdownItems() // โหลด dropdown ทั่วไป
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <VCard class="px-4 py-3">
      <VForm ref="refVForm" @submit.prevent="onFormSubmit">
        <FormFile
          v-model="form"
          :fields="listInput"
          @submitChangePassword="isDisabledChangePassword = true"
        />
        <VCol v-if="isDisplayUserRole" cols="12" class="py-0">
          <!--
            <GoogleMapShow
            height="300px"
            :latitude="latitudeAsNumber"
            :longitude="longitudeAsNumber"
            />
          -->
          <LeafletMapShow
            height="300px"
            :latitude="latitudeAsNumber"
            :longitude="longitudeAsNumber"
          />
        </VCol>
        <div class="d-flex justify-end mt-5">
          <div class="demo-space-x">
            <BtnGoBack />
          </div>
        </div>
      </VForm>
    </VCard>
  </div>
</template>
