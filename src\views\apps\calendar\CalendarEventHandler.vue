<script setup lang="ts">
import type { Event } from '@/@core/types'

const props = defineProps<{
  isDialogOpen: boolean
  event: Event
}>()

const emit = defineEmits<{
  (e: 'update:isDialogOpen', val: boolean): void
  (e: 'update:event', val: Event | null): void
}>()

const closeDialog = () => {
  emit('update:isDialogOpen', false)
}

const formatDate = (date: string | Date | undefined, allDay = false) => {
  if (!date) return 'ไม่ระบุ'

  return new Date(date).toLocaleString('th-TH', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    ...(allDay ? {} : { hour: '2-digit', minute: '2-digit' })
  })
}
</script>

<template>
  <VDialog v-model="props.isDialogOpen" max-width="600" no-click-animation scrollable>
    <VCard :title="props.event.title">
      <VCardActions class="justify-end pb-0">
        <DialogCloseBtn variant="text" size="small" @click="closeDialog" />
      </VCardActions>

      <VCardText v-show="props.event" class="dialog-content pt-0">
        <VList>
          <!-- Start Date & End Date in One Row -->
          <VListItem v-if="props.event && props.event.start">
            <VRow class="align-center">
              <VCol cols="6">
                <VListItemTitle class="title-height">📅 วันที่เริ่ม</VListItemTitle>
                <VListItemSubtitle class="subtitle-height">
                  {{ formatDate(props.event.start, props.event.allDay)
                  }}{{ props.event.allDay ? ' (ทั้งวัน)' : '' }}
                </VListItemSubtitle>
              </VCol>

              <VCol v-if="props.event.end && !props.event.allDay" cols="6">
                <VListItemTitle class="title-height">⏳ วันที่สิ้นสุด</VListItemTitle>
                <VListItemSubtitle class="subtitle-height">
                  {{ formatDate(props.event.end, props.event.allDay) }}
                </VListItemSubtitle>
              </VCol>
            </VRow>
          </VListItem>

          <VListItem v-if="props.event.extendedProps?.location">
            <VListItemTitle class="title-height">📍 สถานที่</VListItemTitle>
            <VListItemSubtitle class="subtitle-height">
              {{ props.event.extendedProps.location }}
            </VListItemSubtitle>
          </VListItem>

          <VListItem v-if="props.event.extendedProps?.description">
            <VListItemTitle class="title-height">📝 รายละเอียด</VListItemTitle>
            <VListItemSubtitle class="subtitle-height">
              {{ props.event.extendedProps.description }}
            </VListItemSubtitle>
          </VListItem>

          <VListItem v-if="props.event.url">
            <VListItemTitle class="title-height">🔗 URL</VListItemTitle>
            <VListItemSubtitle class="subtitle-height">
              <a :href="props.event.url" target="_blank">{{ props.event.url }}</a>
            </VListItemSubtitle>
          </VListItem>

          <VListItem v-if="props.event.extendedProps?.attendees?.length">
            <VListItemTitle class="title-height">👥 ผู้เข้าร่วม</VListItemTitle>
            <VListItemSubtitle class="subtitle-height">
              <ul>
                <li v-for="attendee in props.event.extendedProps.attendees" :key="attendee">
                  {{ attendee }}
                </li>
              </ul>
            </VListItemSubtitle>
          </VListItem>
        </VList>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style>
.subtitle-height {
  line-height: 1.5rem !important;
}

.title-height {
  line-height: 1.8rem !important;
}
</style>
