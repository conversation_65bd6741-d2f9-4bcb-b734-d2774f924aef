<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import Filters from '@core/components/Filters.vue'

const router = useRouter()
const callAxios = useAxios()
const Swal = useSwal()

const isEdit: Ref<boolean> = ref(false)
const refVForm: Ref<any> = ref(null)

const editId: Ref<number> = ref(0)

interface FormType {
  recordDate: string
  complainChannelName: string
  isActive: string
  complainChannelId: number
}

const breadcrumbItems = [
  {
    title: 'ขอสิทธิ์การใช้งานระบบ',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const filter = ref({
  searchQuery: null
})

const listFilter = reactive([
  {
    name: 'searchQuery',
    type: 'text',
    label: 'ค้นหา'
  }
])

const listFields = ref([
  {
    field: 'name',
    header: 'ชื่อ-นามสกุล',
    sortable: true
  },

  {
    field: 'orgStructureName',
    header: 'หน่วยงาน',
    sortable: true
  },
  {
    field: 'requestAuthorizeType',
    header: 'ประเภทการขอรับสิทธิ์',
    sortable: true
  },

  {
    field: 'requestAuthorizeStatus',
    header: 'สถานะ',
    sortable: true
  },
  {
    field: 'updateDate',
    header: 'วันที่แก้ไข',
    sortable: true
  },

  {
    field: 'updateBy',
    header: 'ชื่อผู้แก้ไข',
    sortable: true
  },

  {
    field: 'options',
    header: 'การจัดการ'
  }
])

const ListItem = ref([])
const ListItemStore = ref([])

const GetList = () => {
  callAxios.RequestGet('/SystemMaster/GetRequestAuthorize').then(response => {
    if (response.status == 200) {
      ListItem.value = response.data.response
      ListItemStore.value = response.data.response
    }
  })
}

onMounted(async () => {
  GetList()
})

const ApproveDelete = id => {
  const endpoint = `/SystemMaster/DeleteRequestAuthorize?RequestAuthorizeId=${id}`

  // console.log(listFields.value);

  // ListItem.value.splice(id, 1);

  Swal.ApproveDelete(endpoint).then(response => {
    if (response) GetList()
  })
}

function onSearch(values) {
  alert(JSON.stringify(values, null, 2))
}
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <Filters :fields="listFilter" :model-value="filter" />

    <VCard>
      <VCardText>
        <div class="d-flex align-center flex-wrap gap-4">
          <BtnInsert :to="{ name: 'apps-RequestPermission-Add' }" color="success-200">
            ขอสิทธิ์การใช้งาน
          </BtnInsert>
        </div>
      </VCardText>

      <AppDataTable :filters="filter.searchQuery" :columns="listFields" :value="ListItem">
        <template #options="slotProps">
          <div v-if="slotProps.data.isAdmin === true" class="text-center">
            <IconEdit
              v-if="slotProps.data.requestAuthorizeStatusId === 2"
              :to="{
                name: 'apps-RequestPermission-Edit-id',
                params: {
                  id: slotProps.data.requestAuthorizeId
                }
              }"
            />

            <IconBtn
              v-if="slotProps.data.requestAuthorizeStatusId !== 2"
              :to="{
                name: 'apps-RequestPermission-View-id',
                params: {
                  id: slotProps.data.requestAuthorizeId
                }
              }"
            >
              <VIcon icon="mdi-eye-outline" />
              <VTooltip open-delay="500" location="top" activator="parent">ดูข้อมูล</VTooltip>
            </IconBtn>
          </div>

          <div v-if="slotProps.data.isAdmin === false" class="text-center">
            <IconEdit
              v-if="slotProps.data.requestAuthorizeStatusId === 1"
              :to="{
                name: 'apps-RequestPermission-Edit-id',
                params: {
                  id: slotProps.data.requestAuthorizeId
                }
              }"
            />

            <IconDelete
              v-if="slotProps.data.requestAuthorizeStatusId === 1"
              @click="ApproveDelete(slotProps.data.requestAuthorizeId)"
            />

            <IconBtn
              v-if="slotProps.data.requestAuthorizeStatusId !== 1"
              :to="{
                name: 'apps-RequestPermission-View-id',
                params: {
                  id: slotProps.data.requestAuthorizeId
                }
              }"
            >
              <VIcon icon="mdi-eye-outline" />
              <VTooltip open-delay="500" location="top" activator="parent">ดูข้อมูล</VTooltip>
            </IconBtn>
          </div>
        </template>
      </AppDataTable>
    </VCard>
  </div>
</template>
