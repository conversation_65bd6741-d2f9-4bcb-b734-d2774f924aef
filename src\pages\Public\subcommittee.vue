<script setup>
const router = useRouter()
import Subcommittee from '@/@core/components/Public/Subcommittee.vue'
import { useAxios } from '@/store/useAxios'
const callAxios = useAxios()

router.afterEach(() => {
  window.scrollTo(0, 0)
})
</script>

<template>
  <div>
    <HeaderPublic :logo="imgLogo" />

    <Subcommittee />

    <FooterPublic :logo="imgLogo" />
  </div>
</template>

<style lang="scss">
@use '@public/assets/css/style.css';
@use '@public/assets/css/skin/skin-2.css';
</style>

<route lang="yaml">
meta:
  layout: blank
  action: read
</route>
