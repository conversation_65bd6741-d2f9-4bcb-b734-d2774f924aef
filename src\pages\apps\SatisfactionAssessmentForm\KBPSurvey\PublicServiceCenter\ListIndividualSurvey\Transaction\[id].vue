<script setup lang="ts">
interface breadcrumbItems {
  title: string
  disabled: boolean
  to: string
  active: boolean
  activeClass: string
}

const breadcrumbItems: Ref<breadcrumbItems[]> = ref<breadcrumbItems[]>([
  {
    title: 'แบบประเมินความพึงพอใจ',
    disabled: false,
    to: '/apps/home',
    active: false,
    activeClass: ''
  },
  {
    title: 'KBP Survey',
    disabled: false,
    to: '',
    active: false,
    activeClass: ''
  },
  {
    title: 'แบบประเมินความพึงพอใจการให้บริการ',
    disabled: false,
    to: '',
    active: false,
    activeClass: ''
  },
  {
    title: 'รายการแบบประเมินความพึงพอใจการให้บริการศูนย์บริการ (รายบุคคล)',
    disabled: false,
    to: '',
    active: true,
    activeClass: 'text-info'
  }
])

interface columns {
  field: string
  header: string
  sortable: boolean
}

const columns: Ref<columns[]> = ref<columns[]>([
  {
    field: 'createdByUser',
    header: 'IP Address',
    sortable: true
  },
  {
    field: 'fullName',
    header: 'ชื่อ-นามสกุล',
    sortable: true
  },
  {
    field: 'orgStructureName',
    header: 'หน่วยงาน',
    sortable: true
  },
  {
    field: 'serviceName',
    header: 'กลุ่มงาน',
    sortable: true
  },
  {
    field: 'createdDateTime',
    header: 'วันที่สร้าง',
    sortable: true
  },
  {
    field: 'options',
    header: 'การจัดการ',
    sortable: false
  }
])

interface setAttribute {
  columns: columns[]
  toAnswer: string
}

const setAttribute: Ref<setAttribute> = ref<setAttribute>({
  columns: columns.value,
  toAnswer:
    'apps-SatisfactionAssessmentForm-KBPSurvey-PublicServiceCenter-ListIndividualSurvey-Answer-id'
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <BtnGoBack />
    <ListsTableTransaction v-bind="setAttribute" />
  </div>
</template>
