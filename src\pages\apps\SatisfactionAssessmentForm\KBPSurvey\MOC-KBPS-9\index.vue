<script setup>
import { useAxios } from '@/store/useAxios'

const callAxios = useAxios()
const items = ref([])

const breadcrumbItems = [
  {
    title: 'แบบประเมินความพึงพอใจ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'KBP Survey',
    disabled: false,
    to: ''
  },
  {
    title: 'แบบประเมินความพึงพอใจของผู้มีส่วนได้ส่วนเสีย',
    disabled: false,
    to: ''
  },
  {
    title: 'รายการแบบประเมินความพึงพอใจของผู้มีส่วนได้ส่วนเสีย',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const isDialogDisable = ref(false)

const submitDialogFormEvaluation = event => {
  callAxios.RequestPost('/EvaluationForm/AddKBPSurveyStakeholder', event).then(response => {
    if (response.status == 200) {
      isDialogDisable.value = true
      GetKBPSurveyStakeholder()
    }
  })
}

const GetKBPSurveyStakeholder = () => {
  callAxios.RequestGet('/EvaluationForm/GetKBPSurveyStakeholder').then(response => {
    if (response.status == 200) setAttribute.value.items = response.data.response
  })
}

const setAttribute = ref({
  urlFilter: 'GetKBPSurveyStakeholder',
  toQR: 'apps-SatisfactionAssessmentForm-KBPSurvey-MOC-KBPS-9-QRcode-id',
  toPrint: 'apps-SatisfactionAssessmentForm-KBPSurvey-MOC-KBPS-9-Example-id',
  toTransaction: 'apps-SatisfactionAssessmentForm-KBPSurvey-MOC-KBPS-9-Transaction-id',
  to: 'apps-SatisfactionAssessmentForm-KBPSurvey-MOC-KBPS-9-id',
  items: []
})

onMounted(() => {
  GetKBPSurveyStakeholder()
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>

    <BtnGoBack />
    <ListsTable
      v-bind="setAttribute"
      v-model:isDialogDisable="isDialogDisable"
      @submit="event => submitDialogFormEvaluation(event)"
      @update:modelValue="GetKBPSurveyStakeholder"
    />
  </div>
</template>
