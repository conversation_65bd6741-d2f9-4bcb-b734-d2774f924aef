<script lang="ts" setup>
import type { Component } from 'vue'
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'
import { VNodeRenderer } from './VNodeRenderer'
import { useAppAbility } from '@/plugins/casl/useAppAbility'
import { useAuth } from '@/store/useAuth'
import { useAxios } from '@/store/useAxios'
import { useLayouts } from '@layouts'

import { VerticalNavGroup, VerticalNavLink, VerticalNavSectionTitle } from '@layouts/components'
import { config } from '@layouts/config'
import { injectionKeyIsVerticalNavHovered } from '@layouts/symbols'
import type { NavGroup, NavLink, NavSectionTitle, VerticalNavItems } from '@layouts/types'

const props = withDefaults(defineProps<Props>(), {
  tag: 'aside'
})

const Auth = useAuth()
const router = useRouter()
const ability = useAppAbility()
const version = import.meta.env.VITE_VERSION
const version_release = ref(version)
const callAxios = useAxios()
const baseUrlImg = localStorage.baseUrlImg
const isDialogVisible = ref(false)
const ListItem = ref([])
const refNav = ref()

interface Props {
  tag?: string | Component
  navItems: VerticalNavItems
  isOverlayNavActive: boolean
  toggleIsOverlayNavActive: (value: boolean) => void
}

const navFooterLogout = ref({
  title: 'ออกจากระบบ',
  icon: {
    icon: 'mdi-logout'
  }
})

const navFooterVersion = ref({
  title: 'Version',
  icon: {
    icon: 'mdi-file-document-outline'
  }
})

const { width: windowWidth } = useWindowSize()
const isHovered = useElementHover(refNav)

provide(injectionKeyIsVerticalNavHovered, isHovered)

const {
  isVerticalNavCollapsed: isCollapsed,
  isLessThanOverlayNavBreakpoint,
  isVerticalNavMini,
  isAppRtl
} = useLayouts()

const hideTitleAndIcon = isVerticalNavMini(windowWidth, isHovered)

const resolveNavItemComponent = (item: NavLink | NavSectionTitle | NavGroup) => {
  if ('heading' in item) return VerticalNavSectionTitle

    if ('children' in item && item.children.length > 0) return VerticalNavGroup
  //  if ('children' in item && Array.isArray(item.children) && item.children.length > 0)
  //    return VerticalNavGroup

  return VerticalNavLink
}

const sortedMenus = computed(() => {
  const storedMenusStr = localStorage.getItem('menus')
  let storedMenus = []

  if (storedMenusStr) {
    try {
      storedMenus = JSON.parse(storedMenusStr)
    } catch (e) {
      console.error('Failed to parse menus from localStorage', e)
      storedMenus = []
    }
  }
  const allmenu= storedMenus
    .filter((item:any) => item && typeof item === 'object') // กรองข้อมูลที่ไม่ใช่ object ออก
    .sort((a:any, b:any) => (a.sorting ?? 0) - (b.sorting ?? 0)) // เรียงจากน้อยไปมาก
  const mainmenu= allmenu?.filter(f=>f.parent_id==null).map(main => {
    return {
      title: main.title,
      to: '',
      icon: {
        icon: main.icon
      },
      children: [],
      menu_id:main.menu_id
    }
  });
  mainmenu.forEach(main => {
    const allchild= allmenu.filter(f => f.parent_id == main.menu_id)
    main.children = allchild.map(child => {
      return {
        title: child.title,
        to: {
          name: child.path_url
        },
      
      }
    })
  });
  console.log('mainmenu', mainmenu)
  return  mainmenu
})

function handleClick(item) {
  if (item.path_url) {
    const path = '/' + item.path_url.replace(/-/g, '/')
    router.push(path)
  }
}

const GetSystemLogosBackoffice = () => {
  // callAxios
  //   .RequestGet("/OtherMaster/GetLogo?SystemName=Backoffice")
  //   .then((response) => {
  //     if (response.status == 200 && response.data.response.pathName)
  //       Auth.LogoBackoffice = baseUrlImg + response.data.response.pathName;
  //   });
}

const GetVersion = () => {
  // callAxios.RequestGet("/Deploy/GetDeploymentTrackings").then((response) => {
  //   if (response.status == 200) {
  //     ListItem.value = response.data.response;
  //     ListItem.value.sort((a, b) => b.id - a.id);
  //     navFooterVersion.value.title =
  //       "Version " + ListItem.value[0].deployVersion;
  //   }
  // });
}

/*
  ℹ️ Close overlay side when route is changed
  Close overlay vertical nav when link is clicked
*/
const route = useRoute()

watch(
  () => route.name,
  () => {
    props.toggleIsOverlayNavActive(false)
  }
)

const isVerticalNavScrolled = ref(false)

const updateIsVerticalNavScrolled = (val: boolean) => (isVerticalNavScrolled.value = val)

const handleNavScroll = (evt: Event) => {
  isVerticalNavScrolled.value = (evt.target as HTMLElement).scrollTop > 0
}

const logout = () => {
  localStorage.removeItem('token')
  localStorage.removeItem('username')
  router.push('/').then(() => {
    localStorage.removeItem('userAbilities')
    ability.update(initialAbility)
  })
}

onMounted(() => {
  GetVersion()
  GetSystemLogosBackoffice()
  Auth.initializeTheme()
})
</script>

<template>
  <Component
    :is="props.tag"
    ref="refNav"
    class="layout-vertical-nav"
    :class="[
      {
        'overlay-nav': isLessThanOverlayNavBreakpoint(windowWidth),
        hovered: isHovered,
        visible: isOverlayNavActive,
        scrolled: isVerticalNavScrolled
      }
    ]"
  >
    <!-- 👉 Header -->
    <div class="nav-header bg-white mx-0 ps-5">
      <slot name="nav-header">
        <RouterLink to="/" class="app-logo d-flex align-center gap-x-2 app-title-wrapper">
          <VImg v-if="Auth.LogoBackoffice" :src="Auth.LogoBackoffice" />
          <VNodeRenderer v-else :nodes="config.app.logo" />
          <!--
            <Transition name="vertical-nav-app-title">
            <h1
            v-show="!hideTitleAndIcon"
            class="leading-normal text-xl font-weight-bold text-capitalize"
            >
            {{ config.app.title }}
            </h1>
            </Transition>
          -->
        </RouterLink>
        <!-- 👉 Vertical nav actions -->
        <!-- Show toggle collapsible in >md and close button in <md -->
        <template v-if="!isLessThanOverlayNavBreakpoint(windowWidth)">
          <Component
            :is="config.app.iconRenderer || 'div'"
            v-show="isCollapsed && !hideTitleAndIcon"
            class="header-action"
            v-bind="config.icons.verticalNavUnPinned"
            @click="isCollapsed = !isCollapsed"
          />
          <Component
            :is="config.app.iconRenderer || 'div'"
            v-show="!isCollapsed && !hideTitleAndIcon"
            class="header-action"
            v-bind="config.icons.verticalNavPinned"
            @click="isCollapsed = !isCollapsed"
          />
        </template>
        <template v-else>
          <Component
            :is="config.app.iconRenderer || 'div'"
            class="header-action"
            v-bind="config.icons.close"
            @click="toggleIsOverlayNavActive(false)"
          />
        </template>
      </slot>
    </div>
    <slot name="before-nav-items">
      <div class="vertical-nav-items-shadow" />
    </slot>
    <slot name="nav-items" :update-is-vertical-nav-scrolled="updateIsVerticalNavScrolled">
      <PerfectScrollbar
        :key="isAppRtl"
        tag="ul"
        class="nav-items mt-4"
        :options="{ wheelPropagation: false }"
        @ps-scroll-y="handleNavScroll"
      >
        <Component
          v-for="(item, index) in sortedMenus"
            class="cursor-pointer"
          :key="index"
          :is="resolveNavItemComponent(item)"
          :item="item"
          @click="handleClick(item)"
        />
      </PerfectScrollbar>
    </slot>
    <div class="nav-footer">
      <slot name="nav-footer">
        <PerfectScrollbar
          :key="isAppRtl"
          tag="ul"
          class="nav-items"
          :options="{ wheelPropagation: false }"
          @ps-scroll-y="handleNavScroll"
        >
          <Component
            :is="resolveNavItemComponent(navFooterVersion)"
            style="cursor: pointer;"
            :item="navFooterVersion"
            @click="isDialogVisible = true"
          />
          <Component
            :is="resolveNavItemComponent(navFooterLogout)"
            style="cursor: pointer;"
            :item="navFooterLogout"
            @click="logout"
          />
        </PerfectScrollbar>
      </slot>
    </div>
  </Component>
  <DialogReleaseVersion v-model:is-dialog-visible="isDialogVisible" :items="ListItem" />
</template>

<style lang="scss">
@use "@configured-variables" as variables;
@use "@layouts/styles/mixins";

// 👉 Vertical Nav
.layout-vertical-nav {
  position: fixed;
  z-index: variables.$layout-vertical-nav-z-index;
  display: flex;
  flex-direction: column;
  block-size: 100%;
  inline-size: variables.$layout-vertical-nav-width;
  inset-block-start: 0;
  inset-inline-start: 0;
  transition:
    transform 0.25s ease-in-out,
    inline-size 0.25s ease-in-out,
    box-shadow 0.25s ease-in-out;
  will-change: transform, inline-size;

  .nav-header {
    display: flex;
    align-items: center;

    .header-action {
      cursor: pointer;
    }
  }

  .app-title-wrapper {
    margin-inline-end: auto;
  }

  .nav-items {
    block-size: 100%;

    // ℹ️ We no loner needs this overflow styles as perfect scrollbar applies it
    // overflow-x: hidden;

    // // ℹ️ We used `overflow-y` instead of `overflow` to mitigate overflow x. Revert back if any issue found.
    // overflow-y: auto;
  }

  .nav-item-title {
    overflow: hidden;
    margin-inline-end: auto;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  // 👉 Collapsed
  .layout-vertical-nav-collapsed & {
    &:not(.hovered) {
      inline-size: variables.$layout-vertical-nav-collapsed-width;
    }
  }

  // 👉 Overlay nav
  &.overlay-nav {
    &:not(.visible) {
      transform: translateX(-#{variables.$layout-vertical-nav-width});

      @include mixins.rtl {
        transform: translateX(variables.$layout-vertical-nav-width);
      }
    }
  }
}
</style>
