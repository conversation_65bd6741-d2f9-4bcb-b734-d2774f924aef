<script setup lang="ts">
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import type { FormConfigurationEmails } from '@interfaces/FormInterface'
import type { IGetConfigurationEmailsRes } from '@interfaces/UserManagementInterface'
import { emailValidator, inputNumberOnly, requiredValidator } from '@validators'

const callAxios = useAxios()
const Swal = useSwal()
const refVForm = ref()
const refVFormTestEmail = ref()
const isUpdated = ref(false)

const form = reactive<FormConfigurationEmails>({
  systemEmailServiceId: 0,
  systemId: 0,
  smtpHost: '',
  smtpEmail: '',
  smtpPort: '',
  testMail: '',
  activessl: true,
  activeAuthentication: true,
  username: '',
  password: '',
  senderName: '',
  timeOut: null,
  createDate: '',
  updateDate: '',
  createBy: '',
  updateBy: ''
})

const formRaw = ref<IGetConfigurationEmailsRes>({} as IGetConfigurationEmailsRes)

const show1 = ref(false)

const breadcrumbItems = [
  {
    title: 'ผู้ดูแลระบบ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'ตั้งค่าอีเมลกลาง',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const GetList = async () => {
  try {
    const response = await callAxios.RequestGet('/OtherMaster/GetConfigurationEmails')

    if (response.status === 200 && response.data.response.length > 0) {
      const configData = response.data.response[0]

      Object.assign(form, configData) // คัดลอกค่าทั้งหมดจาก configData ไปยัง form
      formRaw.value = { ...configData } // เก็บสำเนาของข้อมูลใน formRaw
    }
  } catch (error) {
    Swal.callCatch()
  }
}

const UpdateConfigurationEmails = async () => {
  try {
    const confirmed = await Swal.ApproveConfirm()
    if (confirmed) {
      const sanitizedForm = {
        ...form,
        timeOut: form.timeOut || 0 // แปลง null เป็น 0
      }

      const response = await callAxios.RequestPut(
        `/OtherMaster/UpdateConfigurationEmails?SystemEmailServiceId=${form.systemEmailServiceId}`,
        sanitizedForm
      )

      if (response.status === 200) {
        formRaw.value = { ...sanitizedForm } // อัปเดต formRaw
        isUpdated.value = true // ตั้งค่าสถานะว่าอัปเดตเรียบร้อยแล้ว
        Swal.LabelSuccess('บันทึกการตั้งค่า')

        await GetList()
      } else {
        Swal.EditConditionFail()
      }
    }
  } catch (error) {
    Swal.EditFail()
  }
}

const TestSendMail = async () => {
  try {
    // ตรวจสอบว่าฟอร์มถูกต้องก่อนส่งอีเมล
    if (!refVForm.value) return
    const { valid } = await refVForm.value.validate()

    if (!valid) return // หากฟอร์มไม่ถูกต้อง หยุดการทำงานทันที

    // ตรวจสอบว่า testMail ไม่ว่างเปล่า
    if (!form.testMail || form.testMail.trim() === '') return

    // ตรวจสอบรูปแบบอีเมล
    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/

    const invalidDomainPattern = /@[\d]+[a-zA-Z]+\.|@[a-zA-Z]+[\d]+\.|@[a-zA-Z]+\.[a-zA-Z]+[\d]+$/

    if (!emailPattern.test(form.testMail.trim()) || invalidDomainPattern.test(form.testMail.trim()))
      return

    const response = await callAxios.RequestPost(
      `/OtherMaster/TestSendMail?Email=${form.testMail.trim()}`
    )

    if (response.status === 200) Swal.LabelSuccess('ส่งทดสอบอีเมล')
    else Swal.LabelFail('การส่งอีเมลทดสอบ')
  } catch (error) {
    Swal.LabelError()
  }
}

// const CheckDataisNotChanged = () => {
//   if (deepEqual(formRaw.value, form)) {
//     TestSendMail();
//   } else {
//     UpdateConfigurationEmails();
//   }
// };

const deepEqual = (obj1: any, obj2: any): boolean => {
  // ตรวจสอบชนิดของข้อมูล
  if (obj1 === obj2) return true
  if (typeof obj1 !== 'object' || typeof obj2 !== 'object' || obj1 === null || obj2 === null)
    return false

  // ตรวจสอบจำนวน key
  const keys1 = Object.keys(obj1)
  const keys2 = Object.keys(obj2)

  if (keys1.length !== keys2.length) return false

  // ตรวจสอบค่าใน key แต่ละตัว
  for (const key of keys1) {
    if (!keys2.includes(key)) return false
    if (!deepEqual(obj1[key], obj2[key])) return false
  }

  return true
}

const onFormSubmit = async () => {
  try {
    if (!refVForm.value) return
    const { valid } = await refVForm.value.validate()
    if (valid) await UpdateConfigurationEmails()
  } catch (error) {
    Swal.AddConditionFailText('เกิดข้อผิดพลาด')
  }
}

const onInputNumberOnly = (field: keyof FormConfigurationEmails, event: Event) => {
  const input = (event.target as HTMLInputElement).value

  // แทนที่ตัวเลขที่ไม่ใช่ 0-9 และลบเลข 0 ที่เป็นตัวแรก
  const sanitizedInput = input.replace(/\D/g, '').replace(/^0+/, '')

  form[field as keyof typeof form] = sanitizedInput as never
}

const onInputEmail = (event: Event) => {
  const input = (event.target as HTMLInputElement).value

  // ลบตัวอักษรภาษาไทยออก
  form.testMail = input.replace(/[\u0E00-\u0E7F]/g, '')
}

onMounted(() => {
  GetList()
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <!-- {{ filter }} -->

    <VForm ref="refVForm" @submit.prevent="onFormSubmit">
      <!-- 👉 Filters  -->
      <VCard class="mb-5">
        <VCardText>
          <VRow class="mb-4 mx-2 mt-6">
            <VCol cols="12">
              <VRow no-gutters align="center">
                <!-- 👉 First Name -->
                <VCol cols="12" sm="5" md="3" class="text-md-end pe-2">
                  <label class="text-lg">
                    Mail Engine :
                    <small class="text-error">*</small>
                  </label>
                </VCol>
                <VCol cols="12" sm="5">
                  <VTextField
                    v-model="form.senderName"
                    :counter="20"
                    :rules="[requiredValidator]"
                    placeholder="Mail or SMTP"
                  />
                </VCol>
              </VRow>
            </VCol>
            <VCol cols="12">
              <VRow no-gutters align="center">
                <!-- 👉 First Name -->
                <VCol cols="12" sm="5" md="3" class="text-md-end pe-2">
                  <label class="text-lg">
                    Mail Parameters :
                    <small class="text-error">*</small>
                  </label>
                </VCol>
                <VCol cols="12" sm="5">
                  <VTextField
                    v-model="form.smtpEmail"
                    :counter="100"
                    :rules="[requiredValidator]"
                    placeholder="Mail Parameters"
                  />
                </VCol>
              </VRow>
            </VCol>

            <VCol cols="12">
              <VRow no-gutters align="center">
                <!-- 👉 First Name -->
                <VCol cols="12" sm="5" md="3" class="text-md-end pe-2">
                  <label class="text-lg">
                    SMTP Hostname :
                    <small class="text-error">*</small>
                  </label>
                </VCol>
                <VCol cols="12" sm="5">
                  <VTextField
                    v-model="form.smtpHost"
                    :counter="200"
                    :rules="[requiredValidator]"
                    placeholder="IP or domain"
                  />
                </VCol>
              </VRow>
            </VCol>
            <VCol cols="12">
              <VRow no-gutters align="center">
                <!-- 👉 First Name -->
                <VCol cols="12" sm="5" md="3" class="text-md-end pe-2">
                  <label class="text-lg">SMTP Username :</label>
                </VCol>
                <VCol cols="12" sm="5">
                  <VTextField
                    v-model="form.username"
                    :rules="[emailValidator]"
                    placeholder="Username"
                    :counter="40"
                    :maxlength="40"
                  />
                </VCol>
              </VRow>
            </VCol>
            <VCol cols="12">
              <VRow no-gutters align="center">
                <!-- 👉 First Name -->
                <VCol cols="12" sm="5" md="3" class="text-md-end pe-2">
                  <label class="text-lg">SMTP Password :</label>
                </VCol>
                <VCol cols="12" sm="5">
                  <VTextField
                    v-model="form.password"
                    :append-inner-icon="show1 ? 'mdi-eye-off-outline' : 'mdi-eye-outline'"
                    :type="show1 ? 'text' : 'password'"
                    placeholder="Password"
                    :counter="20"
                    :maxlength="20"
                    @click:append-inner="show1 = !show1"
                  />
                </VCol>
              </VRow>
            </VCol>
            <VCol cols="12">
              <VRow no-gutters align="center">
                <!-- 👉 First Name -->
                <VCol cols="12" sm="5" md="3" class="text-md-end pe-2">
                  <label class="text-lg">
                    SMTP Port :
                    <small class="text-error">*</small>
                  </label>
                </VCol>
                <VCol cols="12" sm="5">
                  <VTextField
                    v-model="form.smtpPort"
                    placeholder="Port"
                    :counter="5"
                    :maxlength="5"
                    :rules="[inputNumberOnly]"
                    @input="onInputNumberOnly('smtpPort', $event)"
                  />
                </VCol>
              </VRow>
            </VCol>
            <VCol cols="12">
              <VRow no-gutters align="center">
                <!-- 👉 First Name -->
                <VCol cols="12" sm="5" md="3" class="text-md-end pe-2">
                  <label class="text-lg">
                    SMTP Timeout :
                    <small class="text-error">*</small>
                  </label>
                </VCol>
                <VCol cols="12" sm="5">
                  <VTextField
                    v-model="form.timeOut"
                    placeholder="Timeout"
                    :counter="5"
                    :maxlength="5"
                    :rules="[inputNumberOnly]"
                    @input="onInputNumberOnly('timeOut', $event)"
                  />
                </VCol>
              </VRow>
            </VCol>

            <VCol cols="12" class="d-flex flex-wrap justify-center mt-3">
              <div class="demo-space-x">
                <VBtn type="submit" color="blue-600" rounded="xl" prepend-icon="mdi-content-save">
                  บันทึก
                </VBtn>
              </div>
            </VCol>
          </VRow>
        </VCardText>
      </VCard>
    </VForm>

    <VForm ref="refVFormTestEmail" @submit.prevent="TestSendMail">
      <!-- 👉 Filters  -->
      <VCard class="mb-5">
        <VCardText>
          <VRow>
            <VCol cols="12">
              <VRow align="center">
                <VCol cols="12" sm="5" md="3" class="text-md-end pe-2">
                  <label class="text-lg">ทดสอบอีเมล :</label>
                </VCol>
                <VCol cols="12" sm="5">
                  <VTextField
                    v-model="form.testMail"
                    class="no-select"
                    placeholder="อีเมลปลายทาง"
                    :counter="100"
                    :maxlength="100"
                    :rules="[emailValidator]"
                    :disabled="!isUpdated || deepEqual(form, formRaw)"
                    @input="onInputEmail"
                  />
                </VCol>
                <VCol cols="2" xs="12" class=" ">
                  <div class="demo-space-x d-flex flex-wrap justify-center">
                    <VBtn
                      type="submit"
                      class="text-lg"
                      color="blue-600"
                      rounded="xl"
                      prepend-icon="mdi-email-outline"
                      :disabled="!isUpdated || deepEqual(form, formRaw)"
                    >
                      ส่งอีเมล
                    </VBtn>
                  </div>
                </VCol>
              </VRow>
            </VCol>
          </VRow>
        </VCardText>
      </VCard>
    </VForm>
  </div>
</template>

<style scoped>
.no-select {
  user-select: none;
}
</style>
