import axios, {
  type AxiosError,
  type AxiosInstance,
  type AxiosResponse,
  type InternalAxiosRequestConfig
} from 'axios'
import Swal from 'sweetalert2'
import type { App } from 'vue'
import router from '@/router'

export default function install(app: App) {
  const baseURL: string = import.meta.env.VITE_BASE_URL as string

  const Axios: AxiosInstance = axios.create({
    baseURL,
    headers: {
      'X-Custom-Header': 'foobar',
      'Content-Type': 'application/json;charset=UTF-8',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': '*'
    }
  })

  Axios.interceptors.request.use((config: InternalAxiosRequestConfig) => {
    const token = localStorage.getItem('token')

    if (token) {
      config.headers = config.headers || {}
      config.headers.Authorization = token ? `Bearer ${token}` : ''
    }

    return config
  })

  const LOGOUT = import.meta.env.VITE_URL_LOGOUT

  Axios.interceptors.response.use(
    (response: AxiosResponse) => {
      // Swal.close();
      return response
    },
    (error: AxiosError) => {
      Swal.close()
      if (
        error.response?.status === 401 &&
        router.currentRoute.value.path !== '/apps/login' &&
        router.currentRoute.value.path !== '/apps/aBcDeFgHiJkLmNo' &&
        localStorage.getItem('token') != null
      ) {
        let textAlert = 'เซสซันหมดอายุ โปรดเข้าสู่ระบบอีกครั้ง'
        if (router.currentRoute.value.query?.by === 'public')
          textAlert = 'กรุณาเข้าสู่ระบบเพื่อใช้งาน'

        return Swal.fire({
          icon: 'error',
          title: 'แจ้งเตือน',
          text: textAlert,
          showCancelButton: false,
          confirmButtonText: 'ตกลง',
          allowOutsideClick: false
        }).then(async result => {
          if (result.isConfirmed) {
            localStorage.removeItem('token')
            localStorage.removeItem('userAbilities')
            localStorage.removeItem('username')
            window.location.href = '/apps/login'
          }
        })
      }

      return Promise.reject(error)
    }
  )

  app.config.globalProperties.$axios = Axios
  app.provide('axios', Axios)
}
