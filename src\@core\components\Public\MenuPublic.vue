<script setup lang="ts">
import { useAuth } from '@/store/useAuth'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const callAxios = useAxios()
const Swal = useSwal()
const Auth = useAuth()

// ฟังก์ชันดึงข้อมูล navbar จาก API
const GetNavbarData = async () => {
  try {
    const response = await callAxios.RequestGet(`/OtherMaster/GetFooterNavBar`)
    if (response.status === 200) {
      Auth.NavbarItem = response.data.navBar
      Auth.FooterMenuItem = response.data.navBar
      Auth.newsData = response.data.response.detailBanner
      Auth.FooterData = response.data.response
      Auth.StatusDashboard = response.data.statusDashboard
    }
  } catch (error) {
    console.error('Error fetching Navbar data:', error)
    Swal.callCatch()
  }
}

const handleStorageChange = (event: StorageEvent) => {
  if (event.key === 'footer-updated') {
    GetNavbarData()
  }
}

onMounted(() => {
  GetNavbarData()
  window.addEventListener('storage', handleStorageChange)
})

onUnmounted(() => {
  window.removeEventListener('storage', handleStorageChange)
})

// ใช้สำหรับกำหนดเมนูที่ active
const addActive = ref<string | null>(null)
const subMenuActive = ref<string | null>(null)

// เก็บ index ของเมนูที่เปิดอยู่
const openMenuIndex = ref<number | null>(null)

// กำหนดเมนู active ตาม route ปัจจุบัน
router.afterEach(() => {
  const pathName = router.currentRoute.value.path
  Auth.NavbarItem.forEach((item: any, index: number) => {
    if (item.url === pathName) {
      addActive.value = item.webpageName
      openMenuIndex.value = index // เปิดเมนูที่ตรงกับ path ปัจจุบัน
    }
    item.subList.forEach((subItem: any) => {
      if (subItem.url === pathName) {
        addActive.value = item.webpageName
        subMenuActive.value = subItem.webpageName
        openMenuIndex.value = index
      }
    })
  })
})

// ฟังก์ชันเปิด/ปิด sub-menu
const openNavBtn = (index: number) => {
  openMenuIndex.value = openMenuIndex.value === index ? null : index
}

const ensureAbsolutePath = (url: string, systemManualId: string): string => {
  // ตรวจสอบว่า URL เป็น absolute path หรือไม่
  const isAbsolute = url.startsWith('/') || /^(http|https):\/\//.test(url)
  if (isAbsolute) {
    return `${url}?systemManualId=${systemManualId}`
  }
  return ''
}
</script>

<template>
  <ul class="nav navbar-nav navbar show mt-md-3">
    <li
      v-for="(item, index) in Auth.NavbarItem"
      :key="index"
      :class="{
        'sub-menu-down': item.subList.length > 0,
        active: item.webpageName === addActive,
        open: openMenuIndex === index
      }"
      @click="item.subList.length > 0 && openNavBtn(index)"
    >
      <!-- Main Menu -->
      <template v-if="!item.isActive">
        <a>
          <span>{{ item.webpageName }}</span>
        </a>
      </template>

      <template v-else-if="item.contentId">
        <RouterLink :to="`/public/information?contentId=${item.contentId}`">
          {{ item.webpageName }}
        </RouterLink>
      </template>

      <template v-else-if="!item.url || item.url.startsWith('/')">
        <RouterLink :to="ensureAbsolutePath(item.url, item.systemManualId)">
          <span>{{ item.webpageName }}</span>
        </RouterLink>
      </template>

      <template v-else>
        <a
          :href="ensureAbsolutePath(item.url, item.systemManualId)"
          target="_blank"
          rel="noopener noreferrer"
        >
          <span>{{ item.webpageName }}</span>
        </a>
      </template>
      <!-- Sub-menu -->
      <ul v-if="item.subList.length" class="sub-menu">
        <li
          v-for="(subItem, subIndex) in item.subList"
          :key="subIndex"
          :class="{ active: subItem.webpageName === subMenuActive }"
        >
          <template v-if="subItem.contentId">
            <RouterLink :to="`/public/information?contentId=${subItem.contentId}`">
              {{ subItem.webpageName }}
            </RouterLink>
          </template>
          <template v-else-if="subItem.url && !subItem.url.startsWith('/')">
            <a
              :href="ensureAbsolutePath(subItem.url, subItem.systemManualId)"
              target="_blank"
              rel="noopener noreferrer"
            >
              {{ subItem.webpageName }}
            </a>
            <h2>{{ subItem.systemManualId }}</h2>
          </template>
          <template v-else>
            <RouterLink :to="ensureAbsolutePath(subItem.url, subItem.systemManualId)">
              {{ subItem.webpageName }}
            </RouterLink>
          </template>
        </li>
      </ul>
    </li>
  </ul>
</template>

<style lang="css" scoped>
.header-nav .nav > li > a {
  color: #fff;
}

.v-application-by .header-nav .nav > li > a {
  color: #fecb00 !important;
}

.v-application-by .header-nav .nav > li .mega-menu,
.header-nav .nav > li .sub-menu {
  background: #ffffff;
  /* background: #111111; */
}

.v-application-by .header-nav .nav > li .mega-menu li a,
.v-application-by .header-nav .nav > li .sub-menu li a {
  color: #fecb00;
}

.v-application-by .header-nav .nav > li .mega-menu li a:after,
.v-application-by .header-nav .nav > li .sub-menu li a:after {
  background: #000000;
}

.v-application-by .header-nav .nav > li .mega-menu li:hover > a,
.v-application-by .header-nav .nav > li .sub-menu li:hover > a {
  background-color: #fecb00;
  color: #000000;
}

@media (min-width: 431px) and (max-width: 1025px) {
  .header-nav .nav > li > a {
    padding: 10px 12px !important;
    padding-top: 0px !important;
  }
}

@media (max-width: 991px) {
  .header-nav .nav > li {
    border-bottom: 1px solid #eeeeee76;
  }
  ul .sub-menu {
    background-color: #143874 !important;
  }
  .header-nav .nav > li .sub-menu li a:after {
    color: #fff;
  }
  .header-nav .nav > li .sub-menu li a {
    margin-left: 15px;
    color: #fff;
  }
  .header-nav .nav > li.sub-menu-down {
    margin-top: 10px;
  }
  .header-nav .nav > li.sub-menu-down > a:after {
    background-color: #ffe492;
    color: #143874;
  }

  .v-application-theme2 ul .sub-menu {
    background-color: #ffb049 !important;
  }

  .v-application-theme2 h1.fw-normal,
  .v-application-theme2 .header-nav .nav > li > a,
  .v-application-theme2 .header-nav .nav > li .sub-menu li a,
  .v-application-theme2 .header-nav .nav > li .sub-menu li a:after {
    color: #333333;
    transition: none !important;
  }

  .v-application-theme2 .boxContact ul li::before {
    background: #000000;
    color: #ffb049;
  }
  .v-application-theme3 h1.fw-normal,
  .v-application-theme3 .header-nav .nav > li > a,
  .v-application-theme3 .header-nav .nav > li .sub-menu li a,
  .v-application-theme3 .header-nav .nav > li .sub-menu li a:after {
    color: #333333;
    transition: none !important;
  }

  .v-application-theme3 ul .sub-menu {
    background-color: #d993ab !important;
  }

  .v-application-theme3 .mo-left .header-nav.show {
    background: #d993ab;
  }

  .v-application-theme3 .boxContact ul li::before {
    background: #000000;
    color: #d993ab;
  }

  .v-application-by ul .sub-menu {
    background-color: #000000 !important;
  }

  .v-application-by .header-nav .nav > li .sub-menu li a,
  .v-application-by .header-nav .nav > li .sub-menu li a:after {
    color: #fecb00 !important;
    transition: none !important;
  }

  .v-application-by .header-nav .nav > li .mega-menu li a:after,
  .v-application-by .header-nav .nav > li .sub-menu li a:after {
    background: #000000 !important;
    transition: none !important;
  }
}
</style>
