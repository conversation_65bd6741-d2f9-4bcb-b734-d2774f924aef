<script setup lang="ts">
import BtnInsert from '@/@core/components/button/BtnInsert.vue'
import IconDelete from '@/@core/components/button/IconDelete.vue'
import IconEdit from '@/@core/components/button/IconEdit.vue'
import FiltersAPI from '@/components/FiltersAPI.vue'
import { useAxios } from '@/store/useAxios'
import { useStatus } from '@/store/useStatus'
import { useSwal } from '@/store/useSwal'
import type { IGetSystemWebpagesRes } from '@interfaces/UserManagementInterface'
import { onMounted, ref } from 'vue'
import DialogNavbarMenu from '../components/tabmenu/DialogNavbarMenu.vue'

const breadcrumbItems = [
  {
    title: 'ผู้ดูแลระบบ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'จัดการหมู่บ้าน',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const router = useRouter()

// Dependencies and utilities
const callAxios = useAxios()
const Swal = useSwal()
const callStatus = useStatus()

// Pagination and state management
const currentPage = ref(1)
const rowPerPage = ref(20)
const totalRecords = ref(0)
const editId = ref()

// Dialog visibility states
const isDialogVisible = ref(false)
const isAdd = ref(false)
const isEdit = ref(false)
const isView = ref(false)

// Data list
const ListItem = ref<IGetSystemWebpagesRes[]>([])

// Dialog open functions
const openAddDialog = () => {
  isAdd.value = true
  isEdit.value = false
  isView.value = false
  isDialogVisible.value = true
}

const openEditDialog = (id: string) => {
  isEdit.value = true
  isView.value = false
  isAdd.value = false
  isDialogVisible.value = true
  editId.value = id
}

const openViewDialog = (id: string) => {
  isView.value = true
  isEdit.value = false
  isAdd.value = false
  isDialogVisible.value = true
  editId.value = id
}

// Pagination change handler
const onPageChange = (event: { page: number; rows: number }) => {
  currentPage.value = event.page + 1
  rowPerPage.value = event.rows
  GetList()
}

// Filters configuration
const fieldValues = reactive({
  searchProvince: null,
  searchDistrict: null,
  searchSubDistrict: null,
  villageNumber: null
})

const listFilter = ref([
  {
    name: 'searchProvince',
    type: 'select',
    label: 'จังหวัด',
    default: null,
    title: 'province_name',
    value: 'province_id',
    items: [],
    placeholder: ''
  },
  {
    name: 'searchDistrict',
    type: 'select',
    label: 'อำเภอ',
    default: null,
    title: 'district_name',
    value: 'district_id',
    items: [],
    placeholder: ''
  },
  {
    name: 'searchSubDistrict',
    type: 'select',
    label: 'ตำบล',
    default: null,
    title: 'subdistrict_name',
    value: 'subdistrict_id',
    items: [],
    placeholder: ''
  },
  {
    name: 'villageNumber',
    type: 'select',
    label: 'หมู่ที่',
    default: '',
    title: 'label',
    value: 'value',
    items: [],
    placeholder: ''
  },
  {
    name: 'searchWord',
    type: 'text',
    label: 'คำค้น',
    placeholder: 'ระบุ ชื่อหมู่บ้าน'
  }
])

// Table fields configuration
const listFields = ref([
  {
    field: '',
    header: '#',
    sortable: false,
    style: { textAlign: 'center' }
  },
  {
    field: 'no',
    header: 'ลำดับที่',
    sortable: false,
    style: { textAlign: 'center' }
  },
  {
    field: 'sorting',
    header: 'จังหวัด',
    sortable: false,
    style: { textAlign: 'center' }
  },
  { field: 'webpageName', header: 'อำเภอ', sortable: false },
  { field: 'isActive', header: 'ตำบล', sortable: false },
  { field: 'createDate', header: 'หมู่ที่', sortable: false },
  { field: 'createBy', header: 'ชื่อหมู่บ้าน', sortable: false },
  { field: 'updateDate', header: 'วันที่สร้าง', sortable: false },
  { field: 'updateBy', header: 'ชื่อผู้สร้าง', sortable: false },
  { field: 'options', header: 'วันที่แก้ไข', sortable: false },
  { field: 'options', header: 'ชื่อผู้แก้ไข', sortable: false }
])

// Row highlight check
const checkSubNo = (rowData: any) => {
  if (rowData && typeof rowData.no === 'string' && !rowData.no.includes('.')) {
    // Add/remove "!" to toggle row highlighting
    return 'bg-sub-no'
  }

  return ''
}

const GetList = async () => {
  fieldValues.searchDistrict = ''
  await loadGetDistrict(fieldValues.searchProvince)
}

const ApproveDelete = (id: number) => {
  const endpoint = `/UserManagement/DeleteSystemWebPages?WebpageId=${id}`

  Swal.ApproveDelete(endpoint).then(response => {
    if (response) GetList()
  })
}

// Dialog update handler
const handleDialogUpdate = () => {
  GetList() // Refresh the list after add/edit operation
}

const loadProvinceItems = async () => {
  try {
    await callStatus.getProvince()
    const statusOptions = callStatus.listProvinceFilter

    const targetField = listFilter.value.find(f => f.name === 'searchProvince')

    if (targetField) {
      targetField.items = statusOptions.map(({ province_id, province_name }) => ({
        province_id,
        province_name
      }))

      fieldValues.searchProvince =
        targetField.items.length > 0 ? targetField.items[0].province_id : null
    }
  } catch (err) {
    console.error('โหลดข้อมูลจังหวัดไม่สำเร็จ', err)
  }
}

const loadGetDistrict = async (provinceId: number) => {
  try {
    await callStatus.getDistrict(provinceId)
    const districtOptions = callStatus.listDistrictFilter

    const targetField = listFilter.value.find(f => f.name === 'searchDistrict')
    if (targetField) {
      targetField.items = districtOptions.map(({ district_id, district_name }) => ({
        district_id,
        district_name
      }))

      fieldValues.searchDistrict =
        districtOptions.length > 0 ? districtOptions[0].district_id : null
    }
  } catch (err) {
    console.error('❌ โหลดอำเภอไม่สำเร็จ:', err)
    fieldValues.searchDistrict = ''
  }
}

const loadGetSubDistrict = async (districtId: number) => {
  try {
    await callStatus.getSubDistrict(districtId)
    const subDistrictOptions = callStatus.listSubDistrict

    const targetField = listFilter.value.find(f => f.name === 'searchSubDistrict')
    if (targetField) {
      targetField.items = subDistrictOptions.map(({ subdistrict_id, subdistrict_name }) => ({
        subdistrict_id,
        subdistrict_name
      }))

      fieldValues.searchSubDistrict =
        subDistrictOptions.length > 0 ? subDistrictOptions[0].subdistrict_id : null
    }
  } catch (err) {}
  console.error('โหลดข้อมูลตำบลไม่สำเร็จ:', err)
}

const handleFieldChange = async ({ name, value }) => {
  if (name === 'searchProvince') {
    await loadGetDistrict(value)

    fieldValues.searchDistrict = null
    fieldValues.searchSubDistrict = null

    const subDistrictField = listFilter.value.find(f => f.name === 'searchSubDistrict')
    if (subDistrictField) {
      subDistrictField.items = []
    }

    const villageNumber = listFilter.value.find(f => f.name === 'villageNumber')
    if (villageNumber) {
      villageNumber.items = []
    }
  }

  if (name === 'searchDistrict') {
    fieldValues.searchSubDistrict = null
    fieldValues.villageNumber = null
    const villageNumber = listFilter.value.find(f => f.name === 'villageNumber')
    if (villageNumber) {
      villageNumber.items = []
    }

    await loadGetSubDistrict(value)
  }

  if (name === 'searchSubDistrict') {
    const villageField = listFilter.value.find(f => f.name === 'villageNumber')

    if (villageField) {
      villageField.items = Array.from({ length: 99 }, (_, i) => {
        const num = i + 1
        return {
          label: num.toString().padStart(2),
          value: num
        }
      })

      fieldValues.villageNumber = null
    }
  }
}

// Fetch data on mount
onMounted(() => {
  loadProvinceItems()
  GetList()
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <VRow>
      <VCol cols="12" md="12" lg="12">
        <FiltersAPI
          v-model="fieldValues"
          :fields="listFilter"
          @submit="GetList"
          @change="handleFieldChange"
        />
        <VCard class="px-4">
          <VCardText>
            <div class="d-flex align-center flex-wrap gap-4">
              <BtnInsert
                class="mt-2"
                prepend-icon="mdi-plus-box-multiple"
                color="btn-add"
                rounded="ml"
                @click="openAddDialog"
              >
                เพิ่มข้อมูล
              </BtnInsert>
              <BtnInsert class="mt-2" color="navy-200" rounded="ml" @click="openAddDialog">
                ดึงข้อมูล
              </BtnInsert>
              <BtnInsert class="mt-2" color="btn-search" rounded="ml" @click="openAddDialog">
                ส่งออก TEMPLATE
              </BtnInsert>
              <BtnInsert class="mt-2" color="error-200" rounded="ml" @click="openAddDialog">
                นำเข้าข้อมูล EXCEL
              </BtnInsert>
            </div>
          </VCardText>
          <AppDataTableAPI
            :total-records="totalRecords"
            :columns="listFields"
            :value="ListItem"
            :header-no="false"
            :row-class="checkSubNo"
            @page="onPageChange"
          >
            <!-- กำหนดสีของ Chip ในคอลัมน์สถานะ -->
            <template #isActive="slotProps">
              <ChipStatus
                :status="slotProps.data.isActive"
                :label="slotProps.data.isActive === 'ใช้งาน' ? 'ใช้งาน' : ''"
              />
            </template>

            <!-- ส่วน Icon การจัดการ -->
            <template #options="slotProps">
              <div class="text-center">
                <!-- ถ้ามี subWebpage ให้แสดงเฉพาะปุ่มดูข้อมูล -->
                <IconBtn
                  v-if="slotProps.data.subWebpage?.length > 0"
                  @click="openViewDialog(slotProps.data.webpageId)"
                >
                  <VIcon icon="mdi-eye-outline" />
                  <VTooltip open-delay="500" location="top" activator="parent">ดูข้อมูล</VTooltip>
                </IconBtn>

                <template v-else>
                  <IconBtn @click="openViewDialog(slotProps.data.webpageId)">
                    <VIcon icon="mdi-eye-outline" />
                    <VTooltip open-delay="500" location="top" activator="parent">ดูข้อมูล</VTooltip>
                  </IconBtn>
                  <IconBtn @click="openEditDialog(slotProps.data.webpageId)">
                    <VIcon icon="mdi-pencil-outline" />
                    <VTooltip open-delay="500" location="top" activator="parent">แก้ไข</VTooltip>
                  </IconBtn>
                  <IconBtn
                    v-if="slotProps.data.isActive === 'ไม่ใช้งาน'"
                    @click="ApproveDelete(slotProps.data.webpageId)"
                  >
                    <VIcon icon="mdi-delete-outline" />
                    <VTooltip open-delay="500" location="top" activator="parent">ลบข้อมูล</VTooltip>
                  </IconBtn>
                </template>
              </div>
            </template>

            <!-- แสดงข้อมูลหลักและ SubWebpage -->
            <template #body="slotProps">
              <tr :class="{ 'subwebpage-row': slotProps.data.parentNo }">
                <td v-for="field in listFields" :key="field.field">
                  <span v-if="field.field === 'isActive'">
                    <ChipStatus
                      :status="slotProps.data[field.field] === 'ใช้งาน'"
                      :label="slotProps.data[field.field]"
                    />
                  </span>
                  <span v-else-if="field.field === 'options'">
                    <div class="text-center">
                      <IconBtn @click="openViewDialog(slotProps.data.webpageId)">
                        <VIcon icon="mdi-eye-outline" />
                        <VTooltip open-delay="500" location="top" activator="parent">
                          ดูข้อมูล
                        </VTooltip>
                      </IconBtn>
                      <IconEdit @click="openEditDialog(slotProps.data.webpageId)" />
                      <IconDelete @Click="ApproveDelete(slotProps.data.webpageId)" />
                    </div>
                  </span>
                  <span v-else>
                    {{ slotProps.data[field.field] }}
                  </span>
                </td>
              </tr>
            </template>
          </AppDataTableAPI>
        </VCard>

        <DialogNavbarMenu
          v-model="isDialogVisible"
          :is-add="isAdd"
          :is-edit="isEdit"
          :is-view="isView"
          :view-id="editId"
          :edit-id="editId"
          @update="handleDialogUpdate"
        />
      </VCol>
    </VRow>
  </div>
</template>

<style scoped>
.custom-tabs .v-tab {
  text-transform: none;
}
.no-select {
  user-select: none;
}
</style>
