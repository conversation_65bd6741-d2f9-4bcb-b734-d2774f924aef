// import type { Event, NewEvent } from './types';
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'

const callAxios = useAxios()
const Swal = useSwal()

export const useCalendarStore = defineStore('calendar', {
  // arrow function recommended for full type inference
  state: () => ({
    availableCalendars: [
      { color: 'primary', label: 'Work' },
      { color: 'success', label: 'Holiday' }
    ],
    selectedCalendars: ['Work', 'Holiday']
  }),
  actions: {
    async fetchEvents() {
      try {
        const response = await callAxios.RequestGet('/Calendar/GetCalendarEvents')

        if (response.status === 200) {
          console.log('Events fetched from API:', response.data)

          return response.data.response
        } else {
          console.error('Failed to fetch events:', response)

          return []
        }
      } catch (error) {
        console.error('Error while fetching events:', error)
        Swal.callCatch()

        return []
      }
    }
  }
})
