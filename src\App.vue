<script setup lang="ts">
import { useTheme } from 'vuetify'
import { useAuth } from '@/store/useAuth'
import { useAxios } from '@/store/useAxios'
import { useThemeConfig } from '@core/composable/useThemeConfig'
import { hexToRgb } from '@layouts/utils'

const Auth = useAuth()
const callAxios = useAxios()
const baseUrlImg = localStorage.baseUrlImg

const {
  syncInitialLoaderTheme,
  syncVuetifyThemeWithTheme: syncConfigThemeWithVuetifyTheme,
  isAppRtl,
  handleSkinChanges
} = useThemeConfig()

const { global } = useTheme()

// ℹ️ Sync current theme with initial loader theme
// syncInitialLoaderTheme()
// syncConfigThemeWithVuetifyTheme()
// handleSkinChanges()

// GetSystemLogos();
// GetSystemLogosLanding();
// GetSystemBanner();
</script>

<template>
  <VLocaleProvider :rtl="isAppRtl">
    <!-- ℹ️ This is required to set the background color of active nav link based on currently active global theme's primary -->
    <VApp :style="`--v-global-theme-primary: ${hexToRgb(global.current.value.colors.primary)}`">
      <RouterView />
    </VApp>
  </VLocaleProvider>
</template>

<style>
.btnclose {
  background-color: #f96060 !important;
  corlor: black !important;
}
</style>
