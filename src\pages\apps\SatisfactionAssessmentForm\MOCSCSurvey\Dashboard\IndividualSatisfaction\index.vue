<script lang="ts" setup>
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'

const callAxios = useAxios()
const Swal = useSwal()
const router = useRouter()
interface BreadcrumbItem {
  title: string
  disabled: boolean
  to?: string
  active?: boolean
  activeClass?: string
}

const breadcrumbItems: Ref<BreadcrumbItem[]> = ref<BreadcrumbItem[]>([
  {
    title: 'แบบประเมินความพึงพอใจ',
    disabled: false
  },
  {
    title: 'MOCSC Survey',
    disabled: false
  },
  {
    title: 'แบบประเมินความพึงพอใจการให้บริการศูนย์บริการประชาชน',
    disabled: false
  },
  {
    title: 'รายงานสรุปความพึงพอใจรายบุคคล',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
])

interface Column {
  field: string
  header: string
  sortable: boolean
  style?: { textAlign: string }
}

const columns: Ref<Column[]> = ref<Column[]>([
  {
    field: 'name',
    header: 'รายการ',
    sortable: false
  },
  {
    field: 'number',
    header: 'จำนวน',
    sortable: false,
    style: { textAlign: 'center' }
  },
  {
    field: 'percent',
    header: 'เปอร์เซ็น',
    sortable: false,
    style: { textAlign: 'center' }
  }
])

interface dataDashboard {
  datasTable: any[]
  datas: {
    header: string
    byPercent: Object
  }
  summary: string
  summaryPercent: string
}

const dataDashboard: Ref<dataDashboard> = ref<dataDashboard>({
  datasTable: [],
  datas: {
    header: '',
    byPercent: {}
  },
  summary: '0',
  summaryPercent: '0'
})

interface recommendServiceData {
  answerName: string
  createdDateTime: Date
}

const recommendServiceData: Ref<recommendServiceData[]> = ref<recommendServiceData[]>([])

const GetReportSurveyCommentByPersonal = async (id = 0) => {
  if (id !== null) {
    callAxios
      .RequestGet(
        `/ReportGECC/GetReportSurveyCommentByPersonal?PersonalInfoId=${id}&StartDate=${searchDate.value[0]}&EndDate=${searchDate.value[1]}&EvaluationSystemId=2`
      )
      .then(response => {
        if (response.status == 200) {
          recommendServiceData.value = response.data.response
          isLoad.value = false
          GetReportSurveyByPersonal(id)
        }
      })
  } else {
    Swal.isNotFound()
  }
}

const GetReportSurveyByPersonal = async (id = 0) => {
  if (id !== null) {
    callAxios
      .RequestGet(
        `/ReportGECC/GetReportSurveyByPersonal?PersonalInfoId=${id}&StartDate=${searchDate.value[0] ? searchDate.value[0] : null}&EndDate=${searchDate.value[1] ? searchDate.value[1] : null}&EvaluationSystemId=2`
      )
      .then(response => {
        if (response.status == 200) {
          dataDashboard.value = response.data.response
          footer.value[0].columns[1].footer = dataDashboard.value.summary
          footer.value[0].columns[2].footer = dataDashboard.value.summaryPercent
          isLoad.value = false
          CheckData()
        }
      })
  } else {
    Swal.isNotFound()
  }
}

interface FooterColumn {
  footer: string
  style: { textAlign: string }
}

interface FooterItem {
  columns: FooterColumn[]
}

const footer: Ref<FooterItem[]> = ref<FooterItem[]>([
  {
    columns: [
      {
        footer: 'จำนวนรวม',
        style: { textAlign: 'center' }
      },
      {
        footer: '0',
        style: { textAlign: 'center' }
      },
      {
        footer: '0',
        style: { textAlign: 'center' }
      }
    ]
  }
])

const columns2: Ref<Column[]> = ref<Column[]>([
  {
    field: 'answerName',
    header: 'ข้อเสนอแนะในการให้บริการ',
    sortable: false
  },
  {
    field: 'createdDateTime',
    header: 'วันที่ตอบแบบสอบถาม',
    sortable: false,
    style: { textAlign: 'center' }
  }
])

const searchDate: Ref<any> = ref<any>([])
const isLoad: Ref<boolean> = ref<boolean>(true)

const listPersonalByOrgStructureIdServiceId: Ref<any[]> = ref<any[]>([])
const searchName: Ref<number | null> = ref<number | null>(null)

const GetPersonalByOrgStructureIdServiceId = (org = 0, division = 0) => {
  callAxios
    .RequestGet(
      `/ReportGECC/GetPersonalByOrgStructureIdServiceIdMocscSurvey?OrgStructureId=${org}&DivisionId=${division}`
    )
    .then(response => {
      if (response.status == 200)
        listPersonalByOrgStructureIdServiceId.value = response.data.response
    })
}

const submitFilter = async (navigator = 0) => {
  if (searchName.value == null) {
    Swal.isRequestLost('กรุณาระบุชื่อ - นามสกุล')
  } else if (
    searchDate.value == null ||
    searchDate.value[0] == null ||
    searchDate.value[1] == null
  ) {
    Swal.isRequestLost('กรุณาระบุวันที่เริ่มต้น - สิ้นสุด')
  } else {
    if (navigator == 1) GetReportSurveyCommentByPersonal(searchName.value)
    else if (navigator == 2) GetDashboard()
  }
}

const CheckData = async () => {
  if (
    Object.keys(recommendServiceData.value).length === 0 &&
    Object.keys(dataDashboard.value).length === 0
  )
    Swal.AddConditionFailText('ไม่พบข้อมูลของวันที่กำหนด')
}

const GetDashboard = () => {
  window.open(
    `/apps/satisfactionassessmentform/mocscsurvey/dashboard/IndividualSatisfaction/print?date=${searchDate.value}&name=${searchName.value}`,
    '_blank'
  )
}

const setAttribute: Object = computed(() => ({
  dataDashboard: dataDashboard.value,
  columns: columns.value,
  columns2: columns2.value,
  footer: footer.value,
  recommendServiceData: recommendServiceData.value
}))

const back = () => {
  router.go(-1)
}

onMounted(() => {
  GetPersonalByOrgStructureIdServiceId()
})
</script>

<template>
  <section>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <VRow class="mb-2" align="center">
      <VCol cols="12" class="d-flex flex-wrap">
        <div class="demo-space-x">
          <VBtn prepend-icon="mdi-arrow-left-circle" variant="text" color="grey-900" @click="back">
            ย้อนกลับ
          </VBtn>
        </div>
      </VCol>
    </VRow>
    <VCard>
      <VCardText>
        <VRow align="center">
          <VCol cols="12" md="2">
            <label>วันที่เริ่มต้น - สิ้นสุด :</label>
          </VCol>
          <VCol cols="12" sm="4">
            <DateTimePicker v-model="searchDate" range />
          </VCol>
          <VCol cols="12" md="2">
            <label>ชื่อ - นามสกุล :</label>
          </VCol>
          <VCol cols="12" sm="4">
            <VAutocomplete
              v-model="searchName"
              item-title="fullName"
              item-value="personalInfoId"
              density="default"
              no-data-text="ไม่มีข้อมูล"
              :items="listPersonalByOrgStructureIdServiceId"
            />
          </VCol>
        </VRow>
        <VRow>
          <VCol cols="12">
            <div class="d-flex flex-wrap gap-4 justify-center">
              <VBtn prepend-icon="mdi-file-document" color="success-200" @click="submitFilter(2)">
                ออกรายงาน
              </VBtn>
              <VBtn prepend-icon="mdi-search" color="primary" @click="submitFilter(1)">ค้นหา</VBtn>
            </div>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
    <IndividualSatisfaction v-if="!isLoad" class="mt-5" v-bind="setAttribute" />
  </section>
</template>
