<script setup lang="ts">
import { useRouter } from 'vue-router'
import { VCardText, VCol } from 'vuetify/lib/components/index.mjs'

import Tab1 from './edit/tab1/view.vue'
import Tab2 from './edit/tab2/view.vue'
import Tab3 from './edit/tab3/view.vue'
import Tab4 from './edit/tab4/view.vue'
import { useSwal } from '@/store/useSwal'
import { useAxios } from '@/store/useAxios'

const props = defineProps<{
  modelValue: boolean
  isAdd: boolean
  isEdit: boolean
  isView: boolean
  viewId?: string
  editId?: string
}>()

const emit = defineEmits(['update:modelValue', 'update'])

// import { VBtn, VCard, VCardTitle } from "vuetify/lib/components/index.mjs";

const router = useRouter()
const callAxios = useAxios()
const swal = useSwal()

const isDialogVisible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value)
})

const numberedSteps = [
  {
    title: 'เกณฑ์ด้านกายภาพ'
  },
  {
    title: 'เกณฑ์ด้านคุณภาพ'
  },
  {
    title: 'เกณฑ์ด้านผลลัพธ์'
  },
  {
    title: 'แบบประเมินความพร้อม'
  }
]

const listName = ref([
  'ชื่อศูนย์',
  'สถานที่ตั้ง',
  'จังหวัด',
  'เขต/อำเภอ',
  'แขวง/ตำบล',
  'รหัสไปรษณีย์'
])

const listcordinationmain = ref([
  'ชื่อ',
  'ตำแหน่ง',
  'เบอร์โทรศัพท์1',
  'เบอร์โทรศัพท์2',
  'เบอร์โทรสาร',
  'เบอร์มือถือ',
  'อีเมล',
  'สำนัก/กอง',
  'ไลน์ไอดี'
])

const listcordinationsecond = ref([
  'ชื่อ',
  'ตำแหน่ง',
  'เบอร์โทรศัพท์1',
  'เบอร์โทรศัพท์2',
  'เบอร์โทรสาร',
  'เบอร์มือถือ',
  'อีเมล',
  'สำนัก/กอง',
  'ไลน์ไอดี'
])

const currentStep = ref(0)
</script>

<template>
  <VDialog v-model="isDialogVisible" fullscreen transition="dialog-bottom-transition">
    <VCard class="d-flex flex-column h-100">
      <!--
        <VCardTitle class="d-flex justify-center align-center py-6">
        บันทึกผลการตรวจประเมิน
        <DialogCloseBtn
        class=""
        variant="text"
        size="small"
        @click="isDialogVisible = false"
        />
        </VCardTitle>
      -->

      <VCardText class="overflow-y-auto flex-grow-1 px-5">
        <VCard class="d-flex align-item-center justify-center mb-4">
          <img src="@images/evaluation05.png" class="image-size my-2 mx-2" />
          <VCardText class="text-h6">ตรวจคัดกรอง ใบสมัครศูนย์ราชการสะดวก เลขที่ใบสมัคร</VCardText>
          <VCardTitle class="d-flex align-center flex-wrap gap-4">
            <div class="">
              <VBtn rounded="xl" class="mt-2" prepend-icon="mdi-auto-fix" color="error-300">
                ตรวจประเมินอัตโนมัติ
              </VBtn>
            </div>
          </VCardTitle>
        </VCard>

        <VCard class="">
          <VRow class="d-flex align-items-center my-2">
            <img
              class="ms-5"
              src="@images/evaluation01.png"
              style="width: 50px; height: 50px; margin-right: 8px"
            />
            <VCardTitle class="text-h6">ข้อมูลหน่วยงาน</VCardTitle>
          </VRow>
          <VRow class="mx-n1 mb-4">
            <VCol md="4">
              <VCard class="mb-2">
                <div class="d-flex align-item-center ps-2 py-2">
                  <img
                    src="@images/evaluation02.png"
                    style="width: 40px; height: 40px; margin-right: 8px"
                  />
                  <VCardTitle class="text-h6">ศูนย์ราชการสะดวก</VCardTitle>
                </div>
                <VCardText v-for="(item, index) in listName" :key="index">{{ item }} :</VCardText>
              </VCard>
            </VCol>
            <VCol md="4">
              <VCard>
                <div class="d-flex align-item-center ps-2 py-2">
                  <img
                    src="@images/evaluation03.png"
                    style="width: 40px; height: 40px; margin-right: 8px"
                  />
                  <VCardTitle class="text-h6">ผู้ประสานงานหลัก</VCardTitle>
                </div>
                <VCardText v-for="(item, index) in listcordinationmain" :key="index">
                  {{ item }} :
                </VCardText>
              </VCard>
            </VCol>
            <VCol md="4">
              <VCard>
                <div class="d-flex align-item-center ps-2 py-2">
                  <img src="@images/evaluation04.png" style="width: 50px; height: 50px" />
                  <VCardTitle class="text-h6">ศูนย์ประสานงานสำรอง</VCardTitle>
                </div>
                <VCardText v-for="(item, index) in listcordinationsecond" :key="index">
                  {{ item }} :
                </VCardText>
              </VCard>
            </VCol>
          </VRow>
        </VCard>

        <div class="d-flex align-center justify-center my-8">
          <AppStepper v-model:current-step="currentStep" :items="numberedSteps" />
          <div class="text-center text-h6 ms-10 mb-6">
            คะแนนรวม
            <VCard class="align-item-center px-10 py-2 border border-6" color="grey-200">
              <VCardText class="text-h6">65</VCardText>
            </VCard>
          </div>
        </div>

        <VRow>
          <VCol cols="12" md="12" lg="12">
            <VWindow v-model="currentStep" class="disable-tab-transition">
              <VWindowItem>
                <Tab1 />
              </VWindowItem>

              <VWindowItem>
                <Tab2 />
              </VWindowItem>

              <VWindowItem>
                <Tab3 />
              </VWindowItem>

              <VWindowItem>
                <Tab4 />
              </VWindowItem>
            </VWindow>
          </VCol>
        </VRow>

        <VRow>
          <VCol class="mt-10 d-flex justify-end mr-5">
            <VBtn
              rounded="xl"
              class="mt-2 mr-2 mb-3"
              prepend-icon="mdi-content-save"
              color="grey-300"
            >
              บันทึกร่าง
            </VBtn>
            <VBtn rounded="xl" class="mt-2 mb-5" prepend-icon="mdi-content-save" color="blue-600">
              บันทึกผลการประเมิน
            </VBtn>
            <VBtn
              rounded="xl"
              class="mb-5 mt-2 mx-2"
              prepend-icon="mdi-close-circle"
              color="error-300"
              @click="isDialogVisible = false"
            >
              ยกเลิก
            </VBtn>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style lang="scss">
.image-size {
  width: 50px;
  height: 50px;
}
</style>
