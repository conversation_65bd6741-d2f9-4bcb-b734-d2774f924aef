<script setup>
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import { defineProps, onMounted, ref, watch } from 'vue'

const props = defineProps({
  contentId: {
    type: String,
    required: true
  }
})

const callAxios = useAxios()
const Swal = useSwal()
const contentDetail = ref(null) // Initialize as null
const loading = ref(true) // Loading state

const GetContentDetail = async () => {
  try {
    if (!props.contentId) return
    loading.value = true // Start loading
    const response = await callAxios.RequestGet(
      `/OtherMaster/GetContentLandingPageById?ContentId=${props.contentId}`
    )
    if (response.status === 200) {
      contentDetail.value = response.data.response
    }
  } catch (error) {
    console.error('Error fetching content data:', error)
    Swal.callCatch()
  } finally {
    loading.value = false // Stop loading
  }
}

onMounted(() => {
  GetContentDetail()
})

watch(
  () => props.contentId,
  newContentId => {
    if (newContentId) {
      GetContentDetail()
    }
  }
)
</script>

<template>
  <div class="page-content detail-section">
    <CommonBanner
      :title="contentDetail?.contentName || 'Loading...'"
      :title2="contentDetail?.contentName || 'Loading...'"
    />

    <section class="content-inner-2">
      <div class="container">
        <div class="mt-5">
          <VCard class="container-lg mb-5">
            <template v-if="loading">
              <VCardText>
                <div class="text-center">
                  <VProgressCircular :size="60" color="primary" indeterminate />
                </div>
              </VCardText>
            </template>
            <template v-else>
              <VCardTitle>{{ contentDetail.contentName }}</VCardTitle>
              <div class="mx-6">
                <VCardText v-html="contentDetail.contents" />
              </div>
            </template>
          </VCard>
        </div>
      </div>
      <GovWebsiteAccess />
    </section>
  </div>
</template>

<style lang="scss">
.content-inner-2 {
  position: relative;
  padding-block: 50px;
  padding-inline: 0;
}

@media (min-width: 992px) {
  .content-inner-2 {
    padding-block: 100px;
    padding-inline: 0;
  }
}

.pdf-container {
  position: relative;
  overflow: hidden;
  background-color: #f9f9f9; /* Placeholder background */
  padding-block-start: 56.25%; /* 16:9 Aspect Ratio */
}

.responsive-iframe {
  position: absolute;
  border: none;
  block-size: 100%;
  inline-size: 100%;
  inset-block-start: 0;
  inset-inline-start: 0;
}

.text-center {
  color: #888;
  font-size: 1.2rem;
  text-align: center;
}
</style>
