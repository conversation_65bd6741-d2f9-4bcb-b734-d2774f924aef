<script setup>
const { modelValue, fields, buttonCols, classAttr } = defineProps({
  modelValue: [Object],
  fields: [Array],
  buttonCols: {
    type: Object,
    default: {
      xl: 1,
      lg: 1,
      md: 2,
      sm: 12,
      cols: 12
    }
  },
  classAttr: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'submit', 'reportButtonClick'])
const show = ref(true)

const _buttonCols = computed(() => ({
  xl: buttonCols.xl || 1,
  lg: buttonCols.lg || 1,
  md: buttonCols.md || 2,
  sm: buttonCols.sm || 12,
  cols: buttonCols.cols || 12
}))

const fieldsDefault = reactive([
  {
    name: 'searchQuery',
    type: 'text',
    label: 'ค้นหา',
    default: '',
    rules: [v => !!v || 'This field is required']
  }
])

const initialValues = modelValue ? ref(unref(modelValue)) : {}
const elementFields = fields || fieldsDefault
const flattenFields = fields?.flat() || fieldsDefault.flat()

for (const field of flattenFields) {
  if (field.type === 'number') {
    initialValues[field.name] = field.default || 0
  } else if (field.type === 'date') {
    initialValues[field.name] = field.default || new Date()
  } else if (field.type === 'dateRange') {
    initialValues[field.name] = field.default || [new Date(), new Date()]
  } else if (field.type === 'select') {
    initialValues[field.name] =
      field.default || (field.items && field.items.length ? field.items[0].id : null)
  } else if (field.type === 'text') {
    initialValues[field.name] = field.default || ''
  }
}

for (const field of flattenFields) {
  switch (field.type) {
    case 'number':
      initialValues[field.name] = field.default || 0
      break
    case 'date':
      initialValues[field.name] = field.default || new Date()
      break
    case 'dateRange':
      initialValues[field.name] = field.default || [new Date(), new Date()]
      break
    case 'select':
      initialValues[field.name] =
        field.default || (field.items && field.items.length ? field.items[0].id : null)
      break
    case 'text':
      initialValues[field.name] = field.default || ''
      break
    case 'radio':
      initialValues[field.name] = field.default || ''
      break
    default:
      initialValues[field.name] = field.default || ''
      break
  }
}

const fieldValues = ref(initialValues)

watch(fieldValues.value, () => {
  console.log('Value is changed!')
  emit('update:modelValue', fieldValues.value)
})

const onFormSubmit = () => {
  emit('submit', fieldValues.value)
  console.log(fieldValues.value)
}

const onReportButtonClick = buttonName => {
  emit('reportButtonClick', buttonName)
}

const getErrorMessages = fieldName => {
  const field = elementFields.find(f => f.name === fieldName)
  if (field && field.rules) {
    const value = fieldValues.value[fieldName]

    return field.rules.map(rule => rule(value)).filter(message => typeof message === 'string')
  }

  return []
}
</script>

<template>
  <VCard class="mb-6 mt-5">
    <VCardTitle @click="show = !show">
      <VCardActions class="pb-0">
        <VSpacer>ค้นหาข้อมูล</VSpacer>
        <VBtn :icon="show ? 'mdi-chevron-up' : 'mdi-chevron-down'" class="collapse-arrow" />
      </VCardActions>
    </VCardTitle>

    <VExpandTransition class="px-10">
      <div v-show="show">
        <VCardText class="pb-0">
          <VRow class="align-center justify-center mb-7 py-0">
            <!-- Render Form Fields -->
            <template v-for="(field, i) in elementFields" :key="i">
              <!-- Radio Fields -->
              <template v-if="field.type === 'radio'">
                <VCol cols="12">
                  <label v-if="field.label">{{ field.label }}:</label>
                  <VRadioGroup v-model="fieldValues[field.name]" row :rules="field.rules">
                    <VRow class="d-flex flex-row justify-start">
                      <template v-for="(option, idx) in field.items" :key="idx">
                        <VCol cols="auto">
                          <VRadio
                            :value="option.value"
                            :label="option.label"
                            color="primary"
                            class="me-4"
                          />
                        </VCol>
                      </template>
                    </VRow>
                  </VRadioGroup>
                </VCol>
              </template>
              <!-- Text Fields -->
              <template v-if="field.type === 'text'">
                <VCol cols="12" md="6" sm="6">
                  <label>{{ field.label }} :</label>
                  <VTextField
                    v-model="fieldValues[field.name]"
                    :rules="field.rules"
                    :error-messages="getErrorMessages(field.name)"
                    density="default"
                    :placeholder="field.placeholder"
                    class="input-margin-top"
                  />
                </VCol>
              </template>

              <template v-if="field.type === 'textNumber'">
                <VCol cols="12" md="6" sm="6">
                  <label>{{ field.label }} :</label>
                  <VTextField
                    v-model="fieldValues[field.name]"
                    :rules="field.rules"
                    :error-messages="getErrorMessages(field.name)"
                    density="default"
                    :placeholder="field.placeholder"
                    class="input-margin-top"
                    @input="e => (fieldValues[field.name] = e.target.value.replace(/\D/g, ''))"
                  />
                </VCol>
              </template>

              <!-- Select Fields -->
              <template v-if="field.type === 'select'">
                <VCol cols="12" md="6" sm="6">
                  <label>
                    {{ field.label }}:
                    <span v-if="field.required" class="text-error">*</span>
                  </label>
                  <VAutocomplete
                    v-model="fieldValues[field.name]"
                    :item-title="field.title"
                    :item-value="field.value"
                    :rules="field.rules"
                    :persistent-placeholder="true"
                    :placeholder="field.placeholder"
                    :items="field.items"
                    no-data-text="ไม่มีข้อมูล"
                    density="default"
                    :error-messages="getErrorMessages(field.name)"
                    class="input-margin-top"
                    @update:modelValue="val => $emit('change', { name: field.name, value: val })"
                  />
                </VCol>
              </template>

              <!-- Date Fields -->
              <template v-if="field.type === 'date'">
                <VCol cols="12" md="6" sm="6">
                  <label>
                    {{ field.label }}:
                    <span v-if="field.required" class="text-error">*</span>
                  </label>
                  <DateTimePicker
                    v-model="fieldValues[field.name]"
                    :rules="field.rules"
                    :error-messages="getErrorMessages(field.name)"
                    class="input-margin-top"
                  />
                </VCol>
              </template>

              <!-- Date Range Fields -->
              <template v-if="field.type === 'dateRange'">
                <VCol cols="12" md="6" sm="6">
                  <label>
                    {{ field.label }}:
                    <span v-if="field.required" class="text-error">*</span>
                  </label>
                  <div class="input-margin-bottom" />
                  <DateTimePicker
                    v-model="fieldValues[field.name]"
                    range
                    :rules="field.rules"
                    :error-messages="getErrorMessages(field.name)"
                    class="input-margin-top"
                  />
                </VCol>
              </template>

              <!-- Year Picker Fields -->
              <template v-if="field.type === 'dateYear'">
                <VCol cols="12" md="6" sm="6">
                  <label>
                    {{ field.label }}:
                    <span v-if="field.required" class="text-error">*</span>
                  </label>
                  <div class="input-margin-bottom" />
                  <DateTimePicker
                    v-model="fieldValues[field.name]"
                    format="YYYY"
                    year-picker
                    class="px-2 my-2 input-margin-top"
                    :rules="field.rules"
                    :error-messages="getErrorMessages(field.name)"
                  />
                </VCol>
              </template>

              <template v-if="field.type === 'dateYearRange'">
                <VCol cols="12" md="6" sm="6">
                  <label>
                    {{ field.label }}:
                    <span v-if="field.required" class="text-error">*</span>
                  </label>
                  <div class="input-margin-bottom" />
                  <DateTimePicker
                    v-model="fieldValues[field.name]"
                    format="YYYY"
                    year-picker
                    range
                    :rules="field.rules"
                    :error-messages="getErrorMessages(field.name)"
                  />
                </VCol>
              </template>

              <template v-if="field.type === 'reportButton'">
                <VCol cols="12" md="3" sm="4">
                  <div class="input-margin-bottom">
                    <VBtn
                      rounded="xl"
                      prepend-icon="mdi-note-text"
                      color="yellow-50"
                      @click="onReportButtonClick(field.name)"
                    >
                      {{ field.label }}
                    </VBtn>
                  </div>
                </VCol>
              </template>

              <template v-if="field.type === 'emptyCol'">
                <VCol cols="12" md="6" sm="6">
                  <label>
                    {{ field.label }}
                    <small v-if="field.required" class="text-error">*</small>
                  </label>
                </VCol>
              </template>
            </template>

            <!-- Submit Button -->
            <template v-if="!elementFields.some(f => f.type === 'reportButton')">
              <VCol cols="12" md="12" class="d-flex flex-wrap align-center justify-center py-0">
                <VBtn
                  class="mt-2 custom-btn"
                  prepend-icon="mdi-magnify"
                  color="btn-search"
                  rounded="xs"
                  @click="onFormSubmit"
                >
                  ค้นหา
                </VBtn>
              </VCol>
            </template>
          </VRow>
        </VCardText>
      </div>
    </VExpandTransition>
  </VCard>
</template>

<style scoped>
.custom-btn {
  color: white !important;
}

.input-margin-top {
  margin-block-start: 5px;
}

.input-margin-bottom {
  margin-block-end: 5px;
}

.collapse-arrow {
  color: inherit !important;
}
</style>
