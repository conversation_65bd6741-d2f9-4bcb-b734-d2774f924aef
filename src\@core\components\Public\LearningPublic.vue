<script setup>
import { ref } from 'vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Autoplay, Pagination } from 'swiper/modules'
import { useAxios } from '@/store/useAxios'
const callAxios = useAxios()

const slider = ref([])
const GetEventByType = () => {
  callAxios.RequestGet('/OtherMaster/GetEventByType?TypeId=3').then(response => {
    if (response.status == 200) {
      slider.value = response.data.response
    }
  })
}
const setAttribute = ref({
  modules: [Autoplay, Pagination],
  navigation: {
    prevEl: '.prev',
    nextEl: '.next'
  },
  slidesPerView: 4,
  slidesPerGroup: 3,
  spaceBetween: 30,
  loop: true,
  speed: 3000,
  breakpoints: {
    1200: {
      slidesPerView: 4,
      slidesPerGroup: 3
    },
    775: {
      slidesPerView: 3,
      slidesPerGroup: 2
    },
    200: {
      slidesPerView: 1,
      slidesPerGroup: 1
    }
  },
  autoplay: {
    delay: 1500
  },
  pagination: {
    el: '.owl-dots',
    clickable: true
  }
})

onMounted(() => {
  GetEventByType()
})
</script>

<template>
  <section class="content-inner-1 bgl-primary">
    <div class="container">
      <div class="section-head style-1 text-center">
        <h6 class="sub-title">e-Learning</h6>
        <h2 class="title">หลักสูตรแนะนำ</h2>
      </div>
      <div class="row">
        <div class="col-lg-12">
          <Swiper
            class="team-carousel1 owl owl-carousel owl-none owl-theme owl-dots-primary-full"
            v-bind="setAttribute"
          >
            <SwiperSlide class="item wow fadeInUp" v-for="(item, ind) in slider" :key="ind">
              <div class="dlab-team style-1 m-b20">
                <div class="dlab-media">
                  <a :href="item.url" target="_blank">
                    <img
                      :src="item.logoThumbnail"
                      class="object-fit-contain"
                      style="height: 250px"
                    />
                  </a>
                </div>
                <div class="dlab-content">
                  <div class="clearfix">
                    <h5 class="dlab-name" style="height: 80px">
                      <a :href="item.url" target="_blank">{{ item.name }}</a>
                    </h5>
                    <span class="dlab-position text-truncate-3">{{ item.description }}</span>
                  </div>
                  <!-- <ul class="dlab-social-icon primary-light">
                                        <li><a class="fab fa-facebook-f" href="https://www.facebook.com/"
                                                target="_blank"></a></li>
                                        <li><a class="fab fa-youtube" href="https://www.instagram.com/" target="_blank"></a>
                                        </li>
                                        <li><a class="fab fa-twitter" href="https://www.twitter.com/" target="_blank"></a>
                                        </li>
                                    </ul> -->
                </div>
              </div>
            </SwiperSlide>
          </Swiper>
          <div class="owl-dots" style="text-align: center"></div>
        </div>
      </div>
    </div>
  </section>
</template>
<style>
.owl-dots > span {
  transition: all 0.4s;
}

.owl-dots > .swiper-pagination-bullet-active {
  width: 30px;
  border-radius: 10px;
  transition: all 0.4s;
}
.text-truncate-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3; /* Number of lines to show */
  -webkit-box-orient: vertical;
}
</style>
