<script setup>
import { useGenerateImageVariant } from '@/@core/composable/useGenerateImageVariant'
import pages500 from '@images/pages/500.png'
import miscObj from '@images/pages/misc-500-object.png'
import miscMaskDark from '@images/pages/misc-mask-dark.png'
import miscMaskLight from '@images/pages/misc-mask-light.png'

const miscThemeMask = useGenerateImageVariant(miscMaskLight, miscMaskDark)
</script>

<template>
  <div class="misc-wrapper">
    <div class="misc-center-content text-center mb-4">
      <!-- 👉 Title and subtitle -->
      <h1 class="text-h3 font-weight-medium">ขอขอบคุณ</h1>
      <h5 class="text-h5 font-weight-medium mb-3">ผู้ร่วมตอบแบบสอบถาม</h5>
    </div>

    <!-- 👉 Image -->
    <div class="misc-avatar w-100 text-center">
      <VImg
        :src="pages500"
        alt="Coming Soon"
        :height="$vuetify.display.xs ? 400 : 500"
        class="my-sm-4"
      />

      <VImg :src="miscThemeMask" class="d-none d-md-block footer-coming-soon" cover />

      <VImg
        :src="miscObj"
        class="d-none d-md-block footer-coming-soon-obj"
        :max-width="174"
        height="158"
      />
    </div>
  </div>
</template>

<style lang="scss">
@use '@core/scss/template/pages/misc.scss';
</style>

<route lang="yaml">
meta:
  layout: blank
</route>
