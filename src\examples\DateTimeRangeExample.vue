<template>
  <VContainer>
    <VRow>
      <VCol cols="12">
        <h2>DateTime Range Picker Example</h2>
        <p>This example demonstrates the datetime range functionality of the DateTimePicker component.</p>
      </VCol>
    </VRow>

    <VRow>
      <VCol cols="12" md="6">
        <VCard>
          <VCardTitle>Basic DateTime Range</VCardTitle>
          <VCardText>
            <DateTimePicker
              v-model="datetimeRange"
              datetime-range
              placeholder="เลือกช่วงวันที่และเวลา"
              label="DateTime Range"
            />
            <div class="mt-4">
              <strong>Selected Range:</strong>
              <pre>{{ JSON.stringify(datetimeRange, null, 2) }}</pre>
            </div>
          </VCardText>
        </VCard>
      </VCol>

      <VCol cols="12" md="6">
        <VCard>
          <VCardTitle>DateTime Range with Icon Style</VCardTitle>
          <VCardText>
            <DateTimePicker
              v-model="datetimeRangeIcon"
              datetime-range
              icon-style-flag
              placeholder="เลือกช่วงวันที่และเวลา"
              label="DateTime Range (Icon Style)"
            />
            <div class="mt-4">
              <strong>Selected Range:</strong>
              <pre>{{ JSON.stringify(datetimeRangeIcon, null, 2) }}</pre>
            </div>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <VRow>
      <VCol cols="12">
        <VCard>
          <VCardTitle>DateTime Range with Disabled Dates</VCardTitle>
          <VCardText>
            <DateTimePicker
              v-model="datetimeRangeDisabled"
              datetime-range
              :disabled-dates="disabledDates"
              placeholder="เลือกช่วงวันที่และเวลา (มีวันที่ถูกปิดใช้งาน)"
              label="DateTime Range with Disabled Dates"
            />
            <div class="mt-4">
              <strong>Selected Range:</strong>
              <pre>{{ JSON.stringify(datetimeRangeDisabled, null, 2) }}</pre>
            </div>
            <div class="mt-2">
              <small class="text-muted">
                Disabled dates: {{ disabledDates.map(d => d.toLocaleDateString('th-TH')).join(', ') }}
              </small>
            </div>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>
  </VContainer>
</template>

<script setup>
import DateTimePicker from '@/@core/components/DateTimePicker.vue'

// Reactive data
const datetimeRange = ref(null)
const datetimeRangeIcon = ref(null)
const datetimeRangeDisabled = ref(null)

// Example disabled dates (today + 3 days, today + 7 days)
const today = new Date()
const disabledDates = [
  new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000), // 3 days from today
  new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)  // 7 days from today
]

// Watch for changes
watch(datetimeRange, (newVal) => {
  console.log('DateTime Range changed:', newVal)
})

watch(datetimeRangeIcon, (newVal) => {
  console.log('DateTime Range (Icon) changed:', newVal)
})

watch(datetimeRangeDisabled, (newVal) => {
  console.log('DateTime Range (Disabled) changed:', newVal)
})
</script>

<style scoped>
pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.v-theme--dark pre {
  background-color: #2d2d2d;
  color: #fff;
}
</style>
