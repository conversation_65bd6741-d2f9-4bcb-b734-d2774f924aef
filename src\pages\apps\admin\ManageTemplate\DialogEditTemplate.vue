<script lang="ts" setup>
import { computed, reactive, ref } from 'vue'
import { useAuth } from '@/store/useAuth'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import type { FormTemplate } from '@interfaces/FormInterface'
import { booleanValidator } from '@validators'

// Props and Emits
const props = defineProps({
  modelValue: Boolean,
  isAdd: Boolean,
  isEdit: Boolean,
  viewId: String,
  editId: String
})

const emit = defineEmits(['update:modelValue', 'update'])

const callAxios = useAxios()
const Swal = useSwal()
const refVForm: Ref<any> = ref(null)
const editId = computed(() => props.editId)
const temporaryTemplateColor = ref<string | null>(null)
const Auth = useAuth()

const form = reactive<FormTemplate>({
  imageFiles: null as File | null,
  id: '',
  templateId: '',
  name: '',
  isActive: false,
  isDefault: false,
  color: '',
  pathName: '',
  templateName: '',
  templateColor: ''
})

const setTitle = computed(() => {
  return 'แก้ไขรายการ'
})

const listStatus = ref([
  { id: true, name: 'ใช้งาน' },
  { id: false, name: 'ไม่ใช้งาน' }
])

const listDefault = ref([
  { id: true, name: 'ค่าเริ่มต้น' },
  { id: false, name: 'ไม่ใช่ค่าเริ่มต้น' }
])

const createFormData = () => {
  const formData = new FormData()

  if (form.templateId) formData.append('id', form.templateId)

  if (form.imageFiles instanceof File) formData.append('files', form.imageFiles)

  formData.append('name', form.templateName || '')
  formData.append('color', String(form.color || ''))
  formData.append('isActive', String(form.isActive))
  formData.append('isDefault', String(form.isDefault))

  return formData
}

const formDefault = structuredClone(toRaw(form))

const resetForm = () => {
  Object.assign(form, structuredClone(formDefault))
}

const closeDialog = async () => {
  const response = await Swal.ApproveCancel()
  if (response) {
    resetForm()
    isDialogVisible.value = false
  }
}

const onFormSubmit = async () => {
  const confirmed = await Swal.ApproveConfirm()
  if (!confirmed) return

  try {
    if (!refVForm.value) return
    const { valid } = await refVForm.value.validate()

    if (valid) {
      applyTemplateColor()
      if (props.isEdit) await EditForm()
      else throw new Error('Invalid state')
    }
  } catch (error) {
    Swal.AddConditionFailText(error.data.message)
  }
}

const FetchTemplateData = async () => {
  try {
    const response = await callAxios.RequestGetById(
      '/UserManagement/GetSystemTemplateById',
      `?TemplateId=${props.editId}`
    )

    if (response.status === 200) {
      const templateData = response.data.response

      form.templateId = templateData.templateId
      form.templateName = templateData.templateName
      form.isActive = templateData.isActive
      form.isDefault = templateData.isDefault
      form.templateColor = templateData.templateColor
    } else {
      Swal.ViewFail()
    }
  } catch (error) {
    Swal.ViewFail()
  }
}

const EditForm = async () => {
  const formData = createFormData()
  try {
    const response = await callAxios.RequestPutUpload(
      `/UserManagement/UpdateSystemTemplate?TemplateId=${editId.value}`,
      formData
    )

    if (response.status === 200) {
      resetForm()
      isDialogVisible.value = false
      emit('update')
    } else {
      Swal.EditConditionFail()
    }
  } catch (error) {
    Swal.AddConditionFailText(error.data.message)
  }
}

const isDialogVisible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value)
})

const applyTemplateColor = () => {
  if (!temporaryTemplateColor.value) {
    console.error('Temporary template color is missing.')

    return
  }

  Auth.setTemplateColor(temporaryTemplateColor.value)
  console.log('Template color applied via Auth store:', temporaryTemplateColor.value)
}

const loadTemplateColorFromLocalStorage = () => {
  const savedColor = localStorage.getItem('selectedTemplateColor')
  if (savedColor) {
    temporaryTemplateColor.value = savedColor
    applyTemplateColor()
    console.log('Template Color Loaded from Local Storage:', savedColor)
  }
}

watch(
  () => form.isDefault,
  newValue => {
    if (newValue) {
      temporaryTemplateColor.value = form.templateColor
      console.log('Temporary template color set:', temporaryTemplateColor.value)
    }
  }
)

watch(
  () => isDialogVisible.value,
  async newVal => {
    if (newVal && props.isEdit && props.editId) await FetchTemplateData()
  }
)

onMounted(() => {
  loadTemplateColorFromLocalStorage()
})
</script>

<template>
  <VDialog
    v-if="props.isEdit"
    v-model="isDialogVisible"
    persistent
    class="v-dialog-sm"
    no-click-animation
    scrollable
    :class="`color-theme-${form.templateColor}`"
  >
    <VCard :title="setTitle">
      <DialogCloseBtn variant="text" size="small" @click="closeDialog" />

      <VCardText>
        <VForm ref="refVForm" @submit.prevent="onFormSubmit">
          <VRow class="mb-4 mx-2 mt-3 justify-center">
            <VCol cols="12" md="10">
              <VRow>
                <VCol cols="12" md="12">
                  <VRow class="mb-2" align="center">
                    <VCol cols="12" md="12" class="py-0 mb-2">
                      <label>
                        สถานะ :
                        <small class="text-error">*</small>
                      </label>
                    </VCol>
                    <VCol cols="12" md="12" class="py-0">
                      <VAutocomplete
                        v-model="form.isActive"
                        density="comfortable"
                        placeholder="ระบุสถานะ"
                        item-title="name"
                        item-value="id"
                        class="no-select"
                        :rules="[booleanValidator]"
                        :items="listStatus"
                      />
                    </VCol>
                  </VRow>
                </VCol>

                <VCol cols="12" md="12">
                  <VRow class="mb-2" align="center">
                    <VCol cols="12" md="12" class="py-0 mb-2">
                      <label>
                        ค่าเริ่มต้น :
                        <small class="text-error">*</small>
                      </label>
                    </VCol>
                    <VCol cols="12" md="12" class="py-0">
                      <VAutocomplete
                        v-model="form.isDefault"
                        density="comfortable"
                        placeholder="ระบุสถานะ"
                        item-title="name"
                        item-value="id"
                        class="no-select"
                        :rules="[booleanValidator]"
                        :items="listDefault"
                      />
                    </VCol>
                  </VRow>
                </VCol>
              </VRow>
            </VCol>
          </VRow>

          <div class="d-flex flex-wrap justify-center mt-5">
            <div class="demo-space-x">
              <VBtn
                color="blue-600"
                rounded="xl"
                prepend-icon="mdi-content-save"
                @click="onFormSubmit"
              >
                บันทึก
              </VBtn>
              <VBtn
                color="error-300"
                rounded="xl"
                prepend-icon="mdi-close-circle"
                @click="closeDialog"
              >
                ยกเลิก
              </VBtn>
            </div>
          </div>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style scoped>
.no-select {
  user-select: none;
}
</style>
