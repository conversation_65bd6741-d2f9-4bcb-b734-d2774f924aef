<template>
  <div id="mapContainer2" :style="{ height: props.height }"></div>
</template>

<script setup lang="ts">
import L, { Icon, Marker } from 'leaflet'
import 'leaflet/dist/leaflet.css'
import { onBeforeUnmount, onMounted, ref, watch, nextTick } from 'vue'

const map = ref<L.Map | null>(null)
let activePopup: L.Popup | null = null
let activeMarker: Marker | null = null

const props = defineProps({
  height: {
    type: String,
    default: '650px'
  },
  DataMapGecc: {
    type: Array,
    required: true,
    default: () => []
  },
  hasCondition: {
    type: Boolean,
    default: false
  }
})

const markers = ref([])

const createPopupContent = marker => {
  if (!marker || !Array.isArray(marker.detail) || marker.detail.length === 0) {
    console.warn('Invalid marker data:', marker)
    return `<div>ไม่มีข้อมูลเพิ่มเติม</div>`
  }

  if (
    marker.detail &&
    marker.detail.some(detail => detail.year && detail.certificateName && detail.status)
  ) {
    const latestDetail = marker.detail.reduce((latest, detail) => {
      return detail.year > latest.year ? detail : latest
    }, marker.detail[0]) // หา year ล่าสุดและข้อมูลที่เกี่ยวข้อง

    const certName = latestDetail?.certificateName || '' // ป้องกัน undefined

    const logoUrl = (() => {
      if (certName.includes('พื้นฐาน')) {
        return new URL('@images/report/imageBalance.png', import.meta.url).href
      } else if (certName.includes('ก้าวหน้า')) {
        return new URL('@images/report/imageAdvance.png', import.meta.url).href
      } else if (certName.includes('เป็นเลิศ')) {
        return new URL('@images/report/imageExcellent.png', import.meta.url).href
      }
      return new URL('@images/report/imageBalance.png', import.meta.url).href // โลโก้เริ่มต้นหากไม่มีระดับตรง
    })()

    let levelMessage = latestDetail.certificateName || ''

    // ถ้าเป็นระดับก้าวหน้า, แสดงเป็น "ระดับก้าวหน้า"
    if (levelMessage.includes('ก้าวหน้า')) {
      levelMessage = 'ระดับก้าวหน้า'
    } else if (levelMessage.includes('พื้นฐาน')) {
      levelMessage = 'ระดับพื้นฐาน'
    } else if (levelMessage.includes('เป็นเลิศ')) {
      levelMessage = 'ระดับเป็นเลิศ'
    }

    return `<div>
    <VRow class="d-flex align-center justify-center" style="gap: 20px; flex-wrap: wrap;"
    >
    <button id="closePopupBtn" class="close-btn" style="position: absolute; top: 10px; right: 10px; background: none; border: none; font-size: 18px; cursor: pointer;">&times;</button>
      <!-- คอลัมน์ฝั่งซ้าย -->
      <VCol cols="6" class="v-col col-12 md-6 text-center">
        <img
          src="${logoUrl}"
          alt="โลโก้ GECC"
          style="width: 130px; height: auto; margin-bottom: 20px;"
        />
        <h5>${levelMessage}</h5>
      </VCol>

      <!-- คอลัมน์ฝั่งขวา -->
      <VCol cols="6" class="text-left">
        <h3 style="font-size: 18px; text-align: center; margin-bottom: 10px;">${
          marker.govermentName
        }</h3>
        <h4 style="font-size: 14px; text-align: center; color: #555;">ข้อมูลการสมัครขอรับการรับรองมาตรฐาน GECC</h4>
        <ul style="list-style: none; padding: 0 0 0 20px; margin: 10px 0;">
        ${marker.detail
          .map(detail => {
            let logoUrl = ''
            const certName = detail?.certificateName || '' // ป้องกัน undefined

            if (certName.includes('พื้นฐาน')) {
              logoUrl = new URL('@images/report/Balance.png', import.meta.url).href
            } else if (certName.includes('ก้าวหน้า')) {
              logoUrl = new URL('@images/report/Advance.png', import.meta.url).href
            } else if (certName.includes('เป็นเลิศ')) {
              logoUrl = new URL('@images/report/Excellent.png', import.meta.url).href
            } else {
              logoUrl = new URL('@images/report/LogoRed.png', import.meta.url).href
            }

            return `
            <li style="display: flex; align-items: center; margin-bottom: 5px;">
              <span style="font-size: 14px;">ปี ${detail.year} ${detail.status} ${detail.certificateName}</span>
              <img src="${logoUrl}" alt="icon" style="width: 20px; height: 20px; margin-right: 10px;" />
            </li>`
          })
          .join('')}
      </ul>
      </VCol>
    </VRow>
  </div>
  `
  } else {
    return `<div>
    <VRow class="d-flex align-center justify-center" style="gap: 20px; flex-wrap: wrap;"
    >
    <button id="closePopupBtn" class="close-btn" style="position: absolute; top: 10px; right: 10px; background: none; border: none; font-size: 18px; cursor: pointer;">&times;</button>
      <!-- คอลัมน์ฝั่งซ้าย -->
      <VCol cols="6" class="v-col col-12 md-6 text-center">
      </VCol>

      <!-- คอลัมน์ฝั่งขวา -->
      <VCol cols="6" class="text-left set_width">
        <h3 style="font-size: 18px; text-align: center; margin-bottom: 10px;">${marker.govermentName}</h3>
        <h4 style="font-size: 14px; text-align: center; color: #555;">ข้อมูลการสมัครขอรับการรับรองมาตรฐาน GECC</h4>
        <ul style="list-style: none; padding: 0 0 0 20px; margin: 10px 0;">
      </ul>
      </VCol>
    </VRow>
  </div>`
  }
}

const markersLayer = L.layerGroup() // เก็บ layer ของ marker ทั้งหมด

const initializeMap = data => {
  if (
    !markers.value ||
    !Array.isArray(markers.value) ||
    markers.value.length === 0 ||
    (typeof data === 'object' && Object.keys(data).length === 0)
  ) {
    console.warn('No valid marker data available. Clearing the map...')

    return // ออกจากฟังก์ชัน ไม่ต้องดำเนินการต่อ
  }

  if (map.value) {
    map.value.remove() // ลบ instance เดิม
    map.value = null
  }

  map.value = L.map('mapContainer2').setView([13.7248785, 100.4683014], 6)

  L.tileLayer('http://{s}.tile.osm.org/{z}/{x}/{y}.png', {
    attribution: '&copy; <a href="http://osm.org/copyright">OpenStreetMap</a> contributors'
  }).addTo(map.value)

  if (map.value.markersLayer) {
    map.value.removeLayer(map.value.markersLayer)
    map.value.markersLayer = null
    console.log('remove markers : ' + map.value.markersLayer)
  }
  map.value.markersLayer = L.featureGroup().addTo(map.value)

  const defaultIcons = {
    basic: L.icon({
      iconUrl: new URL('@images/report/maker01.png', import.meta.url).href,
      iconSize: [30, 40],
      iconAnchor: [19, 38]
    }),
    advance: L.icon({
      iconUrl: new URL('@images/report/maker03.png', import.meta.url).href,
      iconSize: [30, 40],
      iconAnchor: [19, 38]
    }),
    excellent: L.icon({
      iconUrl: new URL('@images/report/maker02.png', import.meta.url).href,
      iconSize: [30, 40],
      iconAnchor: [19, 38]
    })
  }

  markers.value.forEach(marker => {
    if (marker?.lat == null || marker?.lng == null || isNaN(marker.lat) || isNaN(marker.lng)) {
      console.warn('Invalid marker coordinates:', marker)
      return // ข้าม marker ที่ไม่มีพิกัดที่ถูกต้อง
    }

    // ตรวจสอบข้อมูล detail
    if (!marker?.detail || !Array.isArray(marker.detail) || marker.detail.length === 0) {
      console.warn('Marker detail is missing or invalid:', marker)
      return // ข้าม marker ที่ไม่มีข้อมูล detail
    }

    const latestDetail = marker.detail.reduce(
      (latest, detail) => (detail.year > latest.year ? detail : latest),
      marker.detail[0]
    )

    if (latestDetail?.year == null || !latestDetail?.status || !latestDetail?.certificateName) {
      console.warn('Marker has missing required detail fields:', marker)
      return // ข้าม marker ที่ข้อมูลไม่ครบ
    }

    const icon = (() => {
      if (!marker.detail || marker.detail.length === 0) {
        return defaultIcons.basic // กำหนดค่าเริ่มต้นเมื่อไม่มีข้อมูล
      }

      const certName = latestDetail?.certificateName || ''

      if (certName.includes('ระดับพื้นฐาน')) {
        return defaultIcons.basic
      } else if (certName.includes('ระดับก้าวหน้า')) {
        return defaultIcons.advance
      } else if (certName.includes('ระดับเป็นเลิศ')) {
        return defaultIcons.excellent
      }

      return defaultIcons.basic // fallback เป็นไอคอนพื้นฐาน
    })()

    const markerInstance = L.marker([marker.lat, marker.lng], { icon }).addTo(
      map.value.markersLayer
    )

    const popupContent = createPopupContent(marker)
    const popup = L.popup({
      maxWidth: 500,
      className: 'custom-popup',
      closeButton: false,
      autoClose: false
    }).setContent(popupContent)

    markerInstance.on('click', e => {
      if (activePopup) {
        map.value.removeLayer(activePopup)
      }
      activePopup = popup
      activeMarker = e.target
      popup.setLatLng(e.target.getLatLng())
      map.value.addLayer(popup)

      document.getElementById('closePopupBtn')?.addEventListener('click', () => {
        map.value!.removeLayer(popup)
        activePopup = null
        activeMarker = null
      })
    })

    markerInstance.on('mouseover', e => {
      e.target.openTooltip()
    })

    markerInstance.on('mouseout', e => {
      if (!activePopup || activePopup !== popup) {
        e.target.closeTooltip()
      }
    })
  })

  map.value.on('click', e => {
    if (activePopup && !activeMarker?.getLatLng().equals(e.latlng)) {
      map.value!.removeLayer(activePopup)
      activePopup = null
      activeMarker = null
    }
  })
}

onMounted(() => {
  if (props.DataMapGecc.length > 0) {
    initializeMap(props.DataMapGecc)
  }
})

watch(
  () => props.DataMapGecc,
  newValue => {
    if (Array.isArray(newValue) && newValue.length > 0) {
      console.log('New DataMapGecc received:', newValue)

      const validMarkers = newValue
        .filter(item => item.lat && item.lng)
        .map(item => ({
          lat: Number(item.lat),
          lng: Number(item.lng),
          govermentName: item.govermentName,
          detail: item.detail,
          condition: item.condition || false
        }))

      if (validMarkers.length > 0) {
        markers.value = validMarkers
        initializeMap(validMarkers)
      } else {
        markers.value = []
        if (map.value && map.value.markersLayer) {
          map.value.markersLayer.clearLayers()
        }
      }
    } else {
      // console.warn("DataMapGecc is invalid or empty:", newValue);
      markers.value = []
    }
  },
  { immediate: true }
)

onBeforeUnmount(() => {
  if (map.value) {
    map.value.remove()
    map.value = null
  }
})
</script>

<style scoped>
.ms-2 {
  margin-inline-start: 8px;
}

#mapContainer {
  block-size: 80vh;
  inline-size: 80vw;
}

.custom-tooltip .leaflet-tooltip-content,
.custom-popup .leaflet-popup-content-wrapper {
  padding: 5px;
  border: 1px solid #ccc;
  border-radius: 5px;
  background-color: white;
}

.custom-tooltip {
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 40%);
}

.custom-tooltip .leaflet-tooltip-tip {
  display: none;
}

.custom-popup .leaflet-popup-tip-container {
  display: none;
}

.close-btn {
  display: none;
}

.popup-content:hover .close-btn {
  display: block;
}

.home {
  padding: 20px;
  margin-block: 0;
  margin-inline: auto;
  max-inline-size: 800px;
  text-align: center;
}

h1 {
  color: #42b983;
  font-size: 2.5rem;
}

p {
  color: #35495e;
  font-size: 1.2rem;
  margin-block-end: 20px;
}

button {
  border: none;
  border-radius: 5px;
  background-color: #42b983;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  padding-block: 10px;
  padding-inline: 20px;
}

button:hover {
  background-color: #358a6b;
}
</style>
