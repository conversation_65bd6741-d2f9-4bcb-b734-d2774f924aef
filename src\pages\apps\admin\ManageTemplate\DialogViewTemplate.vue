<script lang="ts" setup>
import { computed, reactive, ref, toRaw, watch } from 'vue'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import type { FormTemplate } from '@interfaces/FormInterface'
import type { IGetSystemTemplateByIdRes } from '@interfaces/UserManagementInterface'

// Props and Emits
const props = defineProps({
  modelValue: Boolean,
  isView: Boolean,
  viewId: String
})

const emit = defineEmits(['update:modelValue', 'update'])

const callAxios = useAxios()
const Swal = useSwal()
const refVForm: Ref<any> = ref(null)
const viewId = computed(() => props.viewId)

// จัดการรูปภาพ
const accountDataLocal = ref({
  avatarImg: ''
})

const baseUrlImg = localStorage.baseUrlImg || '' // ดึง base URL ของรูปภาพจาก localStorage

const form = reactive<FormTemplate>({
  imageFiles: null as File | null,
  id: '',
  templateId: '',
  name: '',
  isActive: false,
  isDefault: false,
  color: '',
  pathName: '',
  templateName: '',
  templateColor: ''
})

// Default Form Values for Reset
const formDefault = structuredClone(toRaw(form))

const resetForm = () => {
  Object.assign(form, structuredClone(formDefault))
  accountDataLocal.value.avatarImg = '' // รีเซ็ต avatarImg
}

const closeDialog = async () => {
  if (props.isView) {
    resetForm()
    isDialogVisible.value = false

    return
  }
  const response = await Swal.ApproveCancel()
  if (response) {
    resetForm()
    isDialogVisible.value = false
  }
}

const ViewForm = async () => {
  try {
    const response = await callAxios.RequestGetById(
      '/UserManagement/GetSystemTemplateById',
      `?TemplateId=${viewId.value}`
    )

    if (response.status === 200) {
      resetForm()

      const templateData: IGetSystemTemplateByIdRes = response.data.response

      form.templateId = templateData.templateId
      form.templateName = templateData.templateName
      form.templateColor = templateData.templateColor
      form.isActive = templateData.isActive
      form.isDefault = templateData.isDefault
      form.pathName = templateData.pathName

      // เพิ่มการเซ็ต URL รูปภาพ
      accountDataLocal.value.avatarImg = `${baseUrlImg}${form.pathName}`
    }
  } catch (error) {
    Swal.ViewFail()
  }
}

const isDialogVisible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value)
})

watch(
  () => isDialogVisible.value,
  async newVal => {
    if (newVal && props.isView) await ViewForm()
  }
)

const setTitle = computed(() => {
  return form.templateName
})
</script>

<template>
  <VDialog
    v-if="props.isView"
    v-model="isDialogVisible"
    persistent
    class="v-dialog-sm"
    no-click-animation
    scrollable
  >
    <VCard :title="setTitle">
      <VCardText>
        <VRow class="mx-2 mt-1 justify-center">
          <VCol cols="12" md="10">
            <VRow>
              <VCol cols="12" md="12">
                <VRow class="mb-1" align="center">
                  <VCol cols="12" md="12" class="py-0">
                    <!-- แสดงรูปภาพ -->
                    <VImg
                      v-if="accountDataLocal.avatarImg"
                      :height="200"
                      :src="accountDataLocal.avatarImg"
                    />
                  </VCol>
                </VRow>
              </VCol>
            </VRow>
          </VCol>
        </VRow>
      </VCardText>

      <div class="d-flex flex-wrap justify-center mb-5">
        <div class="demo-space-x">
          <VBtn color="grey-800" rounded="xl" prepend-icon="mdi-close" @click="closeDialog">
            ปิด
          </VBtn>
        </div>
      </div>
    </VCard>
  </VDialog>
</template>

<style scoped>
.no-select {
  user-select: none;
}
</style>
