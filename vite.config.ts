import { fileURLToPath } from 'node:url'
import { createRequire } from 'node:module'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { defineConfig } from 'vite'
import Pages from 'vite-plugin-pages'
import Layouts from 'vite-plugin-vue-layouts'
import vuetify from 'vite-plugin-vuetify'

// Ensure compatibility with ESM modules

const require = createRequire(import.meta.url)

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueJsx(),

    // https://github.com/vuetifyjs/vuetify-loader/tree/next/packages/vite-plugin
    vuetify({
      styles: {
        configFile: 'src/styles/variables/_vuetify.scss'
      }
    }),
    Pages({
      dirs: ['./src/pages'],
      onRoutesGenerated: routes => [
        // Email filter
        ...routes
      ]
    }),
    Layouts({
      layoutsDirs: './src/layouts/'
    }),
    Components({
      dirs: ['src/@core/components', 'src/views/demos', 'src/components'],
      dts: true
    }),
    AutoImport({
      eslintrc: {
        enabled: true,
        filepath: './.eslintrc-auto-import.json'
      },
      imports: ['vue', 'vue-router', '@vueuse/core', '@vueuse/math', 'pinia'],
      vueTemplate: true
    })
  ],
  define: { 'process.env': {} },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '@themeConfig': fileURLToPath(new URL('./themeConfig.ts', import.meta.url)),
      '@core': fileURLToPath(new URL('./src/@core', import.meta.url)),
      '@layouts': fileURLToPath(new URL('./src/@layouts', import.meta.url)),
      '@images': fileURLToPath(new URL('./src/assets/images/', import.meta.url)),
      '@public': fileURLToPath(new URL('./src/assets/public/', import.meta.url)),
      '@styles': fileURLToPath(new URL('./src/styles/', import.meta.url)),
      '@configured-variables': fileURLToPath(
        new URL('./src/styles/variables/_template.scss', import.meta.url)
      ),
      '@validators': fileURLToPath(new URL('./src/@core/utils/validators', import.meta.url)),
      apexcharts: fileURLToPath(new URL('node_modules/apexcharts-clevision', import.meta.url)),
      '@utils': fileURLToPath(new URL('./src/plugins/utils', import.meta.url))
    }
  },
  build: {
    chunkSizeWarningLimit: 5000
  },
  optimizeDeps: {
    exclude: ['vuetify'],
    include: ['vue', 'vue-router', 'pinia', '@vueuse/core', 'axios', 'notiflix'],
    entries: ['./src/**/*.vue', './src/**/*.ts', './src/**/*.js', './index.html'],
    force: true
  },
  server: {
    hmr: {
      overlay: false
    },
    watch: {
      usePolling: true
    }
  }
})
