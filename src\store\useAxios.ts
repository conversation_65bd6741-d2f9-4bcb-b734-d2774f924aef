import { AxiosError } from 'axios'
import type { AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'

import { defineStore } from 'pinia'
import router from '@/router'
import { useSwal } from '@/store/useSwal'

export const useAxios = defineStore('useAxios', () => {
  const Swal = useSwal()
  const axios = inject<AxiosInstance>('axios')!

  async function RequestPost<T = any, D = any>(
    endpoint: string,
    data?: any,
    config?: AxiosRequestConfig<D>
  ): Promise<AxiosResponse<T>> {
    Swal.Loading()

    return new Promise((resolve, reject) => {
      axios
        .post<T, AxiosResponse<T>, D>(endpoint, data, config)
        .then(response => {
          if (response.status == 200) {
            Swal.AddSuccess()
            resolve(response)
          } else {
            resolve(response)
          }
        })
        .catch((e: AxiosError<any, any>) => {
          if (!e.response) {
            Swal.AddFail()
            reject(e)
          } else if (e.response?.status == 400) {
            const message =
              typeof e.response?.data === 'string'
                ? e.response?.data
                : JSON.stringify(e.response?.data)

            Swal.isRequestLost(message)
            reject(e)
          } else if (e.response?.status == 401) {
            router.push('/not-authorized')
            reject(e)
          } else if (e.response?.status == 404) {
            if ((e.response?.data as { message: string }).message) {
              Swal.AddConditionFailText((e.response?.data as { message: string }).message)
            } else {
              Swal.AddConditionFail()
            }
            reject(e)
          } else if (e.response?.status == 405) {
            Swal.isUsed()
            reject(e)
          } else if (e.response?.status == 409) {
            Swal.AddConditionFailText((e.response?.data as { message: string }).message)
            reject(e)
          } else if (e.response?.status == 428) {
            if ((e.response?.data as { message: string }).message) {
              Swal.AddConditionFailText((e.response?.data as { message: string }).message)
            } else {
              Swal.AddConditionFail()
            }
            reject(e)
          } else {
            Swal.AddFail()
          }
        })
    })
  }

  async function RequestPostNoAlert<T = any, D = any>(
    endpoint: string,
    data?: any,
    config?: AxiosRequestConfig<D>
  ): Promise<AxiosResponse<T>> {
    // Swal.Loading();

    return new Promise((resolve, reject) => {
      axios
        .post<T, AxiosResponse<T>, D>(endpoint, data, config)
        .then((response: any) => {
          if (response.status == 200) {
            // .Swal.AddSuccess();
            resolve(response)
          } else {
            resolve(response)
          }
        })
        .catch((e: AxiosError<any, any>) => {
          if (!e.response) {
            Swal.AddFail()
            reject(e)
          } else if (e.response?.status == 400) {
            const message =
              typeof e.response?.data === 'string'
                ? e.response?.data
                : JSON.stringify(e.response?.data)

            Swal.isRequestLost(message)
            reject(e)
          } else if (e.response?.status == 401) {
            router.push('/not-authorized')
            reject(e)
          } else if (e.response?.status == 404) {
            Swal.isNotFound()
            reject(e)
          } else if (e.response?.status == 405) {
            Swal.isUsed()
            reject(e)
          } else if (e.response?.status == 409) {
            Swal.isDuplicate()
            reject(e)
          } else if (e.response?.status == 428) {
            if ((e.response?.data as { message: string }).message) {
              Swal.AddConditionFailText((e.response?.data as { message: string }).message)
            } else {
              Swal.AddConditionFail()
            }
            reject(e)
          } else {
            Swal.AddFail()
          }
        })
    })
  }

  async function RequestPostNoAlertAppeal<T = any, D = any>(
    endpoint: string,
    data?: any,
    config?: AxiosRequestConfig<D>
  ): Promise<AxiosResponse<T>> {
    // Swal.Loading();

    return new Promise((resolve, reject) => {
      axios
        .post<T, AxiosResponse<T>, D>(endpoint, data, config)
        .then((response: any) => {
          if (response.status == 200) {
            // .Swal.AddSuccess();
            resolve(response)
          } else {
            resolve(response)
          }
        })
        .catch((e: AxiosError<any, any>) => {
          if (!e.response) {
            Swal.AddFail()
            reject(e)
          } else if (e.response?.status == 400) {
            const message =
              typeof e.response?.data === 'string'
                ? e.response?.data
                : JSON.stringify(e.response?.data)

            Swal.isRequestLost(message)
            reject(e)
          } else if (e.response?.status == 401) {
            router.push('/not-authorized')
            reject(e)
          } else if (e.response?.status == 404) {
            Swal.isNotFound()
            reject(e)
          } else if (e.response?.status == 405) {
            Swal.isUsed()
            reject(e)
          } else if (e.response?.status == 409) {
            resolve(e.response)
          } else if (e.response?.status == 428) {
            if ((e.response?.data as { message: string }).message) {
              Swal.AddConditionFailText((e.response?.data as { message: string }).message)
            } else {
              Swal.AddConditionFail()
            }
            reject(e)
          } else {
            Swal.AddFail()
          }
        })
    })
  }

  async function RequestPostGetData<T = any, D = any>(
    endpoint: string,
    data?: any,
    config?: AxiosRequestConfig<D>
  ): Promise<AxiosResponse<T>> {
    Swal.Loading()

    return new Promise((resolve, reject) => {
      axios
        .post<T, AxiosResponse<T>, D>(endpoint, data, config)
        .then(response => {
          if (response.status == 200) resolve(response)
          else resolve(response)
        })
        .catch((e: AxiosError<any, any>) => {
          if (!e.response) {
            Swal.isNotFound()
            reject(e)
          } else if (e.response?.status == 400) {
            const message =
              typeof e.response?.data === 'string'
                ? e.response?.data
                : JSON.stringify(e.response?.data)

            Swal.isRequestLost(message)
            reject(e)
          } else if (e.response?.status == 401) {
            router.push('/not-authorized')
            reject(e)
          } else if (e.response?.status == 404) {
            Swal.isNotFound()
            reject(e)
          } else if (e.response?.status == 405) {
            Swal.isUsed()
            reject(e)
          } else if (e.response?.status == 409) {
            Swal.isDuplicate()
            reject(e)
          } else if (e.response?.status == 428) {
            if ((e.response?.data as { message: string }).message) {
              Swal.AddConditionFailText((e.response?.data as { message: string }).message)
            } else {
              Swal.AddConditionFail()
            }
            reject(e)
          } else {
            Swal.AddFail()
          }
        })
    })
  }

  // eslint-disable-next-line sonarjs/cognitive-complexity, @typescript-eslint/no-explicit-any
  async function RequestPostForm<T = any, D = any>(
    endpoint: string,
    body: any,
    config: AxiosRequestConfig<D> = {}
  ) {
    Swal.Loading()

    try {
      const response = await axios.postForm<T, AxiosResponse<T>, D>(endpoint, body, config)

      if (response?.status === 200) {
        Swal.AddSuccess()

        return response
      }
    } catch (e) {
      if (e instanceof AxiosError) {
        if (e.response?.status === 400) {
          const message =
            typeof e.response?.data === 'string'
              ? e.response?.data
              : JSON.stringify(e.response?.data)

          Swal.isRequestLost(message)
          throw e
        } else if (e.response?.status === 401) {
          router.push('/not-authorized')
          throw e
        } else if (e.response?.status === 404) {
          Swal.isNotFound()
          throw e
        } else if (e.response?.status === 405) {
          Swal.isUsed()
          throw e
        } else if (e.response?.status === 409) {
          Swal.isDuplicate()
          throw e
        } else if (e.response?.status === 413) {
          if (e.response?.data.message) Swal.AddConditionFailText(e.response?.data.message)
          else Swal.AddConditionFail()
          throw e.response
        } else if (e.response?.status === 428) {
          if (e.response?.data.message) Swal.AddConditionFailText(e.response?.data.message)
          else Swal.AddConditionFail()
          throw e
        }
      }
    }

    Swal.AddFail()
  }

  async function RequestUpload(
    endpoint: string,
    request: FormData,
    config: AxiosRequestConfig = {}
  ): Promise<AxiosResponse> {
    return new Promise((resolve, reject) => {
      Swal.Loading()
      axios
        .post(endpoint, request, {
          ...config,
          headers: {
            ...(config.headers || {}),
            'Content-Type': 'multipart/form-data'
          }
        })
        .then((response?: AxiosResponse) => {
          if (response?.status == 200) {
            // this.Swal.AddSuccess()
            Swal.AddFileSuccess()
            resolve(response)
          }
        })
        .catch((e: AxiosError<any, any>) => {
          if (e.response?.status == 400) {
            Swal.isRequestLost(e.response?.data)
            reject(e)
          } else if (e.response?.status == 401) {
            router.push('/not-authorized')
            reject(e)
          } else if (e.response?.status == 404) {
            Swal.isNotFound()
            reject(e)
          } else if (e.response?.status == 405) {
            Swal.isUsed()
            reject(e)
          } else if (e.response?.status == 409) {
            Swal.isDuplicate()
            reject(e)
          } else if (e.response?.status == 413) {
            if (e.response?.data.message) Swal.AddConditionFailText(e.response?.data.message)
            else Swal.AddConditionFail()
            reject(e.response)
          } else if (e.response?.status == 428) {
            if (e.response?.data.message) Swal.AddConditionFailText(e.response?.data.message)
            else Swal.AddConditionFail()
            reject(e)
          } else {
            Swal.AddFail()
          }
        })
    })
  }

  async function RequestPutUpload(
    endpoint: string,
    request: FormData,
    config: AxiosRequestConfig = {}
  ): Promise<AxiosResponse> {
    return new Promise((resolve, reject) => {
      Swal.Loading()
      axios
        .put(endpoint, request, {
          ...config,
          headers: {
            ...(config.headers || {}),
            'Content-Type': 'multipart/form-data'
          }
        })
        .then((response?: AxiosResponse) => {
          if (response?.status == 200) {
            Swal.EditSuccess()
            resolve(response)
          }
        })
        .catch((e: AxiosError<any, any>) => {
          console.log(e)
          if (e.response?.status == 400) {
            Swal.isRequestLost(e.response?.data)
            reject(e)
          } else if (e.response?.status == 401) {
            router.push('/not-authorized')
            reject(e)
          } else if (e.response?.status == 404) {
            Swal.isNotFound()
            reject(e)
          } else if (e.response?.status == 405) {
            Swal.isUsed()
            reject(e)
          } else if (e.response?.status == 409) {
            Swal.isDuplicate()
            reject(e)
          } else if (e.response?.status == 413) {
            if (e.response?.data.message) Swal.AddConditionFailText(e.response?.data.message)
            else Swal.EditConditionFail()
            reject(e.response)
          } else if (e.response?.status == 428) {
            if (e.response?.data.message) Swal.AddConditionFailText(e.response?.data.message)
            else Swal.EditConditionFail()
            reject(e)
          } else {
            Swal.EditFail()
          }
        })
    })
  }

  async function RequestPut(
    endpoint: string,
    request: any,
    config: AxiosRequestConfig = {}
  ): Promise<AxiosResponse> {
    return new Promise((resolve, reject) => {
      Swal.Loading()
      axios
        .put(endpoint, request, config)
        .then((response?: AxiosResponse) => {
          if (response?.status == 200) {
            if (response?.data.message) Swal.ConditionSuccessText(response?.data.message)
            else Swal.EditSuccess()
            resolve(response)
          }
        })
        .catch((e: AxiosError<any, any>) => {
          if (e.response?.status == 400) {
            console.log(e)
            Swal.isRequestLost(e.response?.data.title)
            reject(e)
          } else if (e.response?.status == 401) {
            router.push('/not-authorized')
            reject(e)
          } else if (e.response?.status == 404) {
            Swal.isNotFound()
            reject(e)
          } else if (e.response?.status == 405) {
            Swal.isUsed()
            reject(e)
          } else if (e.response?.status == 409) {
            Swal.isDuplicate()
            reject(e)
          } else if (e.response?.status == 428) {
            if (e.response?.data.message) Swal.AddConditionFailText(e.response?.data.message)
            else Swal.AddConditionFail()
            reject(e)
          } else {
            Swal.EditFail()
          }
        })
    })
  }
  async function RequestPatch(
    endpoint: string,
    request: any,
    config: AxiosRequestConfig = {}
  ): Promise<AxiosResponse> {
    return new Promise((resolve, reject) => {
      Swal.Loading()
      axios
        .patch(endpoint, request, config)
        .then((response?: AxiosResponse) => {
          if (response?.status == 200) {
            if (response?.data.message) Swal.ConditionSuccessText(response?.data.message)
            else Swal.EditSuccess()
            resolve(response)
          }
        })
        .catch((e: AxiosError<any, any>) => {
          if (e.response?.status == 400) {
            console.log(e)
            Swal.isRequestLost(e.response?.data.title)
            reject(e)
          } else if (e.response?.status == 401) {
            router.push('/not-authorized')
            reject(e)
          } else if (e.response?.status == 404) {
            Swal.isNotFound()
            reject(e)
          } else if (e.response?.status == 405) {
            Swal.isUsed()
            reject(e)
          } else if (e.response?.status == 409) {
            Swal.isDuplicate()
            reject(e)
          } else if (e.response?.status == 428) {
            if (e.response?.data.message) Swal.AddConditionFailText(e.response?.data.message)
            else Swal.AddConditionFail()
            reject(e)
          } else {
            Swal.EditFail()
          }
        })
    })
  }
  async function RequestPutNoAlert(
    endpoint: string,
    request: any,
    config: AxiosRequestConfig = {}
  ): Promise<AxiosResponse> {
    return new Promise((resolve, reject) => {
      Swal.Loading()
      axios
        .put(endpoint, request, config)
        .then((response?: AxiosResponse) => {
          if (response?.status == 200) {
            if (response?.data.message)
              //   this.Swal.ConditionSuccessText(response?.data.message);
              // else this.Swal.EditSuccess();
              resolve(response)
          }
        })
        .catch((e: AxiosError<any, any>) => {
          if (e.response?.status == 400) {
            console.log(e)
            Swal.isRequestLost(e.response?.data.title)
            reject(e)
          } else if (e.response?.status == 401) {
            router.push('/not-authorized')
            reject(e)
          } else if (e.response?.status == 404) {
            Swal.isNotFound()
            reject(e)
          } else if (e.response?.status == 405) {
            Swal.isUsed()
            reject(e)
          } else if (e.response?.status == 409) {
            Swal.isDuplicate()
            reject(e)
          } else if (e.response?.status == 428) {
            if (e.response?.data.message) Swal.AddConditionFailText(e.response?.data.message)
            else Swal.AddConditionFail()
            reject(e)
          } else {
            Swal.EditFail()
          }
        })
    })
  }

  async function RequestGet<T = any, R = AxiosResponse<T>, D = any>(
    endpoint: string,
    isLoading = false,
    config: AxiosRequestConfig<D> = {}
  ): Promise<AxiosResponse> {
    return new Promise((resolve, reject) => {
      if (isLoading) Swal.Loading()

      axios
        .get<T, R, D>(endpoint, config)
        .then((response: any) => {
          if (response?.status == 200) {
            if (isLoading) Swal.close()

            resolve(response)
          } else {
            Swal.close()
          }
        })
        .catch((e: AxiosError<any, any>) => {
          Swal.close()
          if (e.response?.status == 400) {
            Swal.isRequestLost(e.response?.data.title)
            reject(e.response)
          } else if (e.response?.status == 401) {
            router.push('/not-authorized')
            reject(e.response)
          } else if (e.response?.status == 404) {
            Swal.isNotFound()
            reject(e.response)
          } else if (e.response?.status == 428) {
            Swal.AddConditionFailText(e.response?.data.message)
            reject(e.response)
          } else {
            Swal.callCatch()
          }
        })
    })
  }

  async function RequestGetById(endpoint: string, Id: any): Promise<AxiosResponse> {
    return new Promise((resolve, reject) => {
      axios
        .get(`${endpoint}/${Id}`)
        .then((response?: AxiosResponse) => {
          if (response?.status == 200) resolve(response)
        })
        .catch((e: AxiosError<any, any>) => {
          if (e.response?.status == 400) {
            Swal.isRequestLost(e.response?.data.title)
            reject(e.response)
          } else if (e.response?.status == 401) {
            router.push('/not-authorized')
            reject(e.response)
          } else if (e.response?.status == 404) {
            Swal.isNotFound()
            reject(e.response)
          } else {
            Swal.callCatch()
          }
        })
    })
  }

  async function RequestGetFile<T = any, R = AxiosResponse<T>, D = any>(
    endpoint: string,
    config: AxiosRequestConfig<D> = {}
  ): Promise<AxiosResponse> {
    return new Promise((resolve, reject) => {
      axios
        .get<T, R, D>(endpoint, { responseType: 'blob' })
        .then((response: any) => {
          // สร้าง Blob จากข้อมูลที่ได้
          const blob = new Blob([response.data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          })

          // สร้างลิงก์ดาวน์โหลด
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')

          link.href = url
          link.setAttribute('download', 'data.xlsx') // ตั้งชื่อไฟล์ที่ต้องการดาวน์โหลด
          document.body.appendChild(link)
          link.click()

          // ลบลิงก์เมื่อดาวน์โหลดเสร็จ
          link.remove()
          window.URL.revokeObjectURL(url)

          if (response?.status == 200) resolve(response)
        })
        .catch((e: AxiosError<any, any>) => {
          if (e.response?.status == 400) {
            Swal.isRequestLost(e.response?.data.title)
            reject(e.response)
          } else if (e.response?.status == 401) {
            router.push('/not-authorized')
            reject(e.response)
          } else if (e.response?.status == 404) {
            Swal.isNotFound()
            reject(e.response)
          } else if (e.response?.status == 428) {
            Swal.AddConditionFailText(e.response?.data.message)
            reject(e.response)
          } else {
            Swal.callCatch()
          }
        })
    })
  }

  return {
    Swal,
    RequestPost,
    RequestPostNoAlert,
    RequestPostNoAlertAppeal,
    RequestPostGetData,
    RequestPostForm,
    RequestUpload,
    RequestPutUpload,
    RequestPut,
    RequestPutNoAlert,
    RequestGet,
    RequestGetById,
    RequestGetFile
  }
})
