<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { VCol, VRow } from 'vuetify/lib/components/index.mjs'
import Footer from '@/layouts/components/Footer.vue'
import { useAppAbility } from '@/plugins/casl/useAppAbility'
import { useAuth } from '@/store/useAuth'
import { useAxios } from '@/store/useAxios'
import { useSwal } from '@/store/useSwal'
import bgLogin from '@images/pages/bgLogin.png'

const route = useRoute()
const router = useRouter()
const callAxios = useAxios()
const Swal = useSwal()
const callAuth = useAuth()
const AppAbility = useAppAbility()
const refVForm = ref()

// ดึงค่าจาก query string และแปลงเป็นตัวพิมพ์เล็ก
const Token = (route.query.Token as string) || ''
const Code = (route.query.Code as string) || ''
const Expire = (route.query.Expire as string) || ''

const listData = ref({})

const isDialogError = ref(false)

const errorMessage = ref('')
const errorResponseData = ref('')

const AuthenticateEmail = async () => {
  try {
    const response = await callAxios.RequestPut(
      '/Authenticate/AuthenticateEmail',
      {
        token: Token,
        code: Code,
        expire: Expire
      } // JSON payload
    )

    if (response.status === 200) {
      Swal.ConditionSuccessText('ยืนยันตัวตนสำเร็จ')
      console.log(response)

      listData.value = response.data.response // เก็บ response
    } else {
      Swal.ConditionSuccessText('ยืนยันตัวตนสำเร็จ')
    }
  } catch (error) {
    isDialogError.value = true
    errorMessage.value = error.response?.data?.message || 'เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ'

    errorResponseData.value = error.response?.data?.response
    console.log(error.response?.data?.message)
    console.log(error.response?.data?.response)

    Swal.AddConditionFailText(
      error.response?.data?.message || 'Bad request. Please check your input and try again.'
    )
  }
}

const isCardRegisterSuccess = ref(true)
const isCardRegisterAgain = ref(false)

const countdown = ref(0) // เริ่มต้นที่ 300 วินาที (5 นาที)

const startCountdown = () => {
  const interval = setInterval(() => {
    if (countdown.value > 0) countdown.value--
    else clearInterval(interval) // หยุดการนับถอยหลังเมื่อถึง 0
  }, 1000) // อัปเดตทุก ๆ 1 วินาที
}

// เฝ้าดู isCardRegisterAgain
watch(
  () => isCardRegisterAgain.value,
  newValue => {
    if (newValue) startCountdown() // เรียก startCountdown เมื่อค่าเปลี่ยนเป็น true
  }
)

// แปลงวินาทีเป็นรูปแบบ mm:ss
const formattedTime = computed(() => {
  const minutes = Math.floor(countdown.value / 60)
    .toString()
    .padStart(2, '0')

  const seconds = (countdown.value % 60).toString().padStart(2, '0')

  return `${minutes}:${seconds}`
})

const AddRegisterAgain = async () => {
  try {
    const data = {
      email: errorResponseData.value,
      password: '******' // ส่ง password ที่กำหนดไว้
    }

    const response = await callAxios.RequestPost('/Authenticate/RegisterAgain', data)

    if (response.status === 200) {
      // รีเซ็ต countdown กลับไปที่ค่าเริ่มต้น
      countdown.value = 10800 // ตั้งเวลาใหม่เป็น 3 ชั่วโมง
      startCountdown() // เริ่มการนับถอยหลังใหม่

      Swal.ConditionSuccessText(
        response.data.message || 'Successfully resent the confirmation email.'
      )
    } else {
      // Handle non-200 responses
      Swal.AddConditionFailText(response.data?.message || 'Request failed. Please try again.')
    }
  } catch (error) {
    // Handle request errors
    Swal.AddConditionFailText(
      error.response?.data?.message || 'Bad request. Please check your input and try again.'
    )
  }
}

const isRegisterSuccess = ref(true) // ค่าเริ่มต้นคือ `true`

const handleError = () => {
  isCardRegisterAgain.value = true
  isCardRegisterSuccess.value = false
  isRegisterSuccess.value = false // ตั้งค่าให้เป็น `false`
  isDialogError.value = false
}

// เรียก AuthenticateEmail เมื่อ component โหลด
onMounted(() => {
  AuthenticateEmail()
})

const goBack = async () => {
  const confirmed = await Swal.ApproveCancel()
  if (!confirmed) return
  router.push({ path: '/apps/login' })
}
</script>

<template>
  <HeaderPublic />

  <RegisterStep :is-register-again="true" :is-register-success="isRegisterSuccess" />

  <div class="auth-wrapper d-flex">
    <VContainer>
      <VRow class="pt-10">
        <VCol cols="12" justify="center">
          <VCard v-if="isCardRegisterSuccess" class="auth-card login-card pa-2 pt-7 mb-5">
            <VCardText class="text-center">
              <img src="@images/RegisterSuccess.png" alt="RegisterSuccess" />
              <h4 class="mb-3 mt-4">ลงทะเบียนเสร็จสมบูรณ์ สามารถเข้าสู่ระบบได้</h4>

              <h5>ชื่อผู้ใช้งาน : {{ listData.email }}</h5>
              <h5 class="mb-3">รหัสผ่าน : {{ listData.password }}</h5>

              <p class="text-error">
                เพื่อความปลอดภัยของข้อมูลและเพื่อป้องกันการเข้าถึงโดยไม่ได้รับอนุญาต
                โปรดเก็บรักษารหัสผ่านของท่านไว้ในที่ที่ปลอดภัยและไม่เปิดเผยต่อผู้อื่น
                ไม่แนะนำให้จดบันทึกในที่ที่สามารถเข้าถึงได้โดยง่าย
                หรือส่งรหัสผ่านผ่านช่องทางการสื่อสารที่ไม่ปลอดภัย
              </p>

              <VBtn color="blue-600" :to="{ name: 'apps-login' }">เสร็จสิ้น</VBtn>
            </VCardText>
          </VCard>

          <VCard v-if="isCardRegisterAgain" class="auth-card login-card pa-2 pt-7 mb-5">
            <VCardText class="text-center">
              <img src="@images/subscribe.png" alt="RegisterSuccess" />
              <h3 class="text-h6">
                ระบบส่งลิงก์ยืนยันไปทาง E-mail
                <span class="text-blue-200">{{ errorResponseData }}</span>
                แล้ว
                <br />
                กรุณาคลิกลิงก์ที่ได้รับเพื่อเป็นการยืนยันตัวตน
              </h3>
              <p v-if="countdown > 0">ส่งลิงก์ยืนยันซ้ำอีกครั้ง {{ formattedTime }}</p>
              <p v-else>
                ส่งลิงก์ยืนยันซ้ำอีกครั้ง
                <VBtn color="error-200" class="text-dark" type="button" @click="AddRegisterAgain">
                  ส่งรหัสยืนยัน
                  <VTooltip open-delay="500" location="top" activator="parent">
                    ส่งรหัสยืนยัน
                  </VTooltip>
                </VBtn>
              </p>
              <!-- <p>ส่งลิงก์ยืนยันซ้ำอีกครั้ง {{ formattedTime }}</p> -->

              <VBtn color="error-300 mt-4" @click="goBack">ยกเลิก</VBtn>
            </VCardText>
          </VCard>
        </VCol>

        <VCol cols="12">
          <Footer />
        </VCol>
      </VRow>
    </VContainer>

    <VImg :src="bgLogin" role="presentation" class="d-none d-md-block auth-footer-mask" />
    <div class="auth-logo" />

    <VDialog v-model="isDialogError" persistent class="v-dialog-sm">
      <VCard>
        <VCardText class="bg-white">
          <div class="text-center">
            <VIcon icon="mdi-alert-circle-outline" class="text-icon" />

            <!-- แสดงข้อความ error -->
            <p class="my-4">
              {{ errorMessage }}
              <br />
              กรุณายืนยันตัวตน
            </p>

            <VBtn color="grey-800" @click="handleError">ตกลง</VBtn>
          </div>
        </VCardText>
      </VCard>
    </VDialog>

    <GovWebsiteAccess />
  </div>
</template>

<style lang="css">
@import '@public/assets/css/style.css';
@import '@public/assets/css/skin/skin-2.css';
</style>

<style lang="css" scoped>
.v-container {
  position: relative;
  z-index: 1;
}

.auth-footer-mask {
  inset-block-end: 0% !important;
  /* เปลี่ยนค่าเมื่อหน้าจอเล็กลง */
}

.guidTooltip {
  margin-top: -20px;
  position: absolute;
}
.text-icon {
  font-size: 100px;
  color: #b21f29;
}
</style>

<style lang="scss">
@use '@core/scss/template/pages/page-auth.scss';
</style>

<style lang="scss" scoped>
.layout-blank {
  .auth-wrapper {
    min-block-size: calc(var(--vh, 1vh) * 0);

    &::before {
      content: '';
      display: block;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 50%;
      background: linear-gradient(
        180deg,
        rgb(var(--v-theme-blue-100)) 0%,
        rgba(255, 255, 255, 0) 100%
      );
      /* Example background */
      z-index: 1;
      /* Make sure it's behind other content */
    }
  }

  .auth-card {
    z-index: 1 !important;
  }
}
</style>

<route lang="yaml">
meta:
  layout: blank
  action: read
  subject: Auth
  redirectIfLoggedIn: true
</route>
