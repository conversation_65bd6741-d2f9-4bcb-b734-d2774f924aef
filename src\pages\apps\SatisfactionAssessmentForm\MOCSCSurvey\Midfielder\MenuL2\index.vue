<script setup>
import { filterMenu } from '@utils'

const inProcess = ref(true)

const breadcrumbItems = [
  {
    title: 'แบบประเมินความพึงพอใจ',
    disabled: false,
    to: '/apps/home'
  },
  {
    title: 'MOCSC Survey',
    disabled: false
  },
  {
    title: 'แบบประเมินความพึงพอใจการให้บริการศูนย์บริการประชาชน',
    disabled: false,
    active: true,
    activeClass: 'text-info'
  }
]

const ListsMenu = ref([
  {
    title: 'สร้างแบบประเมินความพึงพอใจการให้บริการศูนย์บริการประชาชน (ส่วนกลาง)',
    toRaw: 'สร้างแบบประเมินความพึงพอใจการให้บริการศูนย์บริการประชาชน (ส่วนกลาง)',
    to: {
      name: 'apps-SatisfactionAssessmentForm-MOCSCSurvey-Midfielder-ListGroupSurvey'
    },
    icon: 'mdi-note-plus',
    color: 'info-500'
  },
  {
    title: 'สร้างแบบประเมินความพึงพอใจการให้บริการศูนย์บริการประชาชน (รายบุคคล)',
    toRaw: 'สร้างแบบประเมินความพึงพอใจการให้บริการศูนย์บริการประชาชน (รายบุคคล)',
    to: {
      name: 'apps-SatisfactionAssessmentForm-MOCSCSurvey-Midfielder-ListIndividualSurvey'
    },
    icon: 'mdi-note-plus',
    color: 'blue-700'
  },
  {
    title: 'บันทึกแบบประเมินความพึงพอใจการให้บริการศูนย์บริการประชาชน (รายบุคคล)',
    toRaw: 'บันทึกแบบประเมินความพึงพอใจการให้บริการศูนย์บริการประชาชน (รายบุคคล)',
    to: {
      name: 'apps-SatisfactionAssessmentForm-MOCSCSurvey-Midfielder-ListIndividualSurvey-Submit'
    },
    icon: 'mdi-note-plus',
    color: 'primary'
  },
  {
    title: 'บันทึกแบบประเมินความพึงพอใจการให้บริการศูนย์บริการประชาชน (ส่วนกลาง)',
    toRaw: 'บันทึกแบบประเมินความพึงพอใจการให้บริการศูนย์บริการประชาชน (ส่วนกลาง)',
    to: {
      name: 'apps-SatisfactionAssessmentForm-MOCSCSurvey-Midfielder-ListGroupSurvey-Submit'
    },
    icon: 'mdi-note-plus',
    color: 'blue-700'
  },
  {
    title: 'รายงานสรุปความพึงพอใจภาพรวม',
    toRaw: 'รายงานสรุปความพึงพอใจภาพรวม',
    to: {
      name: 'apps-SatisfactionAssessmentForm-MOCSCSurvey-Dashboard-Satisfaction'
    },
    icon: 'mdi-chart-box',
    color: 'success-200'
  },
  {
    title: 'รายงานสรุปคะแนนการใช้บริการ',
    toRaw: 'รายงานสรุปคะแนนการใช้บริการ',
    to: {
      name: 'apps-Report-id',
      params: { id: 1 },
      query: { controller: 'RP_034_MOCSC_SummaryScore' }
    },
    icon: 'mdi-book-open',
    color: 'error-600'
  },
  {
    title: 'รายงานสรุปข้อคิดเห็น ข้อเสนอแนะ เพื่อปรับปรุงการให้บริการ',
    toRaw: 'รายงานสรุปข้อคิดเห็น ข้อเสนอแนะ เพื่อปรับปรุงการให้บริการ',
    to: {
      name: 'apps-Report-id',
      params: { id: 1 },
      query: { controller: 'RP_033_MOCCS_SummarySuggestion' }
    },
    icon: 'mdi-message-text',
    color: 'orange-200'
  },
  {
    title: 'รายงานสรุปการเรียกรับผลประโยชน์',
    toRaw: 'รายงานสรุปการเรียกรับผลประโยชน์',
    to: {
      name: 'apps-Report-id',
      params: { id: 1 },
      query: { controller: 'RP_035_MOCSC_SummaryReceiveBenefits' }
    },
    icon: 'mdi-message-text',
    color: 'info-500'
  },
  {
    title: 'รายงานสรุปความพึงพอใจรายบุคคล',
    toRaw: 'รายงานสรุปความพึงพอใจรายบุคคล',
    to: {
      name: 'apps-SatisfactionAssessmentForm-MOCSCSurvey-Dashboard-IndividualSatisfaction'
    },
    icon: 'mdi-message-text',
    color: 'navy'
  }
])

onMounted(async () => {
  if (ListsMenu.value) {
    ListsMenu.value = await filterMenu(ListsMenu.value)
    inProcess.value = false
  } else {
    inProcess.value = false
  }
})
</script>

<template>
  <div>
    <VBreadcrumbs :items="breadcrumbItems">
      <template #divider>
        <VIcon icon="mdi-chevron-right" />
      </template>
    </VBreadcrumbs>
    <VCard title="" class="mb-5">
      <VCardText>
        <BtnGoBack />
        <div v-if="inProcess" class="text-center">
          <VProgressCircular :size="60" color="primary" indeterminate />
        </div>
        <VRow v-else class="align-center">
          <VCol
            v-for="(item, index) in ListsMenu"
            :key="index"
            cols="12"
            md="6"
            class="align-center"
          >
            <RouterLink :to="item.to">
              <VAlert prominent variant="outlined" :color="item.color" :icon="item.icon">
                {{ item.title }}
              </VAlert>
            </RouterLink>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
  </div>
</template>
