<script setup>
import Information from '@/@core/components/Public/Information.vue'
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const contentId = ref(route.query.contentId)

watch(
  () => route.query.contentId,
  newContentId => {
    contentId.value = newContentId
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }
)
</script>

<template>
  <div>
    <HeaderPublic :logo="imgLogo" />

    <Information :contentId="contentId" />

    <FooterPublic :logo="imgLogo" />
  </div>
</template>

<style lang="scss">
@use '@public/assets/css/style.css';
@use '@public/assets/css/skin/skin-2.css';
</style>

<route lang="yaml">
meta:
  layout: blank
  action: read
</route>
