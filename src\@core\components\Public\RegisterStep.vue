<script setup lang="ts">
import { defineProps } from 'vue'

const props = defineProps({
  isRegisterAgain: {
    type: Boolean,
    required: false,
    default: false
  },
  isRegisterSuccess: {
    type: Boolean,
    required: false,
    default: false
  }
})
</script>

<template>
  <VContainer>
    <VRow class="pt-10 align-center justify-center">
      <VCol class="step-item" :class="{ 'step-active': true }">
        <div class="step-icon-wrapper">
          <img src="@/assets/images/registerStep01.png" alt="Register Step 1" class="step-icon" />
          <VIcon class="checkmark-overlay">mdi-check-circle</VIcon>
        </div>
        <div class="step-text-content">
          <span class="numb">01</span>
          <h4>ลงทะเบียน</h4>
        </div>
      </VCol>

      <VCol cols="auto" class="line-col">
        <div class="line-connector" :class="{ 'line-active': true }"></div>
      </VCol>

      <VCol class="step-item" :class="{ stepRegisterfalse: !props.isRegisterAgain }">
        <div class="step-icon-wrapper">
          <img src="@/assets/images/registerStep02.png" alt="Register Step 2" class="step-icon" />
        </div>
        <div class="step-text-content">
          <span class="numb">02</span>
          <h4>ยืนยันตัวตน</h4>
        </div>
      </VCol>

      <VCol cols="auto" class="line-col">
        <div class="line-connector" :class="{ 'line-active': props.isRegisterAgain }"></div>
      </VCol>

      <VCol class="step-item" :class="{ stepRegisterfalse: !props.isRegisterSuccess }">
        <div class="step-icon-wrapper">
          <img src="@/assets/images/registerStep03.png" alt="Register Step 3" class="step-icon" />
        </div>
        <div class="step-text-content">
          <span class="numb">03</span>
          <h4>เสร็จสมบูรณ์</h4>
        </div>
      </VCol>
    </VRow>
  </VContainer>
</template>

<style lang="css" scoped>
.v-container {
  z-index: 2 !important;
}

/* Main container for each step item (icon + text) */
.step-item {
  display: flex;
  align-items: center; /* Vertically align icon and text block */
  justify-content: center;
  gap: 15px; /* Space between icon and the text block */
}

.stepRegisterfalse {
  filter: grayscale(1);
  opacity: 0.5;
}

.step-icon-wrapper {
  position: relative;
  flex-shrink: 0;
}

.step-icon {
  display: block;
  block-size: 5rem;
  inline-size: 5rem;
}

.checkmark-overlay {
  position: absolute;
  border-radius: 50%;
  background-color: white;
  color: #583fa1;
  font-size: 24px;

  /* Adjusted to be lower, approximately aligning with the text baseline */
  inset-block-end: -15px; /* Move further down */
  inset-inline-end: -5px;
}

/* Container for the number and title */
.step-text-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start; /* Left-align the text */
}

.step-text-content h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 700;
  line-height: 1.2;
}

.step-text-content .numb {
  font-size: 14px;
  font-weight: 700;
  line-height: 1;
}

/* Text Colors */
.step-item:nth-of-type(1) .step-text-content h4,
.step-item:nth-of-type(1) .step-text-content .numb {
  color: #583fa1;
}

.step-item:nth-of-type(3) .step-text-content h4,
.step-item:nth-of-type(3) .step-text-content .numb {
  color: #846005;
}

.step-item:nth-of-type(5) .step-text-content h4,
.step-item:nth-of-type(5) .step-text-content .numb {
  color: #006e58;
}

/* Line connector styling */
.line-col {
  display: flex;
  flex-grow: 1;
  align-items: flex-end; /* Align lines to the bottom of the content in their flex container */
  justify-content: center;
  padding: 0;
  min-inline-size: 50px;
}

.line-connector {
  position: relative;
  background-color: #afafaf;
  block-size: 2px;
  inline-size: 100%;
  margin-block-end: 5px; /* Adjust margin to fine-tune vertical alignment with text */
}

.line-connector.line-active {
  background-color: #583fa1;
}

.line-connector::before,
.line-connector::after {
  position: absolute;
  border-radius: 50%;
  background-color: #afafaf;
  block-size: 10px;
  content: '';
  inline-size: 10px;

  /* Adjust top to be lower, relative to the line itself */
  inset-block-start: 50%; /* Start at center of line */
  transform: translateY(-50%); /* Adjust to move relative to line */
}

.line-connector::before {
  inset-inline-start: -5px;
}

.line-connector::after {
  inset-inline-end: -5px;
}

/* Set circle colors based on active state */
.line-connector.line-active::before {
  background-color: #583fa1;
}

.line-connector.line-active::after {
  background-color: #846005;
}

@media (max-width: 991px) {
  .step-item {
    flex-direction: column;
    gap: 8px;
  }

  .step-icon {
    block-size: 4rem;
    inline-size: 4rem;
  }

  .checkmark-overlay {
    inset-block-end: -10px; /* Adjusted for smaller icon */
  }

  .step-text-content h4 {
    font-size: 12px;
  }

  .step-text-content .numb {
    font-size: 10px;
  }

  .line-col {
    min-inline-size: 30px;
  }
}
</style>
